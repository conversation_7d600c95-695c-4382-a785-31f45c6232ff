"""
Status Synchronization Service
Keeps Case, Schedule, and Task statuses in sync
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
from django.utils import timezone
from django.db import transaction
from django.db.models import Q, Count, Case as DjangoCase, When

from .exceptions import WorkflowError, DataIntegrityError
from .workflow_automation import workflow_automation, TransitionContext

logger = logging.getLogger(__name__)


class StatusSynchronizationService:
    """
    Service for keeping related entity statuses synchronized
    """

    @staticmethod
    def sync_case_status_from_tasks(case):
        """
        Synchronize case status based on task completion

        Args:
            case: The Case instance to synchronize

        Returns:
            (changed, new_status, message) tuple
        """
        try:
            tasks = case.tasks.all()
            if not tasks.exists():
                return False, case.status, "No tasks to synchronize"

            # Count task statuses
            task_counts = tasks.aggregate(
                total=Count('id'),
                pending=Count(DjangoCase(When(status='pending', then=1))),
                in_progress=Count(DjangoCase(When(status='in_progress', then=1))),
                completed=Count(DjangoCase(When(status='completed', then=1))),
                blocked=Count(DjangoCase(When(status='blocked', then=1))),
                cancelled=Count(DjangoCase(When(status='cancelled', then=1)))
            )

            old_status = case.status
            new_status = StatusSynchronizationService._determine_case_status_from_tasks(
                task_counts, old_status
            )

            if new_status != old_status:
                context = TransitionContext(
                    reason="Auto-sync from task status changes",
                    auto_create_tasks=False,
                    notify_stakeholders=True
                )

                result, message = workflow_automation.transition_entity('case', case, new_status, context)

                if result.value == 'success':
                    logger.info(f"Case #{case.case_number} status synced from {old_status} to {new_status}")
                    return True, new_status, message
                else:
                    logger.warning(f"Failed to sync case status: {message}")
                    return False, old_status, message

            return False, old_status, "Status already synchronized"

        except Exception as e:
            logger.error(f"Error synchronizing case status from tasks: {e}", exc_info=True)
            return False, case.status, f"Sync error: {str(e)}"

    @staticmethod
    def _determine_case_status_from_tasks(task_counts: Dict[str, int], current_status: str) -> str:
        """
        Determine case status based on task counts
        """
        total = task_counts['total']
        completed = task_counts['completed']
        in_progress = task_counts['in_progress']
        blocked = task_counts['blocked']
        cancelled = task_counts['cancelled']
        pending = task_counts['pending']

        # If all tasks are completed, case should be ready for quality check or completion
        if completed == total:
            if current_status in ['in_progress', 'quality_check']:
                return 'quality_check'
            return current_status

        # If any tasks are in progress, case should be in progress
        if in_progress > 0:
            return 'in_progress'

        # If all tasks are blocked, case should be on hold
        if blocked == total and total > 0:
            return 'on_hold'

        # If all tasks are cancelled, case should be cancelled
        if cancelled == total and total > 0:
            return 'cancelled'

        # If we have pending tasks and case is not started, keep pending
        if pending > 0 and current_status == 'pending_acceptance':
            return 'pending_acceptance'

        # Default to current status
        return current_status

    @staticmethod
    def sync_schedule_status_from_items(schedule):
        """
        Synchronize schedule status based on schedule items

        Args:
            schedule: The Schedule instance to synchronize

        Returns:
            (changed, new_status, message) tuple
        """
        try:
            items = schedule.items.all() if hasattr(schedule, 'items') else []

            if not items:
                return False, schedule.status, "No schedule items to synchronize"

            # Count item statuses
            item_counts = {
                'total': len(items),
                'pending': sum(1 for item in items if item.status == 'pending'),
                'in_progress': sum(1 for item in items if item.status == 'in_progress'),
                'completed': sum(1 for item in items if item.status == 'completed'),
                'cancelled': sum(1 for item in items if item.status == 'cancelled')
            }

            old_status = schedule.status
            new_status = StatusSynchronizationService._determine_schedule_status_from_items(
                item_counts, old_status
            )

            if new_status != old_status:
                context = TransitionContext(
                    reason="Auto-sync from schedule item status changes",
                    notify_stakeholders=True
                )

                result, message = workflow_automation.transition_entity('schedule', schedule, new_status, context)

                if result.value == 'success':
                    logger.info(f"Schedule #{schedule.id} status synced from {old_status} to {new_status}")
                    return True, new_status, message
                else:
                    logger.warning(f"Failed to sync schedule status: {message}")
                    return False, old_status, message

            return False, old_status, "Status already synchronized"

        except Exception as e:
            logger.error(f"Error synchronizing schedule status: {e}", exc_info=True)
            return False, schedule.status, f"Sync error: {str(e)}"

    @staticmethod
    def _determine_schedule_status_from_items(item_counts: Dict[str, int], current_status: str) -> str:
        """
        Determine schedule status based on item counts
        """
        total = item_counts['total']
        completed = item_counts['completed']
        in_progress = item_counts['in_progress']
        cancelled = item_counts['cancelled']

        # If all items are completed, schedule is completed
        if completed == total:
            return 'completed'

        # If any items are in progress, schedule is in progress
        if in_progress > 0:
            return 'in_progress'

        # If all items are cancelled, schedule is cancelled
        if cancelled == total and total > 0:
            return 'cancelled'

        # Default to current status
        return current_status

    @staticmethod
    def sync_task_dependencies(task):
        """
        Check and update task status based on dependencies

        Args:
            task: The Task instance to check

        Returns:
            (changed, new_status, message) tuple
        """
        try:
            # Check if task has dependencies
            dependencies = getattr(task, 'dependencies', None)
            if not dependencies or not dependencies.exists():
                return False, task.status, "No dependencies to check"

            # Check if all dependencies are completed
            incomplete_deps = dependencies.exclude(status='completed')

            old_status = task.status

            if incomplete_deps.exists() and task.status not in ['blocked', 'pending']:
                # Task should be blocked due to incomplete dependencies
                new_status = 'blocked'
                task.blocking_issues = f"Waiting for {incomplete_deps.count()} dependencies"

            elif not incomplete_deps.exists() and task.status == 'blocked':
                # Dependencies are complete, unblock the task
                new_status = 'pending'
                task.blocking_issues = ""

            else:
                return False, old_status, "Dependencies already synchronized"

            if new_status != old_status:
                context = TransitionContext(
                    reason="Auto-sync from dependency status changes",
                    notify_stakeholders=True
                )

                result, message = workflow_automation.transition_entity('task', task, new_status, context)

                if result.value == 'success':
                    task.save(update_fields=['blocking_issues'])
                    logger.info(f"Task #{task.id} status synced from {old_status} to {new_status}")
                    return True, new_status, message
                else:
                    logger.warning(f"Failed to sync task dependencies: {message}")
                    return False, old_status, message

            return False, old_status, "Status unchanged"

        except Exception as e:
            logger.error(f"Error synchronizing task dependencies: {e}", exc_info=True)
            return False, task.status, f"Dependency sync error: {str(e)}"

    @staticmethod
    def sync_all_related_statuses(entity, entity_type: str):
        """
        Synchronize all related entity statuses

        Args:
            entity: The entity that changed
            entity_type: Type of entity ('case', 'task', 'schedule')

        Returns:
            List of sync results
        """
        results = []

        try:
            if entity_type == 'task':
                # Sync case status from tasks
                if hasattr(entity, 'case') and entity.case:
                    result = StatusSynchronizationService.sync_case_status_from_tasks(entity.case)
                    results.append(('case', result))

                # Sync dependent tasks
                dependent_tasks = getattr(entity, 'dependent_tasks', None)
                if dependent_tasks and dependent_tasks.exists():
                    for dep_task in dependent_tasks.all():
                        result = StatusSynchronizationService.sync_task_dependencies(dep_task)
                        results.append(('dependent_task', result))

            elif entity_type == 'case':
                # Sync schedule status if case has a schedule
                if hasattr(entity, 'schedule') and entity.schedule:
                    # Update schedule status based on case status
                    schedule = entity.schedule
                    if entity.status == 'completed' and schedule.status != 'completed':
                        context = TransitionContext(
                            reason="Case completed",
                            notify_stakeholders=True
                        )
                        result, message = workflow_automation.transition_entity('schedule', schedule, 'completed', context)
                        results.append(('schedule', (result.value == 'success', 'completed', message)))

            elif entity_type == 'schedule':
                # Sync case status if schedule has a case
                if hasattr(entity, 'case') and entity.case:
                    case = entity.case
                    if entity.status == 'completed' and case.status not in ['completed', 'delivered']:
                        context = TransitionContext(
                            reason="Schedule completed",
                            notify_stakeholders=True
                        )
                        result, message = workflow_automation.transition_entity('case', case, 'ready_to_ship', context)
                        results.append(('case', (result.value == 'success', 'ready_to_ship', message)))

        except Exception as e:
            logger.error(f"Error in sync_all_related_statuses: {e}", exc_info=True)
            results.append(('error', (False, None, f"Sync error: {str(e)}")))

        return results

    @staticmethod
    def validate_status_consistency():
        """
        Validate status consistency across all entities

        Returns:
            List of inconsistency errors
        """
        errors = []

        try:
            # Import models here to avoid circular imports
            from case.models import Case, Task
            from schedule.models import Schedule

            # Check case-task consistency
            cases_with_tasks = Case.objects.filter(tasks__isnull=False).distinct()
            for case in cases_with_tasks:
                tasks = case.tasks.all()
                if tasks.exists():
                    all_completed = all(task.status == 'completed' for task in tasks)
                    any_in_progress = any(task.status == 'in_progress' for task in tasks)

                    if all_completed and case.status == 'in_progress':
                        errors.append(f"Case #{case.case_number}: All tasks completed but case still in progress")

                    if any_in_progress and case.status == 'pending_acceptance':
                        errors.append(f"Case #{case.case_number}: Tasks in progress but case still pending")

            # Check schedule-case consistency (if schedule app exists)
            try:
                from schedule.models import Schedule
                schedules = Schedule.objects.filter(case__isnull=False)
                for schedule in schedules:
                    if schedule.case.status == 'completed' and schedule.status != 'completed':
                        errors.append(f"Schedule #{schedule.id}: Case completed but schedule not completed")
            except ImportError:
                # Schedule app not available, skip this check
                pass

        except Exception as e:
            logger.error(f"Error validating status consistency: {e}", exc_info=True)
            errors.append(f"Validation error: {str(e)}")

        return errors


# Global instance
status_sync = StatusSynchronizationService()
