{% extends "base.html" %}
{% load static %}

{% block title %}Error {{ status_code }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        Error {{ status_code }}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h5 class="alert-heading">{{ error_message }}</h5>
                        {% if error_code %}
                            <hr>
                            <p class="mb-0">
                                <small class="text-muted">Error Code: {{ error_code }}</small>
                            </p>
                        {% endif %}
                    </div>
                    
                    <div class="mt-4">
                        <h6>What you can do:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-arrow-left-circle text-primary me-2"></i>
                                <a href="javascript:history.back()" class="text-decoration-none">Go back to the previous page</a>
                            </li>
                            <li><i class="bi bi-house text-primary me-2"></i>
                                <a href="{% url 'home' %}" class="text-decoration-none">Return to home page</a>
                            </li>
                            <li><i class="bi bi-arrow-clockwise text-primary me-2"></i>
                                <a href="javascript:location.reload()" class="text-decoration-none">Refresh this page</a>
                            </li>
                        </ul>
                    </div>
                    
                    {% if status_code >= 500 %}
                    <div class="mt-4">
                        <div class="alert alert-info" role="alert">
                            <i class="bi bi-info-circle me-2"></i>
                            This appears to be a server error. Our technical team has been notified and will investigate the issue.
                        </div>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer text-muted">
                    <small>
                        If this problem persists, please contact support with error code: {{ error_code }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.alert {
    border-left: 4px solid #dc3545;
}

.list-unstyled li {
    padding: 0.25rem 0;
}

.list-unstyled li:hover {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding-left: 0.5rem;
    transition: all 0.2s ease;
}
</style>
{% endblock %}
