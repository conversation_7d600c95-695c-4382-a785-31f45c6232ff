"""
Django settings for LAB project.

Generated by 'django-admin startproject' using Django 4.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""
import os
from pathlib import Path



# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]
# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-(r!(gj^%#5+4xmz4dlw8#iru7+b+p-%rydwk-femk=0u$tp!f@'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = [
    '9aad-141-98-141-119',
    '127.0.0.1',
    'http://127.0.0.1:8000/'
    'localhost',
    '9aad-141-98-141-119.ngrok-free.app',
    'https://9aad-141-98-141-119.ngrok-free.app'
    'https://9aad-141-98-141-119.ngrok-free.app/accounts/login/?next=/',



]



######################################
CSRF_TRUSTED_ORIGINS = [
    'https://9aad-141-98-141-119.ngrok-free.app',
    'https://9aad-141-98-141-119',
    'https://9aad-141-98-141-119.ngrok-free',
    'https://9aad-141-98-141-119.ngrok-free.app/accounts/login/?next=/',

    # other trusted origins
]


















# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'accounts',



    'Dentists',
    'patients',
    'items',
    # 'invoicing',
    'django.contrib.humanize',
    'widget_tweaks',
    'crispy_forms',
    "crispy_bootstrap5",
    'billing',
    'case',
    'reports',
    'mathfilters',
    'finance',
    'scheduling',
    'ledger',  # New General Ledger app
    'rest_framework',
    'django_extensions',
    'common',  # Common utilities and template filters
    # 'mptt',  # Modified Preorder Tree Traversal for hierarchical data
    # 'django_mptt',

    # 'notifications', # Temporarily commented out due to migration issues










]

MIDDLEWARE = [
    # Security and input sanitization
    'common.middleware.InputSanitizationMiddleware',
    'django.middleware.security.SecurityMiddleware',

    # Session and common middleware
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',

    # Authentication
    'django.contrib.auth.middleware.AuthenticationMiddleware',

    # Messages and UI
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',

    # Custom error handling (should be last)
    'common.middleware.ErrorHandlingMiddleware',
    'accounts.custom_middleware.Custom403Middleware',
]

ROOT_URLCONF = 'LAB.urls'
TEMPLATES_DIR = os.path.join(BASE_DIR, 'LAB','templates')

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [TEMPLATES_DIR,],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]





WSGI_APPLICATION = 'LAB.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.postgresql_psycopg2',
#         'NAME': 'dental_lab',
#         'USER': 'postgres',
#         'PASSWORD': 'klodi',
#         'HOST': 'localhost',
#         'PORT': '5432',
#     }
# }

AUTH_USER_MODEL = 'accounts.CustomUser'

# Site Settings
SITE_NAME = 'Dental Lab'
DOMAIN = 'localhost:8000'  # Change this in production

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),


]



STATIC_URL = '/static/'
# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
]

BASE_CURRENCY = 'ALL'
STATIC_ROOT = os.path.join(BASE_DIR,'staticfiles')

MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
MEDIA_URL = '/media/'

CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'django.log'),
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'errors.log'),
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'common': {
            'handlers': ['file', 'error_file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'case': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'billing': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'finance': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}

# Error Handling Settings
ERROR_HANDLING = {
    'ENABLE_DETAILED_ERRORS': DEBUG,
    'LOG_ERRORS': True,
    'EMAIL_ERRORS': False,  # Set to True in production with proper email config
    'MAX_ERROR_LOG_SIZE': 10 * 1024 * 1024,  # 10MB
}

# Validation Settings
VALIDATION = {
    'ENABLE_STRICT_VALIDATION': True,
    'ENABLE_BUSINESS_RULES': True,
    'ENABLE_DATA_INTEGRITY_CHECKS': True,
    'VALIDATION_CACHE_TIMEOUT': 300,  # 5 minutes
}
