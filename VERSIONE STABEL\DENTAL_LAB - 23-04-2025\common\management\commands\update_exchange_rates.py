"""
Management command to update exchange rates
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from decimal import Decimal
from common.services import CurrencyService
from items.models import Currency, ExchangeRate

class Command(BaseCommand):
    help = 'Update exchange rates for all currencies'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--from-currency',
            type=str,
            help='Update rates from specific currency (e.g., USD)',
        )
        parser.add_argument(
            '--to-currency',
            type=str,
            help='Update rates to specific currency (e.g., ALL)',
        )
        parser.add_argument(
            '--rate',
            type=str,
            help='Specific rate to set (use with --from-currency and --to-currency)',
        )
        parser.add_argument(
            '--date',
            type=str,
            help='Date for the exchange rate (YYYY-MM-DD format)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )
    
    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting exchange rate update...')
        )
        
        # Parse date if provided
        rate_date = timezone.now().date()
        if options['date']:
            try:
                from datetime import datetime
                rate_date = datetime.strptime(options['date'], '%Y-%m-%d').date()
            except ValueError:
                raise CommandError('Invalid date format. Use YYYY-MM-DD')
        
        # Handle specific rate update
        if options['from_currency'] and options['to_currency'] and options['rate']:
            self._update_specific_rate(
                options['from_currency'],
                options['to_currency'],
                options['rate'],
                rate_date,
                options['dry_run']
            )
            return
        
        # Handle bulk update with current market rates (simulated)
        self._update_all_rates(rate_date, options['dry_run'])
    
    def _update_specific_rate(self, from_currency, to_currency, rate_str, date, dry_run):
        """Update a specific exchange rate"""
        try:
            rate = Decimal(rate_str)
            if rate <= 0:
                raise CommandError('Exchange rate must be positive')
        except ValueError:
            raise CommandError('Invalid rate format')
        
        # Validate currencies exist
        try:
            Currency.objects.get(code=from_currency)
            Currency.objects.get(code=to_currency)
        except Currency.DoesNotExist as e:
            raise CommandError(f'Currency not found: {e}')
        
        if dry_run:
            self.stdout.write(
                f'Would update: {from_currency} to {to_currency} = {rate} on {date}'
            )
            return
        
        # Update the rate
        success = CurrencyService.update_exchange_rate(
            from_currency, to_currency, rate, date
        )
        
        if success:
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ Updated: {from_currency} to {to_currency} = {rate}'
                )
            )
        else:
            self.stdout.write(
                self.style.ERROR(
                    f'❌ Failed to update: {from_currency} to {to_currency}'
                )
            )
    
    def _update_all_rates(self, date, dry_run):
        """Update all exchange rates with current market rates"""
        
        # Simulated current market rates (in a real system, these would come from an API)
        market_rates = {
            'USD': {
                'ALL': Decimal('92.50'),
                'EUR': Decimal('0.85'),
                'GBP': Decimal('0.73'),
                'CHF': Decimal('0.92'),
                'CAD': Decimal('1.35'),
                'AUD': Decimal('1.52'),
                'JPY': Decimal('149.50'),
                'CNY': Decimal('7.20'),
                'SEK': Decimal('10.95'),
                'NZD': Decimal('1.63'),
            },
            'EUR': {
                'ALL': Decimal('101.20'),
                'USD': Decimal('1.18'),
                'GBP': Decimal('0.86'),
                'CHF': Decimal('1.08'),
                'CAD': Decimal('1.59'),
                'AUD': Decimal('1.79'),
                'JPY': Decimal('176.00'),
                'CNY': Decimal('8.48'),
                'SEK': Decimal('12.90'),
                'NZD': Decimal('1.92'),
            }
        }
        
        # Get all currencies
        currencies = Currency.objects.all()
        currency_codes = [c.code for c in currencies]
        
        updated_count = 0
        failed_count = 0
        
        for from_code, rates in market_rates.items():
            if from_code not in currency_codes:
                continue
                
            for to_code, rate in rates.items():
                if to_code not in currency_codes:
                    continue
                
                if dry_run:
                    self.stdout.write(
                        f'Would update: {from_code} to {to_code} = {rate}'
                    )
                    continue
                
                success = CurrencyService.update_exchange_rate(
                    from_code, to_code, rate, date
                )
                
                if success:
                    updated_count += 1
                    self.stdout.write(
                        f'✅ {from_code} to {to_code} = {rate}'
                    )
                else:
                    failed_count += 1
                    self.stdout.write(
                        self.style.ERROR(f'❌ Failed: {from_code} to {to_code}')
                    )
        
        # Add base currency rates (ALL to others)
        base_rates = {
            'USD': Decimal('0.0108'),  # 1 ALL = 0.0108 USD
            'EUR': Decimal('0.0099'),  # 1 ALL = 0.0099 EUR
            'GBP': Decimal('0.0085'),  # 1 ALL = 0.0085 GBP
            'CHF': Decimal('0.0097'),  # 1 ALL = 0.0097 CHF
            'CAD': Decimal('0.0146'),  # 1 ALL = 0.0146 CAD
            'AUD': Decimal('0.0163'),  # 1 ALL = 0.0163 AUD
            'JPY': Decimal('1.6135'),  # 1 ALL = 1.6135 JPY
            'CNY': Decimal('0.0778'),  # 1 ALL = 0.0778 CNY
            'SEK': Decimal('0.1183'),  # 1 ALL = 0.1183 SEK
            'NZD': Decimal('0.0176'),  # 1 ALL = 0.0176 NZD
        }
        
        for to_code, rate in base_rates.items():
            if to_code not in currency_codes:
                continue
            
            if dry_run:
                self.stdout.write(
                    f'Would update: ALL to {to_code} = {rate}'
                )
                continue
            
            success = CurrencyService.update_exchange_rate(
                'ALL', to_code, rate, date
            )
            
            if success:
                updated_count += 1
                self.stdout.write(
                    f'✅ ALL to {to_code} = {rate}'
                )
            else:
                failed_count += 1
                self.stdout.write(
                    self.style.ERROR(f'❌ Failed: ALL to {to_code}')
                )
        
        # Summary
        if dry_run:
            self.stdout.write(
                self.style.WARNING('\nDry run completed. No changes were made.')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'\n✅ Exchange rate update completed!'
                )
            )
            self.stdout.write(f'Updated: {updated_count} rates')
            if failed_count > 0:
                self.stdout.write(
                    self.style.WARNING(f'Failed: {failed_count} rates')
                )
            
            # Clear cache after updates
            CurrencyService.clear_cache()
            self.stdout.write('🗑️  Exchange rate cache cleared')
