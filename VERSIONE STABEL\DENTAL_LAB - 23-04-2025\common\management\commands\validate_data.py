"""
Management command to validate data integrity
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from common.validators import run_full_validation
from common.services import DataConsistencyService

class Command(BaseCommand):
    help = 'Validate data integrity and optionally fix issues'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Automatically fix issues where possible',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output',
        )
    
    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Starting data validation...')
        )
        
        # Run validation checks
        errors = run_full_validation()
        
        if not errors:
            self.stdout.write(
                self.style.SUCCESS('✅ All data validation checks passed!')
            )
            return
        
        # Display errors
        self.stdout.write(
            self.style.ERROR(f'Found {len(errors)} data integrity issues:')
        )
        
        for error in errors:
            self.stdout.write(f'  ❌ {error}')
        
        # Fix issues if requested
        if options['fix']:
            self.stdout.write('\nAttempting to fix issues...')
            
            try:
                with transaction.atomic():
                    # Fix invoice totals
                    fixed_invoices = DataConsistencyService.fix_all_invoice_totals()
                    if fixed_invoices > 0:
                        self.stdout.write(f'  ✅ Fixed {fixed_invoices} invoice totals')
                    
                    # Fix status inconsistencies
                    fixed_statuses = DataConsistencyService.fix_all_status_inconsistencies()
                    if fixed_statuses > 0:
                        self.stdout.write(f'  ✅ Fixed {fixed_statuses} status inconsistencies')
                    
                    # Create missing exchange rates
                    created_rates = DataConsistencyService.create_missing_exchange_rates()
                    if created_rates > 0:
                        self.stdout.write(f'  ✅ Created {created_rates} missing exchange rates')
                
                # Re-run validation
                remaining_errors = run_full_validation()
                if not remaining_errors:
                    self.stdout.write(
                        self.style.SUCCESS('\n🎉 All issues have been fixed!')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'\n⚠️  {len(remaining_errors)} issues remain and need manual attention')
                    )
                    for error in remaining_errors:
                        self.stdout.write(f'  ❌ {error}')
            
            except Exception as e:
                raise CommandError(f'Error during fix: {e}')
        
        else:
            self.stdout.write(
                self.style.WARNING('\nRun with --fix to automatically fix issues where possible')
            )
