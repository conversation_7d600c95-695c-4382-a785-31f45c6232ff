{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Purchase Recommendations" %}{% endblock %}

{% block content %}
<div class="container-fluid my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">
            <i class="bi bi-cart-plus"></i> {% trans "Purchase Recommendations" %}
        </h1>
        <div>
            <a href="{% url 'items:stock_alerts_dashboard' %}" class="btn btn-warning me-2">
                <i class="bi bi-exclamation-triangle"></i> {% trans "Stock Alerts" %}
            </a>
            <a href="{% url 'items:material_requirements_planning' %}" class="btn btn-info">
                <i class="bi bi-diagram-3"></i> {% trans "MRP Dashboard" %}
            </a>
        </div>
    </div>

    <!-- Filters and Summary -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="days_ahead" class="form-label">{% trans "Planning Period (Days)" %}</label>
                            <select name="days_ahead" id="days_ahead" class="form-select">
                                <option value="7" {% if days_ahead == 7 %}selected{% endif %}>7 {% trans "days" %}</option>
                                <option value="14" {% if days_ahead == 14 %}selected{% endif %}>14 {% trans "days" %}</option>
                                <option value="30" {% if days_ahead == 30 %}selected{% endif %}>30 {% trans "days" %}</option>
                                <option value="60" {% if days_ahead == 60 %}selected{% endif %}>60 {% trans "days" %}</option>
                                <option value="90" {% if days_ahead == 90 %}selected{% endif %}>90 {% trans "days" %}</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> {% trans "Update Recommendations" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="card-title">{% trans "Summary" %}</h6>
                    <p class="mb-1"><strong>{% trans "Total Recommendations:" %}</strong> {{ total_recommendations }}</p>
                    <p class="mb-1"><strong>{% trans "Urgent Items:" %}</strong> {{ urgent_count }}</p>
                    <p class="mb-1"><strong>{% trans "Total Cost:" %}</strong> ${{ total_cost|floatformat:2 }}</p>
                    <p class="mb-0"><strong>{% trans "Urgent Cost:" %}</strong> ${{ urgent_cost|floatformat:2 }}</p>
                </div>
            </div>
        </div>
    </div>

    {% if recommendations %}
        <!-- Bulk Actions -->
        <form method="post" action="{% url 'items:create_purchase_order_from_recommendation' %}" id="bulkActionForm">
            {% csrf_token %}
            <div class="card mb-4">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{% trans "Bulk Actions" %}</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectAll()">
                                {% trans "Select All" %}
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNone()">
                                {% trans "Select None" %}
                            </button>
                            <button type="submit" class="btn btn-sm btn-success" onclick="return confirmBulkAction()">
                                <i class="bi bi-cart-plus"></i> {% trans "Create Purchase Orders" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Urgent Recommendations -->
            {% if recommendations_by_priority.urgent %}
                <div class="card mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-exclamation-triangle-fill"></i> {% trans "Urgent Recommendations" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" class="form-check-input priority-select" data-priority="urgent">
                                        </th>
                                        <th>{% trans "Material" %}</th>
                                        <th>{% trans "Supplier" %}</th>
                                        <th>{% trans "Quantity" %}</th>
                                        <th>{% trans "Cost" %}</th>
                                        <th>{% trans "Delivery" %}</th>
                                        <th>{% trans "Reason" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rec in recommendations_by_priority.urgent %}
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="recommendation_ids" value="{{ rec.raw_material.id }}" 
                                                       class="form-check-input recommendation-checkbox urgent-checkbox">
                                            </td>
                                            <td>
                                                <strong>{{ rec.raw_material.name }}</strong>
                                                {% if rec.raw_material.description %}
                                                    <br><small class="text-muted">{{ rec.raw_material.description|truncatechars:40 }}</small>
                                                {% endif %}
                                            </td>
                                            <td>{{ rec.supplier.name }}</td>
                                            <td>{{ rec.recommended_quantity }} {{ rec.raw_material.unit.name }}</td>
                                            <td>${{ rec.estimated_cost|floatformat:2 }}</td>
                                            <td>
                                                {% if rec.delivery_date %}
                                                    {{ rec.delivery_date|date:"M d, Y" }}
                                                {% else %}
                                                    {% trans "TBD" %}
                                                {% endif %}
                                            </td>
                                            <td>
                                                <small>{{ rec.reason|truncatechars:50 }}</small>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- High Priority Recommendations -->
            {% if recommendations_by_priority.high %}
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-exclamation-circle"></i> {% trans "High Priority Recommendations" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" class="form-check-input priority-select" data-priority="high">
                                        </th>
                                        <th>{% trans "Material" %}</th>
                                        <th>{% trans "Supplier" %}</th>
                                        <th>{% trans "Quantity" %}</th>
                                        <th>{% trans "Cost" %}</th>
                                        <th>{% trans "Delivery" %}</th>
                                        <th>{% trans "Reason" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rec in recommendations_by_priority.high %}
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="recommendation_ids" value="{{ rec.raw_material.id }}" 
                                                       class="form-check-input recommendation-checkbox high-checkbox">
                                            </td>
                                            <td>
                                                <strong>{{ rec.raw_material.name }}</strong>
                                                {% if rec.raw_material.description %}
                                                    <br><small class="text-muted">{{ rec.raw_material.description|truncatechars:40 }}</small>
                                                {% endif %}
                                            </td>
                                            <td>{{ rec.supplier.name }}</td>
                                            <td>{{ rec.recommended_quantity }} {{ rec.raw_material.unit.name }}</td>
                                            <td>${{ rec.estimated_cost|floatformat:2 }}</td>
                                            <td>
                                                {% if rec.delivery_date %}
                                                    {{ rec.delivery_date|date:"M d, Y" }}
                                                {% else %}
                                                    {% trans "TBD" %}
                                                {% endif %}
                                            </td>
                                            <td>
                                                <small>{{ rec.reason|truncatechars:50 }}</small>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Medium and Low Priority (Collapsed by default) -->
            {% if recommendations_by_priority.medium or recommendations_by_priority.low %}
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <button class="btn btn-link text-white text-decoration-none p-0" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#mediumLowPriority" 
                                    aria-expanded="false" aria-controls="mediumLowPriority">
                                <i class="bi bi-info-circle"></i> {% trans "Medium & Low Priority Recommendations" %}
                                <i class="bi bi-chevron-down"></i>
                            </button>
                        </h5>
                    </div>
                    <div class="collapse" id="mediumLowPriority">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th width="50">
                                                <input type="checkbox" class="form-check-input priority-select" data-priority="medium-low">
                                            </th>
                                            <th>{% trans "Priority" %}</th>
                                            <th>{% trans "Material" %}</th>
                                            <th>{% trans "Supplier" %}</th>
                                            <th>{% trans "Quantity" %}</th>
                                            <th>{% trans "Cost" %}</th>
                                            <th>{% trans "Delivery" %}</th>
                                            <th>{% trans "Reason" %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for rec in recommendations_by_priority.medium %}
                                            <tr>
                                                <td>
                                                    <input type="checkbox" name="recommendation_ids" value="{{ rec.raw_material.id }}" 
                                                           class="form-check-input recommendation-checkbox medium-low-checkbox">
                                                </td>
                                                <td><span class="badge bg-warning">{% trans "Medium" %}</span></td>
                                                <td>{{ rec.raw_material.name }}</td>
                                                <td>{{ rec.supplier.name }}</td>
                                                <td>{{ rec.recommended_quantity }} {{ rec.raw_material.unit.name }}</td>
                                                <td>${{ rec.estimated_cost|floatformat:2 }}</td>
                                                <td>
                                                    {% if rec.delivery_date %}
                                                        {{ rec.delivery_date|date:"M d, Y" }}
                                                    {% else %}
                                                        {% trans "TBD" %}
                                                    {% endif %}
                                                </td>
                                                <td><small>{{ rec.reason|truncatechars:40 }}</small></td>
                                            </tr>
                                        {% endfor %}
                                        {% for rec in recommendations_by_priority.low %}
                                            <tr>
                                                <td>
                                                    <input type="checkbox" name="recommendation_ids" value="{{ rec.raw_material.id }}" 
                                                           class="form-check-input recommendation-checkbox medium-low-checkbox">
                                                </td>
                                                <td><span class="badge bg-secondary">{% trans "Low" %}</span></td>
                                                <td>{{ rec.raw_material.name }}</td>
                                                <td>{{ rec.supplier.name }}</td>
                                                <td>{{ rec.recommended_quantity }} {{ rec.raw_material.unit.name }}</td>
                                                <td>${{ rec.estimated_cost|floatformat:2 }}</td>
                                                <td>
                                                    {% if rec.delivery_date %}
                                                        {{ rec.delivery_date|date:"M d, Y" }}
                                                    {% else %}
                                                        {% trans "TBD" %}
                                                    {% endif %}
                                                </td>
                                                <td><small>{{ rec.reason|truncatechars:40 }}</small></td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </form>
    {% else %}
        <!-- No Recommendations -->
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                <h3 class="mt-3 text-success">{% trans "No Purchase Recommendations" %}</h3>
                <p class="text-muted">{% trans "All materials are adequately stocked for the selected planning period." %}</p>
                <a href="{% url 'items:stock_alerts_dashboard' %}" class="btn btn-outline-primary">
                    {% trans "Check Stock Alerts" %}
                </a>
            </div>
        </div>
    {% endif %}
</div>

<script>
function selectAll() {
    document.querySelectorAll('.recommendation-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function selectNone() {
    document.querySelectorAll('.recommendation-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
}

function confirmBulkAction() {
    const selectedCount = document.querySelectorAll('.recommendation-checkbox:checked').length;
    if (selectedCount === 0) {
        alert('{% trans "Please select at least one recommendation." %}');
        return false;
    }
    return confirm(`{% trans "Create purchase orders for " %}${selectedCount}{% trans " selected recommendations?" %}`);
}

// Priority-based selection
document.querySelectorAll('.priority-select').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const priority = this.dataset.priority;
        const targetClass = priority + '-checkbox';
        document.querySelectorAll('.' + targetClass).forEach(target => {
            target.checked = this.checked;
        });
    });
});
</script>
{% endblock %}
