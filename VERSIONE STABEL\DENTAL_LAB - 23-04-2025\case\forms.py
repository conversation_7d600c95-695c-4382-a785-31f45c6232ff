# forms.py

import datetime
import logging
from datetime import timed<PERSON><PERSON>
from django import forms
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator
from django.forms import (BaseInlineFormSet, ModelForm, ValidationError,
                         inlineformset_factory, modelformset_factory)
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

logger = logging.getLogger(__name__)

from items.models import Item, Unit
from patients.models import Patient
from Dentists.models import Dentist
from .models import (Case, CaseItem, Tooth, TryoutAttachment, CaseTeeth,
                    Department, WorkflowStage, WorkflowTemplate)

class BaseCaseValidationForm(forms.ModelForm):
    """
    Abstract base form for shared business rule validation (deadline, estimated_duration).
    """
    class Meta:
        abstract = True

    def clean(self):
        cleaned_data = super().clean()
        deadline = cleaned_data.get('deadline')
        estimated_duration = cleaned_data.get('estimated_duration')
        if deadline and deadline < timezone.now():
            self.add_error('deadline', _('Deadline cannot be in the past'))
        if estimated_duration is not None and hasattr(estimated_duration, 'total_seconds') and estimated_duration.total_seconds() <= 0:
            self.add_error('estimated_duration', _('Duration must be positive'))
        return cleaned_data

class CaseForm(BaseCaseValidationForm):
    # Hidden field for teeth selection
    selected_teeth_input = forms.CharField(
        required=False,
        widget=forms.HiddenInput()
    )

    # Existing fields
    patient = forms.ModelChoiceField(
        queryset=Patient.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-control select2-patients',
            'data-placeholder': 'Kërkoni ose shtoni një pacient'
        })
    )

    dentist = forms.ModelChoiceField(
        queryset=Dentist.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select dentist')
        })
    )

    workflow_template = forms.ModelChoiceField(
        queryset=WorkflowTemplate.objects.filter(status='active'),
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select workflow template')
        })
    )

    status = forms.ChoiceField(
        choices=Case.STATUS_CHOICES,
        required=True,
        widget=forms.Select(attrs={
            'class': 'form-select',
            'data-tooltip': _('Current status of the case')
        })
    )

    priority = forms.ChoiceField(
        choices=Case.PRIORITY_CHOICES,
        initial=2,
        widget=forms.Select(attrs={
            'class': 'form-select',
            'data-tooltip': _('Case priority level')
        })
    )

    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': _('Add case notes')
        })
    )

    teeth_color = forms.CharField(  # Changed from teethcolor to teeth_color to match model
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control color-picker',
            'data-tooltip': _('Select teeth color')
        })
    )

    received_date_time = forms.DateTimeField(
        required=False,
        input_formats=['%Y-%m-%d %H:%M'],
        widget=forms.DateTimeInput(attrs={
            'type': 'datetime-local',
            'class': 'form-control datetimepicker'
        })
    )

    finished_date_time = forms.DateTimeField(
        required=False,
        input_formats=['%Y-%m-%d %H:%M'],
        widget=forms.DateTimeInput(attrs={
            'type': 'datetime-local',
            'class': 'form-control datetimepicker'
        })
    )

    ship_date_time = forms.DateTimeField(
        required=False,
        input_formats=['%Y-%m-%d %H:%M'],
        widget=forms.DateTimeInput(attrs={
            'type': 'datetime-local',
            'class': 'form-control datetimepicker'
        })
    )

    deadline = forms.DateTimeField(
        required=False,
        input_formats=['%Y-%m-%d %H:%M'],
        widget=forms.DateTimeInput(attrs={
            'type': 'datetime-local',
            'class': 'form-control datetimepicker'
        })
    )

    estimated_duration = forms.DurationField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'HH:MM:SS'
        })
    )

    responsible_department = forms.ModelChoiceField(
        queryset=Department.objects.filter(is_active=True),
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select department')
        })
    )

    current_stage = forms.ModelChoiceField(
        queryset=WorkflowStage.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select stage')
        })
    )



    patient_feedback = forms.DecimalField(
        required=False,
        max_digits=3,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': '0',
            'max': '5',
            'step': '0.5'
        })
    )

    special_requirements = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': _('Any special requirements')
        })
    )

    quality_checklist = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        initial=dict
    )

    class Meta:
        model = Case
        fields = [
            'patient', 'dentist', 'workflow_template', 'status',
            'priority', 'notes', 'teeth_color', 'received_date_time',
            'finished_date_time', 'ship_date_time', 'deadline',
            'estimated_duration', 'responsible_department',
            'current_stage', 'attachments', 'patient_feedback',
            'special_requirements','selected_teeth_input'
        ]

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Filter querysets based on user permissions
        if self.user and not self.user.is_superuser:
            department_ids = self.user.departments.values_list('department_id', flat=True)
            self.fields['responsible_department'].queryset = Department.objects.filter(
                id__in=department_ids,
                is_active=True
            )
            if self.user.dentist_profile:
                self.fields['dentist'].initial = self.user.dentist_profile
                self.fields['dentist'].widget.attrs['readonly'] = True

        # Update workflow stages based on department
        if 'responsible_department' in self.data:
            try:
                department_id = int(self.data.get('responsible_department'))
                self.fields['current_stage'].queryset = WorkflowStage.objects.filter(
                    department_id=department_id
                ).order_by('order')
            except (ValueError, TypeError):
                pass
        elif self.instance.pk and self.instance.responsible_department:
            self.fields['current_stage'].queryset = WorkflowStage.objects.filter(
                department=self.instance.responsible_department
            ).order_by('order')

        # Set initial selected teeth if editing existing case
        if self.instance.pk:
            case_teeth = CaseTeeth.objects.filter(case=self.instance)
            if case_teeth.exists():
                self.initial['selected_teeth_input'] = ','.join(
                    str(ct.tooth.tooth_number) for ct in case_teeth
                )

    def save(self, commit=True):
        instance = super().save(commit=False)

        if not instance.pk:
            instance.created_by = self.user
            if hasattr(self.user, 'dentist_profile'):
                instance.dentist = self.user.dentist_profile
                instance.dentist_user = self.user
            instance.status = 'pending_acceptance'

        if commit:
            instance.save()
            self.save_m2m()

            # Process teeth selection
            self.process_teeth_selection(instance)

        return instance

    def process_teeth_selection(self, instance):
        """Process teeth selection separately to handle the issue with multiple values"""
        try:
            # Check if this is an update form
            is_update = False
            if hasattr(self, 'data'):
                is_update = self.data.get('is_update_form') == 'true' or self.data.get('teeth_selection_updated') == 'true'

            logger.debug(f"Processing teeth selection for case #{instance.case_number} (is_update: {is_update})")

            # Get the current teeth before clearing them (for logging/comparison)
            current_teeth = list(instance.selected_teeth.values_list('tooth_number', flat=True))
            logger.debug(f"Current teeth before processing: {current_teeth}")

            # Clear existing teeth associations
            CaseTeeth.objects.filter(case=instance).delete()
            logger.debug(f"Cleared existing teeth associations for case #{instance.case_number}")

            # Get teeth selection from the form data
            teeth_input = self.cleaned_data.get('selected_teeth_input', '')

            # Debug logging
            logger.debug(f"Raw teeth_input value from cleaned_data: '{teeth_input}'")

            # If not found in cleaned_data, try to get it directly from POST data
            if not teeth_input and hasattr(self, 'data'):
                # Try to get it as a list first (for multi-value data)
                teeth_inputs = self.data.getlist('selected_teeth_input')
                # Debug logging
                logger.debug(f"Multiple teeth_inputs from POST: {teeth_inputs}")

                # Find the first non-empty value in the list
                for value in teeth_inputs:
                    if value and value.strip():
                        teeth_input = value
                        logger.debug(f"Found non-empty value in list: '{value}'")
                        break

                # If still not found, try to get it as a single value
                if not teeth_input:
                    teeth_input = self.data.get('selected_teeth_input', '')
                    logger.debug(f"Single teeth_input from POST: '{teeth_input}'")

            logger.debug(f"Final teeth_input to process: '{teeth_input}'")

            if teeth_input and teeth_input.strip():
                # Process each tooth number
                teeth_numbers = []
                for num in teeth_input.split(','):
                    num = num.strip()
                    if num and num.isdigit():
                        teeth_numbers.append(int(num))
                        logger.debug(f"Added tooth number: {num}")
                    elif num:
                        logger.warning(f"Invalid tooth number format: '{num}'")

                logger.debug(f"Parsed tooth numbers: {teeth_numbers}")

                # Log changes if this is an update
                if is_update:
                    added = [num for num in teeth_numbers if num not in current_teeth]
                    removed = [num for num in current_teeth if num not in teeth_numbers]
                    logger.debug(f"Teeth added: {added}")
                    logger.debug(f"Teeth removed: {removed}")

                # Create tooth associations
                for number in teeth_numbers:
                    tooth, created = Tooth.objects.get_or_create(
                        tooth_number=number,
                        defaults={'tooth_name': str(number)}
                    )
                    CaseTeeth.objects.create(case=instance, tooth=tooth)
                    logger.debug(f"Created tooth association for number {number} (new tooth: {created})")

                logger.debug(f"Created {len(teeth_numbers)} tooth associations for case #{instance.case_number}")
            else:
                logger.warning(f"No teeth selected for case #{instance.case_number}")
        except Exception as e:
            logger.error(f"Error processing teeth: {str(e)}", exc_info=True)
            # Don't raise the exception, but make it visible in logs

        return instance

class DentistSimpleCaseForm(BaseCaseValidationForm):
    selected_teeth_input = forms.CharField(
        required=False,
        widget=forms.HiddenInput(attrs={'class': 'teeth-selection-input'})
    )

    workflow_template = forms.ModelChoiceField(
        queryset=WorkflowTemplate.objects.filter(status='active'),
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select workflow template')
        })
    )
    responsible_department = forms.ModelChoiceField(
        queryset=Department.objects.filter(is_active=True),
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select department')
        })
    )
    current_stage = forms.ModelChoiceField(
        queryset=WorkflowStage.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select stage')
        })
    )

    class Meta:
        model = Case
        fields = [
            'patient', 'notes', 'received_date_time',
            'teeth_color', 'attachments', 'priority',
            'workflow_template', 'responsible_department', 'current_stage'
        ]
        widgets = {
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('Add any notes')
            }),
            'received_date_time': forms.DateTimeInput(attrs={
                'class': 'form-control datetimepicker',
                'type': 'datetime-local'
            }),
            'teeth_color': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Teeth color')
            }),

            'priority': forms.Select(attrs={
                'class': 'form-select'
            })
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Set initial values if user is a dentist
        if self.user and hasattr(self.user, 'dentist_profile'):
            self.instance.dentist = self.user.dentist_profile
            self.instance.dentist_user = self.user

        # Filter patient queryset to show only dentist's patients
        if self.user and self.user.dentist_profile:
            self.fields['patient'].queryset = Patient.objects.filter(
                dentist=self.user.dentist_profile
            )

        # Set initial selected teeth if editing existing case
        if self.instance.pk:
            selected_teeth = self.instance.selected_teeth.all()
            if selected_teeth:
                self.initial['selected_teeth_input'] = ','.join(
                    str(tooth.tooth_number) for tooth in selected_teeth
                )

        # Set default workflow_template and department if not set
        if not self.instance.workflow_template:
            default_template = WorkflowTemplate.objects.filter(is_default=True).first()
            if default_template:
                self.fields['workflow_template'].initial = default_template.pk
        if not self.instance.responsible_department:
            default_dept = Department.objects.filter(is_active=True).first()
            if default_dept:
                self.fields['responsible_department'].initial = default_dept.pk

    def clean(self):
        cleaned_data = super().clean()
        selected_teeth_str = cleaned_data.get('selected_teeth_input')
        if selected_teeth_str:
            teeth_numbers = [num.strip() for num in selected_teeth_str.split(',') if num.strip().isdigit()]
            if not teeth_numbers:
                self.add_error('selected_teeth_input', _('Invalid teeth selection.'))
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        if not instance.pk:
            instance.created_by = self.user
            if self.user and hasattr(self.user, 'dentist_profile'):
                instance.dentist = self.user.dentist_profile
            instance.dentist_user = self.user
            instance.status = 'pending_acceptance'
        if commit:
            instance.save()
            # Handle selected teeth
            selected_teeth_str = self.cleaned_data.get('selected_teeth_input')
            logger.debug(f"DentistSimpleCaseForm - selected_teeth_str: '{selected_teeth_str}'")

            # If not found in cleaned_data, try to get it directly from POST data
            if not selected_teeth_str and hasattr(self, 'data'):
                # Try to get it as a list first
                teeth_inputs = self.data.getlist('selected_teeth_input')
                logger.debug(f"DentistSimpleCaseForm - teeth_inputs from POST: {teeth_inputs}")

                # Find the first non-empty value in the list
                for value in teeth_inputs:
                    if value and value.strip():
                        selected_teeth_str = value
                        logger.debug(f"DentistSimpleCaseForm - Found non-empty value: '{value}'")
                        break

                # If still not found, try to get it as a single value
                if not selected_teeth_str:
                    selected_teeth_str = self.data.get('selected_teeth_input', '')
                    logger.debug(f"DentistSimpleCaseForm - Single value from POST: '{selected_teeth_str}'")

            if selected_teeth_str:
                logger.debug(f"DentistSimpleCaseForm - Processing teeth: '{selected_teeth_str}'")
                instance.selected_teeth.clear()
                teeth_numbers = []
                for num in selected_teeth_str.split(','):
                    num = num.strip()
                    if num and num.isdigit():
                        teeth_numbers.append(int(num))
                        logger.debug(f"DentistSimpleCaseForm - Added tooth number: {num}")

                for number in teeth_numbers:
                    tooth, created = Tooth.objects.get_or_create(tooth_number=number, defaults={'tooth_name': str(number)})
                    instance.selected_teeth.add(tooth)
                    logger.debug(f"DentistSimpleCaseForm - Added tooth {number} to case (new: {created})")

                logger.debug(f"DentistSimpleCaseForm - Added {len(teeth_numbers)} teeth to case #{instance.case_number}")
            else:
                logger.warning(f"DentistSimpleCaseForm - No teeth selected for case #{instance.case_number}")

            self.save_m2m()
        return instance

from django.forms import modelformset_factory, BaseModelFormSet
from .models import CaseItem

# Formset i personalizuar për CaseItem
class BaseCaseItemFormSet(BaseModelFormSet):
    def save_new(self, form, commit=True):
        """Metodë e personalizuar për të ruajtur items e reja"""
        # Krijo objektin por mos e ruaj ende
        obj = super().save_new(form, commit=False)

        # Sigurohu që case_id është vendosur nëse kemi një instance
        if hasattr(self, 'instance') and self.instance and not obj.case_id:
            obj.case = self.instance

        # Ruaj objektin nëse commit=True
        if commit:
            obj.save()

        return obj

CaseItemFormSet = modelformset_factory(
    CaseItem,
    fields=('item', 'quantity', 'unit', 'estimated_production_time', 'actual_production_time', 'status', 'notes', 'assigned_to'),
    formset=BaseCaseItemFormSet,  # Përdor formset-in e personalizuar
    extra=1,
    can_delete=True
)

from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import WorkflowTemplate

class WorkflowTemplateForm(forms.ModelForm):
    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Template name'),
            'data-tooltip': _('Enter a unique name for this workflow template')
        })
    )

    description = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': _('Describe the workflow template...')
        })
    )

    default_duration = forms.DurationField(
        widget=forms.TextInput(attrs={
            'class': 'form-control duration-picker',
            'placeholder': 'HH:MM:SS',
            'data-tooltip': _('Expected total duration for this workflow')
        })
    )

    priority = forms.ChoiceField(
        choices=WorkflowTemplate.PRIORITY_CHOICES,
        initial=2,
        widget=forms.Select(attrs={
            'class': 'form-select',
            'data-tooltip': _('Default priority level')
        })
    )

    status = forms.ChoiceField(
        choices=WorkflowTemplate.STATUS_CHOICES,
        initial='active',
        widget=forms.Select(attrs={
            'class': 'form-select',
            'data-tooltip': _('Template status')
        })
    )

    is_default = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input',
            'data-tooltip': _('Set as default template')
        })
    )

    estimated_cost = forms.DecimalField(
        required=False,
        max_digits=10,
        decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': _('Estimated cost'),
            'step': '0.01'
        })
    )

    required_skills = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        initial=dict
    )

    quality_requirements = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        initial=dict
    )

    applicable_case_types = forms.MultipleChoiceField(
        required=False,
        choices=[],  # Set in __init__
        widget=forms.SelectMultiple(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select applicable case types')
        })
    )

    departments = forms.ModelMultipleChoiceField(
        queryset=None,  # Set in __init__
        required=True,
        widget=forms.SelectMultiple(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select involved departments')
        })
    )

    class Meta:
        model = WorkflowTemplate
        fields = [
            'name', 'description', 'default_duration', 'priority',
            'status', 'is_default', 'estimated_cost', 'required_skills',
            'quality_requirements', 'applicable_case_types', 'departments'
        ]

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Set department queryset based on user permissions
        if self.user and not self.user.is_superuser:
            self.fields['departments'].queryset = Department.objects.filter(
                id__in=self.user.departments.values_list('department_id', flat=True),
                is_active=True
            )
        else:
            self.fields['departments'].queryset = Department.objects.filter(is_active=True)

        # Set case type choices
        self.fields['applicable_case_types'].choices = Case.TYPE_CHOICES

        # If editing existing template
        if self.instance.pk:
            # Set initial values for JSON fields
            self.fields['required_skills'].initial = self.instance.required_skills
            self.fields['quality_requirements'].initial = self.instance.quality_requirements

            # Set initial departments
            self.fields['departments'].initial = self.instance.stages.values_list(
                'department', flat=True
            ).distinct()

    def clean(self):
        cleaned_data = super().clean()
        name = cleaned_data.get('name')
        is_default = cleaned_data.get('is_default')
        status = cleaned_data.get('status')
        departments = cleaned_data.get('departments', [])

        # Validate unique name
        if name:
            exists_query = WorkflowTemplate.objects.filter(name=name)
            if self.instance.pk:
                exists_query = exists_query.exclude(pk=self.instance.pk)
            if exists_query.exists():
                raise ValidationError({'name': _('A template with this name already exists')})

        # Validate default template
        if is_default:
            if status != 'active':
                raise ValidationError({
                    'is_default': _('Default template must be active')
                })

            exists_query = WorkflowTemplate.objects.filter(is_default=True)
            if self.instance.pk:
                exists_query = exists_query.exclude(pk=self.instance.pk)
            if exists_query.exists():
                raise ValidationError({
                    'is_default': _('Another template is already set as default')
                })

        # Validate departments
        if len(departments) < 1:
            raise ValidationError(_('At least one department must be selected'))

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)

        if not instance.pk:
            instance.created_by = self.user

        if commit:
            instance.save()

            # Save departments
            if 'departments' in self.cleaned_data:
                # Create initial stages for each department
                self.create_initial_stages(instance)

            # Create notifications for department managers
            self.notify_department_managers(instance)

        return instance

    def create_initial_stages(self, template):
        """Create initial workflow stages for each department"""
        departments = self.cleaned_data['departments']

        for order, department in enumerate(departments, start=1):
            WorkflowStage.objects.create(
                workflow=template,
                name=f"{department.name} Stage",
                department=department,
                order=order,
                estimated_duration=template.default_duration / len(departments),
                required_skills=template.required_skills.get(str(department.id), {}),
                is_critical=order == 1  # First stage is critical by default
            )

    def notify_department_managers(self, template):
        """Notify department managers about new/updated template"""
        from notifications.models import Notification

        action = 'updated' if self.instance.pk else 'created'

        for department in self.cleaned_data['departments']:
            if department.manager:
                Notification.objects.create(
                    user=department.manager,
                    title=_('Workflow Template Update'),
                    message=_(f'Workflow template "{template.name}" has been {action}'),
                    notification_type='workflow_template_update',
                    reference_id=template.id
                )

    def get_preview_data(self):
        """Get preview data for template visualization"""
        cleaned_data = self.cleaned_data
        departments = cleaned_data.get('departments', [])

        stages = []
        current_time = datetime.timedelta(0)

        for order, department in enumerate(departments, start=1):
            stage_duration = cleaned_data['default_duration'] / len(departments)
            stages.append({
                'name': f"{department.name} Stage",
                'department': department.name,
                'order': order,
                'start_time': current_time,
                'end_time': current_time + stage_duration,
                'duration': stage_duration
            })
            current_time += stage_duration

        return {
            'stages': stages,
            'total_duration': cleaned_data['default_duration'],
            'total_departments': len(departments)
        }

from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import WorkflowStage, Case

class WorkflowStageForm(forms.ModelForm):
    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Stage name')
        })
    )

    department = forms.ModelChoiceField(
        queryset=Department.objects.filter(is_active=True),
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select department')
        })
    )

    order = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': _('Stage order')
        })
    )

    estimated_duration = forms.DurationField(
        widget=forms.TextInput(attrs={
            'class': 'form-control duration-picker',
            'placeholder': 'HH:MM:SS'
        })
    )

    required_skills = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        initial=dict
    )

    required_equipment = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        initial=dict
    )

    quality_checklist = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        initial=dict
    )

    is_critical = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input',
            'data-tooltip': _('Mark as critical stage')
        })
    )

    dependencies = forms.ModelMultipleChoiceField(
        queryset=WorkflowStage.objects.all(),
        required=False,
        widget=forms.SelectMultiple(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select dependencies')
        })
    )

    class Meta:
        model = WorkflowStage
        fields = ['name', 'department', 'order', 'estimated_duration', 'required_skills',
                 'required_equipment', 'quality_checklist', 'is_critical', 'dependencies']

    def __init__(self, *args, **kwargs):
        workflow_template = kwargs.pop('workflow_template', None)
        super().__init__(*args, **kwargs)

        if workflow_template:
            self.fields['dependencies'].queryset = WorkflowStage.objects.filter(
                workflow_template=workflow_template
            ).exclude(pk=self.instance.pk if self.instance.pk else None)

    def clean(self):
        cleaned_data = super().clean()
        order = cleaned_data.get('order')
        dependencies = cleaned_data.get('dependencies', [])

        # Check for circular dependencies
        if self.instance.pk and self.instance in dependencies:
            raise ValidationError(_('A stage cannot depend on itself'))

        # Validate order with dependencies
        for dependency in dependencies:
            if dependency.order >= order:
                raise ValidationError(
                    _('Dependencies must have lower order than the current stage')
                )

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        if commit:
            instance.save()
            self.save_m2m()  # Save dependencies
        return instance

class CaseStatusForm(forms.ModelForm):
    status = forms.ChoiceField(
        choices=Case.STATUS_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select',
            'data-tooltip': _('Change case status')
        })
    )

    reason = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': _('Reason for status change')
        })
    )

    notify_involved = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input',
            'data-tooltip': _('Notify involved parties')
        })
    )

    class Meta:
        model = Case
        fields = ['status', 'reason']

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Filter available status choices based on current status and permissions
        self.fields['status'].choices = self.get_available_status_choices()

    def get_available_status_choices(self):
        """Get available status choices based on current status and user permissions"""
        current_status = self.instance.status
        all_choices = dict(Case.STATUS_CHOICES)
        available_choices = []

        # Define valid status transitions
        VALID_TRANSITIONS = {
            'pending_acceptance': ['in_progress', 'cancelled'],
            'in_progress': ['on_hold', 'completed', 'cancelled'],
            'on_hold': ['in_progress', 'cancelled'],
            'completed': ['ready_to_ship', 'revision_needed'],
            'ready_to_ship': ['shipped', 'revision_needed'],
            'shipped': ['delivered'],
            'revision_needed': ['in_progress']
        }

        if current_status in VALID_TRANSITIONS:
            available_statuses = VALID_TRANSITIONS[current_status]
            available_choices = [(s, all_choices[s]) for s in available_statuses]

            # Always include current status
            available_choices.append((current_status, all_choices[current_status]))

        return sorted(available_choices)

    def clean(self):
        cleaned_data = super().clean()
        new_status = cleaned_data.get('status')
        reason = cleaned_data.get('reason')

        # Require reason for certain status changes
        if new_status in ['cancelled', 'on_hold', 'revision_needed'] and not reason:
            raise ValidationError({
                'reason': _('Please provide a reason for this status change')
            })

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        old_status = instance.status

        if commit:
            instance.save()

            # Log status change
            self.log_status_change(old_status, instance.status)

            # Send notifications if requested
            if self.cleaned_data.get('notify_involved'):
                self.send_status_notifications(instance)

        return instance

    def log_status_change(self, old_status, new_status):
        """Log the status change"""
        from .models import CaseStatusHistory

        CaseStatusHistory.objects.create(
            case=self.instance,
            old_status=old_status,
            new_status=new_status,
            reason=self.cleaned_data.get('reason', ''),
            changed_by=self.user
        )

    def send_status_notifications(self, case):
        """Send notifications about status change"""
        from notifications.models import Notification

        # Create notification for dentist
        if case.dentist_user:
            Notification.objects.create(
                user=case.dentist_user,
                title=_('Case Status Updated'),
                message=_(f'Case #{case.case_number} status changed to {case.get_status_display()}'),
                notification_type='case_status_change',
                reference_id=case.case_number
            )

        # Notify department manager
        if case.responsible_department and case.responsible_department.manager:
            Notification.objects.create(
                user=case.responsible_department.manager,
                title=_('Case Status Updated'),
                message=_(f'Case #{case.case_number} status changed to {case.get_status_display()}'),
                notification_type='case_status_change',
                reference_id=case.case_number
            )

class CaseItemForm(forms.ModelForm):
    id = forms.IntegerField(required=False, widget=forms.HiddenInput())

    class Meta:
        model = CaseItem
        fields = ['id', 'item', 'quantity', 'unit', 'estimated_production_time', 'actual_production_time', 'status', 'notes', 'assigned_to']

    def clean_id(self):
        id_value = self.cleaned_data.get('id')
        if isinstance(id_value, CaseItem):
            return id_value.id
        if isinstance(id_value, str):
            # Remove any non-digit characters
            id_value = ''.join(filter(str.isdigit, id_value))
        if id_value in (None, ''):
            return None
        try:
            return int(id_value)
        except (ValueError, TypeError):
            return None

    def clean(self):
        cleaned_data = super().clean()
        # Add any additional form-wide cleaning here if needed
        return cleaned_data

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['unit'].required = True
        self.fields['unit'].empty_label = None  # This will force the user to select a unit

    def clean_unit(self):
        unit = self.cleaned_data.get('unit')
        if unit is None:
            raise forms.ValidationError("Please select a unit.")
        return unit

from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from datetime import datetime, timedelta
from .models import Tryout, TryoutAttachment, TryoutLocation, Case

class TryoutForm(forms.ModelForm):
    # Add duration field as integer field for minutes
    duration = forms.IntegerField(
        required=False,
        min_value=1,
        max_value=480,  # 8 hours = 480 minutes
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Duration in minutes'
        }),
        help_text='Enter duration in minutes (1-480)'
    )

    class Meta:
        model = Tryout
        fields = [
            'case', 'date_time', 'status',
            'notes', 'outcome', 'feedback', 'duration',
            'confirmation_status'
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # If there's an instance with duration, convert timedelta to minutes
        if self.instance.pk and self.instance.duration:
            self.initial['duration'] = int(self.instance.duration.total_seconds() / 60)

        # Common attributes for all fields
        for field_name, field in self.fields.items():
            if field_name != 'confirmation_status':
                field.widget.attrs.update({
                    'class': 'form-control'
                })

        # Customize case field
        self.fields['case'].widget.attrs.update({
            'class': 'form-select',
            'data-placeholder': _('Select Case')
        })

        # Customize location field
        self.fields['location'] = forms.ModelChoiceField(
            queryset=TryoutLocation.objects.all(),
            required=False,
            widget=forms.Select(attrs={
                'class': 'form-select',
                'data-placeholder': _('Select Location')
            })
        )

        # Customize date_time field
        self.fields['date_time'].required = False
        self.fields['date_time'].widget = forms.DateTimeInput(
            attrs={
                'type': 'datetime-local',
                'class': 'form-control'
            },
            format='%Y-%m-%dT%H:%M'
        )

        # Customize status field
        self.fields['status'].widget = forms.Select(
            attrs={'class': 'form-select'},
            choices=Tryout.STATUS_CHOICES
        )

        # Customize duration field
        self.fields['duration'].required = False
        self.fields['duration'].widget = forms.TimeInput(
            attrs={
                'type': 'time',
                'class': 'form-control',
                'step': '60'  # For minute precision
            }
        )

        # Customize confirmation_status
        self.fields['confirmation_status'] = forms.BooleanField(
            required=False,
            initial=False,
            widget=forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        )

    def clean_date_time(self):
        date_time = self.cleaned_data.get('date_time')
        if date_time:
            # Only validate future dates for new tryouts
            if not self.instance.pk and date_time < timezone.now():
                raise ValidationError(_("Cannot schedule new tryout in the past."))
        return date_time

    def clean_duration(self):
            """Validate duration and convert minutes to timedelta."""
            duration = self.cleaned_data.get('duration')

            if duration is not None:
                if duration < 1:
                    raise ValidationError(_("Duration must be at least 1 minute."))
                if duration > 480:  # 8 hours
                    raise ValidationError(_("Duration cannot exceed 480 minutes (8 hours)."))

                # Convert minutes to timedelta
                return timedelta(minutes=duration)

            return duration

    def clean(self):
        cleaned_data = super().clean()
        status = cleaned_data.get('status')
        confirmation_status = cleaned_data.get('confirmation_status', False)

        if status == 'completed' and not confirmation_status:
            self.add_error('confirmation_status',
                _("Completed tryouts must be confirmed."))

        return cleaned_data

class TryoutAttachmentForm(forms.ModelForm):
    class Meta:
        model = TryoutAttachment
        fields = ['attachment']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['attachment'].required = False  # Make attachment not required

        self.fields['attachment'].widget = forms.FileInput(
            attrs={
                'class': 'form-control',
                'accept': 'image/*,.pdf,.doc,.docx',
                'data-max-size': '5242880'  # 5MB in bytes
            }
        )

    def clean_attachment(self):
        """Validate file type and size."""
        attachment = self.cleaned_data.get('attachment')
        if attachment:
            # Check file size (5MB limit)
            if attachment.size > 5 * 1024 * 1024:
                raise ValidationError(_("File size cannot exceed 5MB."))

            # Check file extension
            valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx']
            ext = attachment.name.lower()[-5:]
            if not any(ext.endswith(e) for e in valid_extensions):
                raise ValidationError(_("Invalid file type. Allowed types: images, PDF, DOC, DOCX"))

        return attachment

# Form for filtering tryouts
class TryoutFilterForm(forms.Form):
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search tryouts...'),
        })
    )

    status = forms.ChoiceField(
        required=False,
        choices=[('', _('All Status'))] + list(Tryout.STATUS_CHOICES),
        widget=forms.Select(attrs={
            'class': 'form-select',
        })
    )

    location = forms.ModelChoiceField(
        required=False,
        queryset=TryoutLocation.objects.all(),
        empty_label=_("All Locations"),
        widget=forms.Select(attrs={
            'class': 'form-select',
        })
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-control',
        })
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-control',
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')

        if date_from and date_to and date_from > date_to:
            raise forms.ValidationError(_('End date must be after start date'))

        return cleaned_data

# Formset for multiple attachments
TryoutAttachmentFormSet = forms.inlineformset_factory(
    Tryout,
    TryoutAttachment,
    form=TryoutAttachmentForm,
    extra=1,
    can_delete=True,
    max_num=5,
    validate_max=True,
    fields=['attachment'],
)

# Optional: Bulk Action Form
class TryoutBulkActionForm(forms.Form):
    """Form for bulk actions on tryouts."""

    ACTION_CHOICES = [
        ('', _('Select Action')),
        ('mark_completed', _('Mark as Completed')),
        ('mark_cancelled', _('Mark as Cancelled')),
        ('delete', _('Delete Selected'))
    ]

    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        required=True,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )

    selected_tryouts = forms.MultipleChoiceField(
        required=True,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'form-check-input'
        })
    )

    def clean_action(self):
        action = self.cleaned_data.get('action')
        if not action:
            raise ValidationError(_("Please select an action."))
        return action

from django import forms
# Adjust the import path according to your project structure
from accounts.models import CustomUser
from .models import Task

from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import Task, Case, WorkflowStage

from django import forms
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from .models import Task, Case, WorkflowStage
# from notifications.models import Notification # Temporarily commented out due to migration issues

from django import forms
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from .models import Task, Case, WorkflowStage

# Base task validation form
class BaseTaskValidationForm(forms.ModelForm):
    """
    Abstract base form for shared task validation logic.

    This ensures consistent validation across all task-related forms.
    """
    class Meta:
        abstract = True

    def clean(self):
        cleaned_data = super().clean()

        # Validate deadline if present
        deadline = cleaned_data.get('deadline')
        if deadline and deadline < timezone.now():
            self.add_error('deadline', _('Deadline cannot be in the past'))

        return cleaned_data

class TaskForm(BaseTaskValidationForm):
    """Form for creating and updating tasks with robust validation."""

    case = forms.ModelChoiceField(
        queryset=Case.objects.exclude(status__in=['completed', 'cancelled']),
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select Case')
        })
    )

    case_item = forms.ModelChoiceField(
        queryset=CaseItem.objects.all(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select Case Item')
        })
    )

    workflow_stage = forms.ModelChoiceField(
        queryset=WorkflowStage.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select Stage')
        })
    )

    assigned_to = forms.ModelChoiceField(
        queryset=None,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Assign to')
        })
    )

    title = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter task title')
        })
    )

    description = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': _('Describe the task...')
        })
    )

    priority = forms.ChoiceField(
        choices=Task.PRIORITY_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-select',
        })
    )

    status = forms.ChoiceField(
        choices=Task.STATUS_CHOICES,
        initial='pending',
        widget=forms.Select(attrs={
            'class': 'form-select',
        })
    )

    estimated_duration = forms.DurationField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'HH:MM:SS'
        })
    )

    # scheduled_start_time and scheduled_end_time fields removed
    # scheduling is now handled through ScheduleItem model

    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': _('Additional notes...')
        })
    )

    attachments = forms.FileField(
        required=False,
        widget=forms.ClearableFileInput(attrs={
            'class': 'form-control'
        })
    )

    required_skills = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        initial=dict
    )

    required_equipment = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        initial=dict
    )

    quality_checklist = forms.JSONField(
        required=False,
        widget=forms.HiddenInput(),
        initial=dict
    )
    status = forms.ChoiceField(
        choices=Task.STATUS_CHOICES,
        initial='pending',
        widget=forms.Select(attrs={
            'class': 'form-select select2',
            'data-placeholder': _('Select Status')
        })
    )

    class Meta:
        model = Task

        fields = [
            'case', 'case_item', 'title', 'description', 'workflow_stage',
            'assigned_to', 'status', 'priority', 'estimated_duration',
            'notes', 'attachments', 'required_skills', 'required_equipment',
            'quality_checklist'
        ]

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        if not self.user:
            raise ValueError(_('User must be provided'))

        super().__init__(*args, **kwargs)

        # Filtro cases dhe workflow stages bazuar në departamentet e userit
        if self.user and not self.user.is_superuser:
            department_ids = self.user.departments.values_list('id', flat=True)
            self.fields['case'].queryset = Case.objects.filter(
                responsible_department_id__in=department_ids
            ).exclude(status__in=['completed', 'cancelled'])

            self.fields['workflow_stage'].queryset = WorkflowStage.objects.filter(
                department_id__in=department_ids
            )

            self.fields['assigned_to'].queryset = self.user._meta.model.objects.filter(
                departments__id__in=department_ids,
                is_active=True
            ).distinct()
        else:
            self.fields['assigned_to'].queryset = self.user._meta.model.objects.filter(
                is_active=True
            ).distinct()

        # Filter case_items based on the selected case
        case_id = None
        if self.data.get('case'):
            try:
                case_id = int(self.data.get('case'))
            except (ValueError, TypeError):
                pass
        elif self.instance.case_id:
            case_id = self.instance.case_id
        elif 'initial' in kwargs and kwargs['initial'].get('case'):
            case_id = kwargs['initial']['case'].id if hasattr(kwargs['initial']['case'], 'id') else kwargs['initial']['case']

        if case_id:
            self.fields['case_item'].queryset = CaseItem.objects.filter(case_id=case_id)
        else:
            self.fields['case_item'].queryset = CaseItem.objects.none()

        # Vendos vlerat fillestare për status
        if not self.instance.pk:
            self.fields['status'].initial = 'pending'

    def clean(self):
        cleaned_data = super().clean()
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)

        if not instance.pk:  # New task
            instance.created_by = self.user
            if not instance.status:  # Nëse nuk është vendosur status
                instance.status = 'pending'

        # Set case_item if provided
        case_item = self.cleaned_data.get('case_item')
        if case_item:
            instance.case_item = case_item

        if commit:
            instance.save()

            if instance.assigned_to:
                # Krijo njoftimin
                # Temporarily commented out due to migration issues
                # from notifications.models import Notification
                # Notification.objects.create(
                #     user=instance.assigned_to,
                #     title=_('New Task Assignment'),
                #     message=_('You have been assigned to task: {}').format(instance.title),
                #     notification_type='task_assignment',
                #     reference_id=instance.id
                # )
                # Log instead
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"Would create notification for user {instance.assigned_to} about task assignment")

        return instance

class DentistSimpleCaseItemForm(forms.ModelForm):
    class Meta:
        model = CaseItem
        fields = ['item', 'quantity', 'unit']

DentistSimpleCaseItemFormSet = modelformset_factory(
    CaseItem,
    form=DentistSimpleCaseItemForm,
    extra=1,
    can_delete=True
)

from django import forms
from django.utils import timezone
from datetime import timedelta

class DateRangeForm(forms.Form):
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control flatpickr',
            'placeholder': 'Select start date'
        })
    )
    page_size = forms.ChoiceField(
        choices=[(25, '25'), (50, '50'), (100, '100')],
        required=False,
        initial=25,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    def clean_start_date(self):
        start_date = self.cleaned_data.get('start_date')
        if start_date:
            # Nuk lejon data në të ardhmen
            if start_date > timezone.now().date():
                raise forms.ValidationError("Start date cannot be in the future")
            # Nuk lejon data më të vjetra se 1 vit
            if start_date < (timezone.now().date() - timedelta(days=365)):
                raise forms.ValidationError("Start date cannot be older than one year")
        return start_date

    def clean_page_size(self):
        page_size = self.cleaned_data.get('page_size')
        try:
            page_size = int(page_size)
            if page_size not in [25, 50, 100]:
                return 25
        except (TypeError, ValueError):
            return 25
        return page_size


# Form for filtering cases
class CaseFilterForm(forms.Form):
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search cases...'),
        })
    )

    status = forms.ChoiceField(
        required=False,
        choices=[('', _('All Statuses'))] + list(Case.STATUS_CHOICES),
        widget=forms.Select(attrs={
            'class': 'form-select',
        })
    )

    priority = forms.ChoiceField(
        required=False,
        choices=[('', _('All Priorities'))] + list(Case.PRIORITY_CHOICES),
        widget=forms.Select(attrs={
            'class': 'form-select',
        })
    )

    department = forms.ModelChoiceField(
        required=False,
        queryset=Department.objects.filter(is_active=True),
        empty_label=_('All Departments'),
        widget=forms.Select(attrs={
            'class': 'form-select',
        })
    )

    case_number = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Case Number'),
        })
    )

    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-control',
        })
    )

    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-control',
        })
    )

    page_size = forms.ChoiceField(
        required=False,
        choices=[(10, '10'), (25, '25'), (50, '50'), (100, '100')],
        initial=10,
        widget=forms.Select(attrs={
            'class': 'form-select',
        })
    )
