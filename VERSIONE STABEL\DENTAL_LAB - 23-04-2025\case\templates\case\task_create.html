{% extends 'base.html' %}

{% block title %}Create New Task{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
/* Modern Color Scheme */
:root {
    --primary: #4f46e5;
    --primary-light: #6366f1;
    --primary-dark: #4338ca;
    --secondary: #64748b;
    --success: #22c55e;
    --success-light: #86efac;
    --danger: #ef4444;
    --danger-light: #fca5a5;
    --warning: #f59e0b;
    --warning-light: #fcd34d;
    --info: #3b82f6;
    --info-light: #93c5fd;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
}

/* Card and Layout Styles */
.task-create-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.task-form-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 15px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.task-form-card:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.form-header {
    background: linear-gradient(to right, var(--primary-dark), var(--primary));
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    color: white;
}

.form-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-content {
    padding: 2rem;
}

/* Section Styles */
.form-section {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.form-section:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--gray-200);
}

.section-header i {
    color: var(--primary);
    font-size: 1.25rem;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-700);
    margin: 0;
}

/* Grid Layout */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Form Controls */
.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-control, .select2-container--default .select2-selection--single {
    width: 100%;
    padding: 0.625rem 0.875rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.5rem;
    background-color: white;
    transition: all 0.2s;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    outline: none;
    transform: translateY(-1px);
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

/* Form Actions */
.form-actions {
    padding: 1.5rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

.btn {
    padding: 0.625rem 1.25rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary);
    color: white;
    border: none;
}

.btn-primary:hover {
    background: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2);
}

.btn-secondary {
    background: white;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-50);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
}

/* Error States */
.field-error {
    color: var(--danger);
    font-size: 0.875rem;
    margin-top: 0.375rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Required Field Indicator */
.required-field::after {
    content: "*";
    color: var(--danger);
    margin-left: 0.25rem;
}

/* Select2 Customization */
.select2-container--default .select2-selection--single {
    height: auto;
    padding: 0.625rem 0.875rem;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 100%;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: var(--primary);
}

/* Field Type Styles */
.case-item-field {
    border-left: 3px solid var(--success);
    padding-left: 0.75rem;
    transition: all 0.3s ease;
}

.case-item-field:hover {
    background-color: rgba(34, 197, 94, 0.05);
}

/* Tooltip Styles */
.tooltip-icon {
    color: var(--gray-400);
    margin-left: 0.5rem;
    cursor: help;
    position: relative;
}

.tooltip-icon:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-800);
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 10;
}

/* Alert Styles */
.alert {
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    border-color: var(--info-light);
    color: var(--info);
}

.alert-success {
    background-color: rgba(34, 197, 94, 0.1);
    border-color: var(--success-light);
    color: var(--success);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: var(--warning-light);
    color: var(--warning);
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: var(--danger-light);
    color: var(--danger);
}
</style>
{% endblock %}

{% block content %}
<div class="task-create-container">
    <form method="post" enctype="multipart/form-data" class="task-form-card">
        {% csrf_token %}

        <!-- Header -->
        <div class="form-header">
            <div class="d-flex justify-content-between align-items-center w-100">
                <h1>
                    <i class="bi bi-plus-circle-fill"></i>
                    Create New Task
                </h1>
                <div>
                    <a href="{% url 'case:task_list' %}" class="btn btn-sm btn-outline-light">
                        <i class="bi bi-list-ul"></i>
                        View All Tasks
                    </a>
                </div>
            </div>
        </div>

        <div class="form-content">
            <!-- Basic Information Section -->
            <div class="form-section">
                <div class="section-header">
                    <i class="bi bi-info-circle-fill"></i>
                    <h2 class="section-title">Basic Information</h2>
                </div>

                <div class="alert alert-info mb-3">
                    <i class="bi bi-lightbulb-fill me-2"></i>
                    <strong>Tip:</strong> First select a case, then you can choose a specific case item to associate with this task.
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="required-field">Case</label>
                        <div class="input-group">
                            {{ form.case }}
                            <span class="input-group-text bg-light">
                                <i class="bi bi-folder2-open text-primary"></i>
                            </span>
                        </div>
                        <small class="text-muted">Select the case this task belongs to</small>
                        {% if form.case.errors %}
                            <div class="field-error">{{ form.case.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group case-item-field">
                        <label>
                            Case Item
                            <i class="bi bi-question-circle tooltip-icon" data-tooltip="Link this task to a specific item in the case"></i>
                        </label>
                        <div class="d-flex">
                            {{ form.case_item }}
                            <button type="button" id="test-api-btn" class="btn btn-sm btn-outline-success ms-2">
                                <i class="bi bi-arrow-repeat"></i>
                                Refresh Items
                            </button>
                        </div>
                        <small class="text-muted">Optional: Associate this task with a specific case item</small>
                        {% if form.case_item.errors %}
                            <div class="field-error">{{ form.case_item.errors }}</div>
                        {% endif %}
                        <div id="api-test-result" class="mt-2"></div>
                    </div>

                    <div class="form-group">
                        <label class="required-field">Title</label>
                        <div class="input-group">
                            {{ form.title }}
                            <span class="input-group-text bg-light">
                                <i class="bi bi-type-h1 text-primary"></i>
                            </span>
                        </div>
                        <small class="text-muted">A clear, concise title for the task</small>
                        {% if form.title.errors %}
                            <div class="field-error">{{ form.title.errors }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="form-group mt-3">
                    <label>Description</label>
                    <div class="input-group">
                        {{ form.description }}
                    </div>
                    <small class="text-muted">Detailed explanation of what needs to be done</small>
                    {% if form.description.errors %}
                        <div class="field-error">{{ form.description.errors }}</div>
                    {% endif %}
                </div>
            </div>

                        <!-- Assignment Section -->
            <div class="form-section">
                <div class="section-header">
                    <i class="bi bi-person-fill"></i>
                    <h2 class="section-title">Assignment Details</h2>
                </div>

                <div class="alert alert-info mb-3">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    <strong>Note:</strong> Assigning a task to a specific person will send them a notification.
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="required-field">
                            Workflow Stage
                            <i class="bi bi-question-circle tooltip-icon" data-tooltip="The production stage this task belongs to"></i>
                        </label>
                        <div class="input-group">
                            {{ form.workflow_stage }}
                            <span class="input-group-text bg-light">
                                <i class="bi bi-diagram-3 text-primary"></i>
                            </span>
                        </div>
                        <small class="text-muted">The workflow stage this task is part of</small>
                        {% if form.workflow_stage.errors %}
                            <div class="field-error">{{ form.workflow_stage.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label>
                            Assigned To
                            <i class="bi bi-question-circle tooltip-icon" data-tooltip="The person responsible for completing this task"></i>
                        </label>
                        <div class="input-group">
                            {{ form.assigned_to }}
                            <span class="input-group-text bg-light">
                                <i class="bi bi-person-badge text-primary"></i>
                            </span>
                        </div>
                        <small class="text-muted">Who will be responsible for this task</small>
                        {% if form.assigned_to.errors %}
                            <div class="field-error">{{ form.assigned_to.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label class="required-field">
                            Status
                            <i class="bi bi-question-circle tooltip-icon" data-tooltip="Current status of the task"></i>
                        </label>
                        <div class="input-group">
                            {{ form.status }}
                            <span class="input-group-text bg-light">
                                <i class="bi bi-flag text-primary"></i>
                            </span>
                        </div>
                        <small class="text-muted">Initial status of the task (usually 'Pending')</small>
                        {% if form.status.errors %}
                            <div class="field-error">{{ form.status.errors }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>



            <!-- Schedule Section -->
            <div class="form-section">
                <div class="section-header">
                    <i class="bi bi-calendar-fill"></i>
                    <h2 class="section-title">Priority & Duration</h2>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="required-field">
                            Priority
                            <i class="bi bi-question-circle tooltip-icon" data-tooltip="Higher priority tasks appear first in work queues"></i>
                        </label>
                        <div class="input-group">
                            {{ form.priority }}
                            <span class="input-group-text bg-light">
                                <i class="bi bi-exclamation-triangle text-warning"></i>
                            </span>
                        </div>
                        <small class="text-muted">How urgent is this task (affects scheduling)</small>
                        {% if form.priority.errors %}
                            <div class="field-error">{{ form.priority.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label class="required-field">
                            Estimated Duration
                            <i class="bi bi-question-circle tooltip-icon" data-tooltip="Format: HH:MM:SS (e.g., 01:30:00 for 1.5 hours)"></i>
                        </label>
                        <div class="input-group">
                            {{ form.estimated_duration }}
                            <span class="input-group-text bg-light">
                                <i class="bi bi-clock text-primary"></i>
                            </span>
                        </div>
                        <small class="text-muted">How long this task is expected to take (HH:MM:SS)</small>
                        {% if form.estimated_duration.errors %}
                            <div class="field-error">{{ form.estimated_duration.errors }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="alert alert-warning mt-3">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-info-circle-fill me-2 fs-4"></i>
                        <div>
                            <strong>Important:</strong> Task scheduling is now handled through the Scheduling module.<br>
                            After creating this task, you can schedule it in the Calendar view by going to the Scheduling section.
                        </div>
                    </div>
                    <div class="mt-2 text-end">
                        <a href="{% url 'scheduling:schedule_list' %}" class="btn btn-sm btn-outline-dark">
                            <i class="bi bi-calendar-week"></i> Go to Scheduling
                        </a>
                    </div>
                </div>
            </div>

            <!-- Additional Information Section -->
            <div class="form-section">
                <div class="section-header">
                    <i class="bi bi-file-earmark-text-fill"></i>
                    <h2 class="section-title">Additional Information</h2>
                </div>

                <div class="form-group">
                    <label>
                        Notes
                        <i class="bi bi-question-circle tooltip-icon" data-tooltip="Any additional information about this task"></i>
                    </label>
                    <div class="input-group">
                        {{ form.notes }}
                    </div>
                    <small class="text-muted">Additional instructions, references, or context</small>
                    {% if form.notes.errors %}
                        <div class="field-error">{{ form.notes.errors }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label>
                        Attachments
                        <i class="bi bi-question-circle tooltip-icon" data-tooltip="Upload files related to this task (images, documents, etc.)"></i>
                    </label>
                    <div class="input-group">
                        {{ form.attachments }}
                    </div>
                    <small class="text-muted">Upload relevant files (images, documents, etc.)</small>
                    {% if form.attachments.errors %}
                        <div class="field-error">{{ form.attachments.errors }}</div>
                    {% endif %}
                </div>

                <div class="alert alert-info mt-3">
                    <i class="bi bi-lightbulb-fill me-2"></i>
                    <strong>Tip:</strong> You can add more detailed requirements and quality checklists after creating the task.
                </div>
            </div>

            <!-- Hidden Fields -->
            {{ form.required_skills }}
            {{ form.required_equipment }}
            {{ form.quality_checklist }}
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <div>
                <a href="{% url 'case:task_list' %}" class="btn btn-secondary">
                    <i class="bi bi-x-lg"></i>
                    Cancel
                </a>
            </div>
            <div>
                <button type="reset" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-counterclockwise"></i>
                    Reset Form
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-lg"></i>
                    Create Task
                </button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips if Bootstrap 5 is available
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Initialize Select2 with custom styling
    $('.select2').select2({
        theme: 'classic',
        width: '100%',
        placeholder: 'Select an option',
        allowClear: true
    }).on('select2:open', function() {
        // Add animation when dropdown opens
        document.querySelector('.select2-dropdown').classList.add('animate__animated', 'animate__fadeIn');
    });

    // Add test API button functionality with improved UX
    $(document).on('click', '#test-api-btn', function() {
        const btn = $(this);
        const originalText = btn.html();
        const caseId = $('#id_case').val();
        const resultDiv = $('#api-test-result');
        const caseItemSelect = $('#id_case_item');

        // Show loading state
        btn.html('<i class="bi bi-arrow-repeat spin"></i> Loading...');
        btn.prop('disabled', true);

        if (!caseId) {
            resultDiv.html('<div class="alert alert-warning"><i class="bi bi-exclamation-triangle me-2"></i>Please select a case first</div>');
            btn.html(originalText);
            btn.prop('disabled', false);
            return;
        }

        resultDiv.html('<div class="alert alert-info"><i class="bi bi-hourglass-split me-2"></i>Loading case items...</div>');

        const apiUrl = `/case/api/case/${caseId}/items/`;

        $.ajax({
            url: apiUrl,
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                // Clear current options
                caseItemSelect.empty().append('<option value="">Select Case Item</option>');

                // Add options for each case item
                if (data && data.length > 0) {
                    $.each(data, function(index, item) {
                        const statusBadge = getStatusBadge(item.status);
                        caseItemSelect.append(`<option value="${item.id}">${item.item_name} (${item.quantity}) ${statusBadge}</option>`);
                    });
                    resultDiv.html(`<div class="alert alert-success"><i class="bi bi-check-circle me-2"></i>Found ${data.length} items for this case</div>`);
                } else {
                    caseItemSelect.append('<option value="" disabled>No items found for this case</option>');
                    resultDiv.html(`<div class="alert alert-warning"><i class="bi bi-exclamation-triangle me-2"></i>No items found for this case</div>`);
                }

                // Refresh Select2
                caseItemSelect.trigger('change');

                // Restore button state
                btn.html(originalText);
                btn.prop('disabled', false);
            },
            error: function(xhr, status, error) {
                resultDiv.html(`<div class="alert alert-danger"><i class="bi bi-x-circle me-2"></i>Failed to load items: ${status}</div>`);
                caseItemSelect.append('<option value="" disabled>Error loading items</option>');
                console.error('API error:', xhr.responseText);

                // Restore button state
                btn.html(originalText);
                btn.prop('disabled', false);
            }
        });
    });

    // Helper function to get status badge HTML
    function getStatusBadge(status) {
        const statusColors = {
            'pending': 'secondary',
            'in_progress': 'primary',
            'completed': 'success',
            'cancelled': 'danger'
        };
        const color = statusColors[status] || 'secondary';
        return `<span class="badge bg-${color}">${status}</span>`;
    }

    // Update case_item dropdown when case is selected
    $('#id_case').on('change', function() {
        const caseId = $(this).val();
        const caseItemSelect = $('#id_case_item');
        const resultDiv = $('#api-test-result');

        // Clear current options and show loading message
        caseItemSelect.empty().append('<option value="">Loading items...</option>');
        resultDiv.html('<div class="alert alert-info"><i class="bi bi-hourglass-split me-2"></i>Loading case items...</div>');

        if (caseId) {
            const apiUrl = `/case/api/case/${caseId}/items/`;

            // Fetch case items for the selected case
            $.ajax({
                url: apiUrl,
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    // Clear loading option
                    caseItemSelect.empty().append('<option value="">Select Case Item</option>');

                    // Add options for each case item
                    if (data && data.length > 0) {
                        $.each(data, function(index, item) {
                            const statusBadge = getStatusBadge(item.status);
                            caseItemSelect.append(`<option value="${item.id}">${item.item_name} (${item.quantity}) ${statusBadge}</option>`);
                        });
                        resultDiv.html(`<div class="alert alert-success"><i class="bi bi-check-circle me-2"></i>Found ${data.length} items for this case</div>`);
                    } else {
                        caseItemSelect.append('<option value="" disabled>No items found for this case</option>');
                        resultDiv.html(`<div class="alert alert-warning"><i class="bi bi-exclamation-triangle me-2"></i>No items found for this case</div>`);
                    }

                    // Refresh Select2
                    caseItemSelect.trigger('change');
                },
                error: function(xhr, status, error) {
                    caseItemSelect.empty().append('<option value="">Select Case Item</option>');
                    caseItemSelect.append('<option value="" disabled>Error loading items</option>');
                    resultDiv.html(`<div class="alert alert-danger"><i class="bi bi-x-circle me-2"></i>Failed to load items: ${status}</div>`);
                    console.error('API error:', xhr.responseText);
                }
            });
        } else {
            // No case selected
            caseItemSelect.empty().append('<option value="">Select Case Item</option>');
            resultDiv.html('<div class="alert alert-warning"><i class="bi bi-exclamation-triangle me-2"></i>Please select a case first</div>');
        }
    });

    // Priority Color Coding with animation
    const prioritySelect = document.querySelector('[name="priority"]');
    if (prioritySelect) {
        const updatePriorityStyle = () => {
            const value = prioritySelect.value;
            // Remove all existing classes
            prioritySelect.className = 'form-control';

            // Add appropriate classes based on priority
            switch(value) {
                case '4': // Urgent
                    prioritySelect.classList.add('bg-danger', 'text-white');
                    prioritySelect.style.fontWeight = 'bold';
                    break;
                case '3': // High
                    prioritySelect.classList.add('bg-warning');
                    prioritySelect.style.fontWeight = 'bold';
                    break;
                case '2': // Medium
                    prioritySelect.classList.add('bg-info', 'text-white');
                    prioritySelect.style.fontWeight = 'normal';
                    break;
                case '1': // Low
                    prioritySelect.classList.add('bg-success', 'text-white');
                    prioritySelect.style.fontWeight = 'normal';
                    break;
            }

            // Add subtle animation
            prioritySelect.animate([{opacity: 0.7}, {opacity: 1}], {
                duration: 300,
                easing: 'ease-in-out'
            });
        };

        prioritySelect.addEventListener('change', updatePriorityStyle);
        // Initial styling
        updatePriorityStyle();
    }

    // Enhanced Form Validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('.required-field');

        // Check all required fields
        requiredFields.forEach(label => {
            const inputId = label.getAttribute('for');
            const input = document.getElementById(inputId) || label.closest('.form-group').querySelector('input, select, textarea');

            if (input && (!input.value || input.value.trim() === '')) {
                isValid = false;
                input.classList.add('is-invalid');

                // Create or update error message
                let errorDiv = input.nextElementSibling;
                if (!errorDiv || !errorDiv.classList.contains('field-error')) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'field-error';
                    input.parentNode.insertBefore(errorDiv, input.nextSibling);
                }
                errorDiv.textContent = 'This field is required';
            }
        });

        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();

            // Scroll to first error
            const firstError = document.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }

            // Show error message at top of form
            const formContent = document.querySelector('.form-content');
            const errorAlert = document.createElement('div');
            errorAlert.className = 'alert alert-danger mb-4';
            errorAlert.innerHTML = '<i class="bi bi-exclamation-triangle-fill me-2"></i>Please fix the errors in the form before submitting.';
            formContent.insertBefore(errorAlert, formContent.firstChild);

            // Remove error message after 5 seconds
            setTimeout(() => {
                errorAlert.remove();
            }, 5000);
        }
    });

    // Clear validation errors when input changes
    form.querySelectorAll('input, select, textarea').forEach(input => {
        input.addEventListener('input', function() {
            this.classList.remove('is-invalid');
            const errorDiv = this.nextElementSibling;
            if (errorDiv && errorDiv.classList.contains('field-error')) {
                errorDiv.textContent = '';
            }
        });
    });

    // Add CSS for spinning icon
    const style = document.createElement('style');
    style.textContent = `
        .spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}