"""
Business logic services for Case Management.

This module contains service classes that encapsulate business rules and operations,
keeping views and forms focused on presentation and validation respectively.
"""
import logging
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.db import transaction
from django.db.models import Q
from django.contrib.auth import get_user_model

# from notifications.models import Notification # Temporarily commented out due to migration issues
from .models import Case, Task, WorkflowStage, StageHistory, Department, Tooth

logger = logging.getLogger(__name__)

User = get_user_model()


class CaseService:
    """Encapsulates business logic for Case operations."""

    @staticmethod
    def get_dentist_cases(user):
        """Get cases for a dentist user."""
        if not hasattr(user, 'dentist_profile'):
            return Case.objects.none()

        return Case.objects.filter(
            Q(dentist=user.dentist_profile) |
            Q(dentist_user=user)
        ).order_by('-received_date_time')

    @staticmethod
    def update_case_status(case, new_status, user=None):
        """
        Update a case's status with proper logging and notifications.

        Args:
            case: The Case instance to update
            new_status: The new status value
            user: The user making the change (for audit)

        Returns:
            Updated Case instance
        """
        old_status = case.status
        if old_status == new_status:
            return case

        case.status = new_status
        case.save(update_fields=['status'])

        # Log the status change
        logger.info(f"Case #{case.case_number} status changed from {old_status} to {new_status}")

        # Create notifications for relevant users
        if user and user != case.dentist_user:
            # Notification.objects.create(
            #     user=case.dentist_user,
            #     title=_('Case Status Updated'),
            #     message=_(f'Case #{case.case_number} status changed to {case.get_status_display()}'),
            #     notification_type='case_status_change',
            #     reference_id=case.case_number
            # )
            # Temporarily commented out due to migration issues
            logger.info(f"Would create notification for user {case.dentist_user} about case status change")

        return case

    @staticmethod
    def change_case_department(case, new_department, user=None):
        """Change a case's responsible department with proper logging."""
        if case.responsible_department == new_department:
            return case

        old_department = case.responsible_department
        case.responsible_department = new_department

        # If we're changing departments, we might need to update the current stage
        if new_department:
            # Get the first stage for this department in the workflow
            if case.workflow_template:
                first_stage = case.workflow_template.stages.filter(
                    department=new_department
                ).order_by('order').first()

                if first_stage:
                    case.current_stage = first_stage

        case.save(update_fields=['responsible_department', 'current_stage'])

        # Log the change
        logger.info(
            f"Case #{case.case_number} department changed from "
            f"{old_department} to {new_department} by {user or 'system'}"
        )

        return case

    @staticmethod
    def assign_teeth_to_case(case, teeth_numbers):
        """
        Assign teeth to a case by their numbers.

        Args:
            case: The Case instance
            teeth_numbers: List of int tooth numbers

        Returns:
            Updated Case instance
        """
        with transaction.atomic():
            # Clear existing teeth
            case.selected_teeth.clear()

            # Add new teeth
            for number in teeth_numbers:
                tooth, _ = Tooth.objects.get_or_create(
                    tooth_number=number,
                    defaults={'tooth_name': str(number)}
                )
                case.selected_teeth.add(tooth)

        return case


class TaskService:
    """Encapsulates business logic for Task operations."""

    @staticmethod
    def get_user_tasks(user):
        """Get tasks assigned to a user or in their departments."""
        if user.is_superuser:
            return Task.objects.all()

        user_departments = user.departments.all()
        return Task.objects.filter(
            Q(assigned_to=user) |
            Q(case__responsible_department__in=user_departments)
        ).distinct().order_by('-priority', 'deadline')

    @staticmethod
    def start_task(task, user):
        """
        Start a task if it's in a startable state.

        Args:
            task: The Task instance to start
            user: The user starting the task

        Returns:
            (success, message) tuple
        """
        if task.status not in ['pending', 'paused', 'blocked']:
            return False, _("Task cannot be started from its current status.")

        if task.assigned_to and task.assigned_to != user:
            return False, _("This task is assigned to someone else.")

        # Update task status and timestamps
        task.status = 'in_progress'
        task.actual_start_time = timezone.now()

        # If no one is assigned, assign the current user
        if not task.assigned_to:
            task.assigned_to = user

        task.save(update_fields=['status', 'actual_start_time', 'assigned_to'])

        # Log the action
        logger.info(f"Task #{task.id} started by {user}")

        return True, _("Task started successfully.")

    @staticmethod
    def complete_task(task, user):
        """
        Complete a task if it's in a completable state.

        Args:
            task: The Task instance to complete
            user: The user completing the task

        Returns:
            (success, message) tuple
        """
        if task.status != 'in_progress':
            return False, _("Only in-progress tasks can be completed.")

        if task.assigned_to and task.assigned_to != user:
            return False, _("This task is assigned to someone else.")

        # Update task status and timestamps
        task.status = 'completed'
        task.actual_end_time = timezone.now()
        task.progress = 100

        # Calculate actual duration
        if task.actual_start_time:
            task.actual_duration = task.actual_end_time - task.actual_start_time

        task.save(update_fields=['status', 'actual_end_time', 'progress', 'actual_duration'])

        # Log the action
        logger.info(f"Task #{task.id} completed by {user}")

        # Notify case owner
        if task.case and task.case.dentist_user:
            # Notification.objects.create(
            #     user=task.case.dentist_user,
            #     title=_('Task Completed'),
            #     message=_(f'Task "{task.title}" for Case #{task.case.case_number} has been completed.'),
            #     notification_type='task_completed',
            #     reference_id=task.id
            # )
            # Temporarily commented out due to migration issues
            logger.info(f"Would create notification for user {task.case.dentist_user} about task completion")

        return True, _("Task completed successfully.")

    @staticmethod
    def assign_task(task, assigned_to, user):
        """
        Assign a task to a user.

        Args:
            task: The Task instance to assign
            assigned_to: The User to assign the task to
            user: The User making the assignment

        Returns:
            (success, message) tuple
        """
        if task.assigned_to == assigned_to:
            return True, _("Task already assigned to this user.")

        old_assigned_to = task.assigned_to
        task.assigned_to = assigned_to
        task.save(update_fields=['assigned_to'])

        # Log the action
        logger.info(f"Task #{task.id} reassigned from {old_assigned_to} to {assigned_to} by {user}")

        # Notify the newly assigned user
        if assigned_to:
            # Notification.objects.create(
            #     user=assigned_to,
            #     title=_('Task Assignment'),
            #     message=_(f'You have been assigned to task "{task.title}" for Case #{task.case.case_number}'),
            #     notification_type='task_assignment',
            #     reference_id=task.id
            # )
            # Temporarily commented out due to migration issues
            logger.info(f"Would create notification for user {assigned_to} about task assignment")

        return True, _("Task assigned successfully.")


class SchedulingService:
    """Enhanced scheduling service with advanced algorithms"""

    @staticmethod
    def create_optimized_schedule(case, optimization_strategy='balanced', user=None):
        """
        Create an optimized schedule for a case using advanced scheduling engine

        Args:
            case: The Case instance to schedule
            optimization_strategy: 'speed', 'quality', 'balanced', 'resource_efficient'
            user: The user creating the schedule

        Returns:
            (success, message, schedule) tuple
        """
        try:
            from common.scheduling_engine import scheduling_engine

            # Use advanced scheduling engine
            result = scheduling_engine.schedule_case(case, optimization_strategy)

            if result.success:
                logger.info(f"Optimized schedule created for case #{case.case_number} with score {result.optimization_score:.1f}%")

                # Update case status if needed
                if case.status == 'pending_acceptance':
                    CaseService.update_case_status(case, 'in_progress', user=user, reason="Schedule created")

                return True, result.message, result.schedule_id
            else:
                logger.warning(f"Failed to create optimized schedule for case #{case.case_number}: {result.message}")
                return False, result.message, None

        except Exception as e:
            logger.error(f"Error creating optimized schedule: {e}", exc_info=True)
            return False, f"Scheduling error: {str(e)}", None

    @staticmethod
    def analyze_department_capacity(department, days_ahead=14):
        """
        Analyze department capacity and utilization

        Args:
            department: The Department instance to analyze
            days_ahead: Number of days to analyze

        Returns:
            Dictionary with capacity analysis
        """
        try:
            from common.scheduling_engine import capacity_planner
            from datetime import date, timedelta

            start_date = date.today()
            end_date = start_date + timedelta(days=days_ahead)

            analysis = capacity_planner.analyze_department_capacity(department, start_date, end_date)

            logger.info(f"Capacity analysis completed for department {department.name}")
            return analysis

        except Exception as e:
            logger.error(f"Error analyzing department capacity: {e}", exc_info=True)
            return {'error': str(e)}

    @staticmethod
    def check_resource_availability(department=None, start_time=None, end_time=None):
        """
        Check real-time resource availability

        Args:
            department: Department to check (optional)
            start_time: Start of availability window
            end_time: End of availability window

        Returns:
            List of available resources
        """
        try:
            from common.resource_management import resource_manager

            department_id = department.id if department else None

            # Check user availability
            user_availability = resource_manager.check_real_time_availability(
                resource_type='user',
                department_id=department_id,
                start_time=start_time,
                end_time=end_time
            )

            # Check department availability
            dept_availability = resource_manager.check_real_time_availability(
                resource_type='department',
                department_id=department_id,
                start_time=start_time,
                end_time=end_time
            )

            return {
                'users': user_availability,
                'departments': dept_availability,
                'timestamp': timezone.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error checking resource availability: {e}", exc_info=True)
            return {'error': str(e)}

    @staticmethod
    def detect_scheduling_conflicts(start_date=None, end_date=None):
        """
        Detect scheduling conflicts in the given period

        Args:
            start_date: Start date for conflict detection
            end_date: End date for conflict detection

        Returns:
            List of detected conflicts
        """
        try:
            from common.resource_management import resource_manager
            from datetime import date, timedelta

            if start_date is None:
                start_date = date.today()
            if end_date is None:
                end_date = start_date + timedelta(days=7)  # Next week

            conflicts = resource_manager.detect_resource_conflicts(start_date, end_date)

            logger.info(f"Conflict detection completed: {len(conflicts)} conflicts found")
            return conflicts

        except Exception as e:
            logger.error(f"Error detecting scheduling conflicts: {e}", exc_info=True)
            return []

    @staticmethod
    def balance_workload_across_departments(days_ahead=14):
        """
        Analyze and balance workload across departments

        Args:
            days_ahead: Number of days to analyze

        Returns:
            Workload balance analysis
        """
        try:
            from common.scheduling_engine import capacity_planner
            from datetime import date, timedelta

            start_date = date.today()
            end_date = start_date + timedelta(days=days_ahead)

            balance_analysis = capacity_planner.balance_workload_across_departments(start_date, end_date)

            logger.info("Workload balance analysis completed")
            return balance_analysis

        except Exception as e:
            logger.error(f"Error balancing workload: {e}", exc_info=True)
            return {'error': str(e)}

    @staticmethod
    def predict_capacity_needs(department, days_ahead=30):
        """
        Predict future capacity needs for a department

        Args:
            department: The Department instance
            days_ahead: Number of days to predict

        Returns:
            Capacity prediction analysis
        """
        try:
            from common.scheduling_engine import capacity_planner

            prediction = capacity_planner.predict_future_capacity_needs(department, days_ahead)

            logger.info(f"Capacity prediction completed for department {department.name}")
            return prediction

        except Exception as e:
            logger.error(f"Error predicting capacity needs: {e}", exc_info=True)
            return {'error': str(e)}

    @staticmethod
    def optimize_existing_schedule(schedule, optimization_goal='balanced'):
        """
        Optimize an existing schedule

        Args:
            schedule: The Schedule instance to optimize
            optimization_goal: 'speed', 'quality', 'balanced', 'cost'

        Returns:
            Optimization results
        """
        try:
            from common.resource_management import resource_manager

            # Get schedule items
            schedule_items = schedule.items.all() if hasattr(schedule, 'items') else []

            if not schedule_items:
                return {'error': 'No schedule items to optimize'}

            # Optimize resource allocation
            optimization_results = resource_manager.optimize_resource_allocation(
                schedule_items, optimization_goal
            )

            logger.info(f"Schedule optimization completed with {optimization_results.get('optimization_score', 0):.1f}% score")
            return optimization_results

        except Exception as e:
            logger.error(f"Error optimizing schedule: {e}", exc_info=True)
            return {'error': str(e)}

    @staticmethod
    def generate_utilization_report(days_back=30):
        """
        Generate comprehensive resource utilization report

        Args:
            days_back: Number of days to analyze

        Returns:
            Utilization report
        """
        try:
            from common.resource_management import resource_manager
            from datetime import date, timedelta

            end_date = date.today()
            start_date = end_date - timedelta(days=days_back)

            report = resource_manager.get_resource_utilization_report(start_date, end_date)

            logger.info("Resource utilization report generated")
            return report

        except Exception as e:
            logger.error(f"Error generating utilization report: {e}", exc_info=True)
            return {'error': str(e)}
