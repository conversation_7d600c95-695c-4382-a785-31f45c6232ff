"""
Custom exceptions for the Dental Lab System
"""

from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import logging

logger = logging.getLogger(__name__)


class DentalLabException(Exception):
    """Base exception for all dental lab specific errors"""
    
    def __init__(self, message, error_code=None, details=None):
        self.message = message
        self.error_code = error_code or 'DENTAL_LAB_ERROR'
        self.details = details or {}
        super().__init__(self.message)
        
    def to_dict(self):
        """Convert exception to dictionary for API responses"""
        return {
            'error': True,
            'error_code': self.error_code,
            'message': str(self.message),
            'details': self.details
        }


class BusinessRuleViolation(DentalLabException):
    """Raised when business rules are violated"""
    
    def __init__(self, message, rule_name=None, details=None):
        super().__init__(
            message, 
            error_code='BUSINESS_RULE_VIOLATION',
            details=details or {}
        )
        self.rule_name = rule_name


class DataIntegrityError(DentalLabException):
    """Raised when data integrity is compromised"""
    
    def __init__(self, message, entity=None, entity_id=None, details=None):
        super().__init__(
            message,
            error_code='DATA_INTEGRITY_ERROR',
            details=details or {}
        )
        self.entity = entity
        self.entity_id = entity_id


class ValidationError(DentalLabException):
    """Enhanced validation error with field-specific details"""
    
    def __init__(self, message, field_errors=None, details=None):
        super().__init__(
            message,
            error_code='VALIDATION_ERROR',
            details=details or {}
        )
        self.field_errors = field_errors or {}
        
    def to_dict(self):
        """Convert to dictionary with field errors"""
        result = super().to_dict()
        if self.field_errors:
            result['field_errors'] = self.field_errors
        return result


class FinancialError(DentalLabException):
    """Raised for financial operation errors"""
    
    def __init__(self, message, operation=None, amount=None, currency=None, details=None):
        super().__init__(
            message,
            error_code='FINANCIAL_ERROR',
            details=details or {}
        )
        self.operation = operation
        self.amount = amount
        self.currency = currency


class WorkflowError(DentalLabException):
    """Raised for workflow-related errors"""
    
    def __init__(self, message, case_id=None, current_stage=None, target_stage=None, details=None):
        super().__init__(
            message,
            error_code='WORKFLOW_ERROR',
            details=details or {}
        )
        self.case_id = case_id
        self.current_stage = current_stage
        self.target_stage = target_stage


class PermissionError(DentalLabException):
    """Raised for permission-related errors"""
    
    def __init__(self, message, user=None, required_permission=None, details=None):
        super().__init__(
            message,
            error_code='PERMISSION_ERROR',
            details=details or {}
        )
        self.user = user
        self.required_permission = required_permission


class ResourceNotFoundError(DentalLabException):
    """Raised when a required resource is not found"""
    
    def __init__(self, message, resource_type=None, resource_id=None, details=None):
        super().__init__(
            message,
            error_code='RESOURCE_NOT_FOUND',
            details=details or {}
        )
        self.resource_type = resource_type
        self.resource_id = resource_id


class CurrencyError(FinancialError):
    """Raised for currency-related errors"""
    
    def __init__(self, message, from_currency=None, to_currency=None, details=None):
        super().__init__(
            message,
            operation='currency_conversion',
            details=details or {}
        )
        self.from_currency = from_currency
        self.to_currency = to_currency
        self.error_code = 'CURRENCY_ERROR'


class SchedulingError(DentalLabException):
    """Raised for scheduling-related errors"""
    
    def __init__(self, message, schedule_id=None, resource=None, time_slot=None, details=None):
        super().__init__(
            message,
            error_code='SCHEDULING_ERROR',
            details=details or {}
        )
        self.schedule_id = schedule_id
        self.resource = resource
        self.time_slot = time_slot


class ExternalServiceError(DentalLabException):
    """Raised when external services fail"""
    
    def __init__(self, message, service_name=None, status_code=None, details=None):
        super().__init__(
            message,
            error_code='EXTERNAL_SERVICE_ERROR',
            details=details or {}
        )
        self.service_name = service_name
        self.status_code = status_code


# Exception mapping for common Django exceptions
EXCEPTION_MAPPING = {
    'ValidationError': ValidationError,
    'PermissionDenied': PermissionError,
    'ObjectDoesNotExist': ResourceNotFoundError,
    'IntegrityError': DataIntegrityError,
}


def handle_exception(exc, context=None):
    """
    Central exception handler that converts exceptions to standardized format
    """
    context = context or {}
    
    # Log the exception
    logger.error(f"Exception occurred: {exc}", exc_info=True, extra=context)
    
    # Convert to our custom exception if needed
    if isinstance(exc, DentalLabException):
        return exc
    
    # Map common Django exceptions
    exc_type = type(exc).__name__
    if exc_type in EXCEPTION_MAPPING:
        custom_exc_class = EXCEPTION_MAPPING[exc_type]
        return custom_exc_class(str(exc), details=context)
    
    # Default to generic exception
    return DentalLabException(
        str(exc),
        error_code='UNKNOWN_ERROR',
        details=context
    )
