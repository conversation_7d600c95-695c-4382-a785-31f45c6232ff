# case/models.py

from datetime import timedelta
import json
from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.conf import settings
from patients.models import Patient
from items.models import Unit

# ---------------------------
# CASE ITEM
# ---------------------------
class CaseItem(models.Model):
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled'))
    ]
    case = models.ForeignKey('Case', on_delete=models.CASCADE, related_name='case_items')
    item = models.ForeignKey('items.Item', on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1, validators=[MinValueValidator(1)])
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    estimated_production_time = models.DurationField(
        default=timedelta(hours=1),
        help_text=_("Estimated time to produce this item")
    )
    actual_production_time = models.DurationField(default=timedelta(hours=0), null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    notes = models.TextField(blank=True)
    assigned_to = models.ForeignKey('accounts.CustomUser', on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        unique_together = ['case', 'item']

    def __str__(self):
        return f"{self.quantity} x {self.item.name} for Case #{self.case.case_number}"

    def get_total_estimated_time(self):
        return self.estimated_production_time * self.quantity

    def is_delayed(self):
        if self.actual_production_time and self.estimated_production_time:
            return self.actual_production_time > self.estimated_production_time
        return False

    def get_progress_percentage(self):
        # First check if there are tasks associated with this item
        tasks = self.tasks.all()
        if tasks.exists():
            completed_tasks = tasks.filter(status='completed').count()
            return (completed_tasks / tasks.count()) * 100

        # If no tasks, check if there are schedule items directly associated
        from django.apps import apps
        ScheduleItem = apps.get_model('scheduling', 'ScheduleItem')
        schedule_items = ScheduleItem.objects.filter(case_item=self)
        if schedule_items.exists():
            completed_items = schedule_items.filter(status='completed').count()
            return (completed_items / schedule_items.count()) * 100

        # If no schedule items, use the status and production time
        if self.status == 'completed':
            return 100
        elif self.status == 'in_progress' and self.actual_production_time:
            progress = (self.actual_production_time.total_seconds() / self.estimated_production_time.total_seconds()) * 100
            return min(round(progress, 2), 99)
        return 0

    def get_scheduled_timeframe(self):
        """Returns tuple (start_time, end_time) of the overall scheduling timeframe"""
        from django.apps import apps
        ScheduleItem = apps.get_model('scheduling', 'ScheduleItem')

        # Get all schedule items related to this case item
        direct_items = ScheduleItem.objects.filter(case_item=self)

        # Get all schedule items related through tasks
        task_items = ScheduleItem.objects.filter(task_object__case_item=self)

        # Combine all items
        all_items = list(direct_items) + list(task_items)

        if not all_items:
            return None, None

        start_times = [item.start_time for item in all_items if item.start_time]
        end_times = [item.end_time for item in all_items if item.end_time]

        if not start_times or not end_times:
            return None, None

        return min(start_times), max(end_times)

    def save(self, *args, **kwargs):
        if isinstance(self.estimated_production_time, int):
            self.estimated_production_time = timedelta(minutes=self.estimated_production_time)
        super().save(*args, **kwargs)

# ---------------------------
# TOOTH & CASE TEETH
# ---------------------------
class Tooth(models.Model):
    tooth_number = models.PositiveSmallIntegerField(unique=True)
    tooth_name = models.CharField(max_length=2, default='')
    color = models.CharField(max_length=30, blank=True)

    class Meta:
        ordering = ['tooth_number']

    def __str__(self):
        return f"Tooth {self.tooth_number} ({self.tooth_name or self.tooth_number})"

    def save(self, *args, **kwargs):
        if not self.tooth_name:
            self.tooth_name = str(self.tooth_number)
        super().save(*args, **kwargs)


class CaseTeeth(models.Model):
    case = models.ForeignKey('Case', on_delete=models.CASCADE)
    tooth = models.ForeignKey(Tooth, on_delete=models.CASCADE)
    selected_at = models.DateTimeField(auto_now_add=True)
    color = models.CharField(max_length=30, blank=True)

    class Meta:
        unique_together = ['case', 'tooth']
        ordering = ['tooth__tooth_number']

    def __str__(self):
        return f"Case #{self.case.case_number} - Tooth {self.tooth.tooth_number}"

# ---------------------------
# CASE
# ---------------------------
class Case(models.Model):
    STATUS_CHOICES = [
        ('pending_acceptance', _('Pending Acceptance')),
        ('on_hold', _('On Hold')),
        ('in_progress', _('In Progress')),
        ('quality_check', _('Quality Check')),
        ('revision_needed', _('Revision Needed')),
        ('ready_to_ship', _('Ready for Shipping')),
        ('shipped', _('Shipped')),
        ('delivered', _('Delivered')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    ]
    PRIORITY_CHOICES = [
        (1, _('High')),
        (2, _('Normal')),
        (3, _('Low')),
    ]

    # Basic Information
    case_number = models.AutoField(primary_key=True)
    workflow_template = models.ForeignKey('WorkflowTemplate', on_delete=models.SET_NULL, null=True, related_name='cases')
    patient = models.ForeignKey(Patient, on_delete=models.SET_NULL, null=True, blank=True, related_name='patient_cases')
    dentist = models.ForeignKey('Dentists.Dentist', on_delete=models.PROTECT, related_name='dentist_cases')
    dentist_user = models.ForeignKey('accounts.CustomUser', on_delete=models.CASCADE, related_name='cases', null=True, blank=True)

    # Items and Teeth
    items = models.ManyToManyField('items.Item', through=CaseItem, related_name='case_set')
    selected_teeth = models.ManyToManyField(Tooth, related_name='cases', blank=True, through=CaseTeeth)
    teeth_color = models.CharField(max_length=30, blank=True)

    # Dates and Deadlines
    created_at = models.DateTimeField(auto_now_add=True)
    received_date_time = models.DateTimeField(null=True, blank=True)
    estimated_completion = models.DateTimeField(null=True, blank=True)
    actual_completion = models.DateTimeField(null=True, blank=True)
    ship_date_time = models.DateTimeField(null=True, blank=True)
    delivery_date = models.DateTimeField(null=True, blank=True)
    deadline = models.DateTimeField(null=True, blank=True)

    # Status and Progress
    status = models.CharField(max_length=30, choices=STATUS_CHOICES, default='pending_acceptance')
    status_changed_at = models.DateTimeField(auto_now_add=True)
    priority = models.PositiveSmallIntegerField(choices=PRIORITY_CHOICES, default=2)
    progress_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0)

    # Workflow Management
    current_stage = models.ForeignKey('WorkflowStage', on_delete=models.SET_NULL, null=True, blank=True, related_name='current_cases')
    next_stage = models.ForeignKey('WorkflowStage', on_delete=models.SET_NULL, null=True, blank=True, related_name='next_cases')
    stage_history = models.JSONField(default=list, help_text=_('History of stage transitions'))

    # Department and Assignment
    responsible_department = models.ForeignKey('Department', on_delete=models.SET_NULL, null=True, blank=True, related_name='responsible_cases')
    assigned_technicians = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='assigned_cases', blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='created_cases')

    # Quality and Feedback
    quality_checks = models.JSONField(default=dict, help_text=_('Quality control checkpoints'))
    patient_feedback = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    dentist_feedback = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    revision_count = models.PositiveIntegerField(default=0)

    # Time Tracking
    estimated_duration = models.DurationField(null=True, blank=True)
    actual_duration = models.DurationField(null=True, blank=True)
    delay_reason = models.TextField(blank=True)

    # Additional Information
    notes = models.TextField(blank=True)
    special_requirements = models.TextField(blank=True)
    attachments = models.FileField(upload_to='case_attachments/%Y/%m/', null=True, blank=True)
    cost_estimate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _('Case')
        verbose_name_plural = _('Cases')
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['deadline']),
            models.Index(fields=['responsible_department']),
        ]

    def __str__(self):
        return f"Case #{self.case_number} - {self.get_status_display()}"

    def clean(self):
        if self.deadline and self.deadline < timezone.now():
            raise ValidationError({'deadline': _('Deadline cannot be in the past')})
        if self.estimated_duration and self.estimated_duration.total_seconds() <= 0:
            raise ValidationError({'estimated_duration': _('Duration must be positive')})

    def save(self, *args, **kwargs):
        self.clean()
        if isinstance(self.estimated_duration, int):
            self.estimated_duration = timedelta(minutes=self.estimated_duration)
        if self.received_date_time and self.estimated_duration:
            self.estimated_completion = self.received_date_time + self.estimated_duration
        if self.pk:
            old_case = Case.objects.get(pk=self.pk)
            if old_case.status != self.status:
                self.status_changed_at = timezone.now()
                self.track_status_change(old_case.status, self.status)
        super().save(*args, **kwargs)

    def move_to_next_stage(self):
        if not self.current_stage:
            return False
        try:
            next_stage = WorkflowStage.objects.get(
                workflow=self.workflow_template,
                order=self.current_stage.order + 1
            )
            self.record_stage_completion()
            self.current_stage = next_stage
            self.next_stage = self.get_subsequent_stage()
            self.save()
            self.create_stage_tasks()
            return True
        except WorkflowStage.DoesNotExist:
            self.complete_case()
            return False

    def record_stage_completion(self):
        if not self.current_stage:
            return
        completion_data = {
            'stage_id': self.current_stage.id,
            'stage_name': self.current_stage.name,
            'completed_at': timezone.now().isoformat(),
            'duration': str(self.get_stage_duration()),
            'completed_by': str(self.get_current_assignee())
        }
        if isinstance(self.stage_history, list):
            self.stage_history.append(completion_data)
        else:
            self.stage_history = [completion_data]

    def get_stage_duration(self):
        if not self.current_stage or not self.status_changed_at:
            return timedelta(0)
        return timezone.now() - self.status_changed_at

    def complete_case(self):
        self.status = 'completed'
        self.actual_completion = timezone.now()
        if self.received_date_time:
            self.actual_duration = self.actual_completion - self.received_date_time
        self.save()

    def is_overdue(self):
        if self.deadline and self.status not in ['completed', 'cancelled', 'delivered']:
            return timezone.now() > self.deadline
        return False

    def get_total_progress(self):
        if not self.workflow_template:
            return 0
        total_stages = self.workflow_template.stages.count()
        if total_stages == 0:
            return 0
        completed_stages = len(self.stage_history) if isinstance(self.stage_history, list) else 0
        progress = (completed_stages / total_stages) * 100
        self.progress_percentage = progress
        self.save(update_fields=['progress_percentage'])
        return progress

    def track_status_change(self, old_status, new_status):
        from .models import CaseStatusHistory
        CaseStatusHistory.objects.create(
            case=self,
            old_status=old_status,
            new_status=new_status,
            changed_by=self.get_current_assignee()
        )

    def get_current_assignee(self):
        return self.assigned_technicians.first()

    def create_stage_tasks(self):
        if not self.current_stage:
            return
        for task_template in self.current_stage.task_templates.all():
            Task.objects.create(
                case=self,
                workflow_stage=self.current_stage,
                title=task_template.name,
                description=task_template.description,
                estimated_duration=task_template.estimated_duration
            )

    def get_subsequent_stage(self):
        if not self.current_stage:
            return None
        try:
            return WorkflowStage.objects.get(
                workflow=self.workflow_template,
                order=self.current_stage.order + 2
            )
        except WorkflowStage.DoesNotExist:
            return None

    @property
    def is_delayed(self):
        if self.estimated_completion and self.status not in ['completed', 'cancelled', 'delivered']:
            return timezone.now() > self.estimated_completion
        return False

    @property
    def total_cost(self):
        return self.items.aggregate(
            total=models.Sum(
                models.F('caseitem__quantity') * models.F('selling_price')
            )
        )['total'] or 0

    def get_required_departments(self):
        """Get departments required for this case based on workflow template"""
        if self.workflow_template:
            # Get departments from workflow stages
            return Department.objects.filter(
                id__in=self.workflow_template.stages.values_list('department', flat=True)
            ).distinct()
        elif self.responsible_department:
            # If no workflow template, return the responsible department
            return Department.objects.filter(id=self.responsible_department.id)
        else:
            # If no workflow template or responsible department, return all active departments
            return Department.objects.filter(is_active=True)

# ---------------------------
# CASE NOTE
# ---------------------------
class CaseNote(models.Model):
    case = models.ForeignKey(Case, on_delete=models.CASCADE)
    note = models.TextField()
    attachment = models.FileField(upload_to='case_notes', blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Note for Case #{self.case.case_number}"

# ---------------------------
# TASK
# ---------------------------
class Task(models.Model):
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('in_progress', _('In Progress')),
        ('paused', _('Paused')),
        ('completed', _('Completed')),
        ('delayed', _('Delayed')),
        ('blocked', _('Blocked')),
        ('cancelled', _('Cancelled')),
        ('review', _('Under Review'))
    ]
    PRIORITY_CHOICES = [
        (1, _('Low')),
        (2, _('Medium')),
        (3, _('High')),
        (4, _('Urgent'))
    ]
    title = models.CharField(max_length=200, verbose_name=_('Title'))
    description = models.TextField(blank=True, verbose_name=_('Description'))
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='tasks')
    case_item = models.ForeignKey(CaseItem, on_delete=models.SET_NULL, null=True, blank=True, related_name='tasks')
    workflow_stage = models.ForeignKey('WorkflowStage', on_delete=models.CASCADE, related_name='tasks')
    assigned_to = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_tasks')
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='created_tasks')
    dependencies = models.ManyToManyField('self', symmetrical=False, blank=True, related_name='dependent_tasks')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2)
    progress = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    estimated_duration = models.DurationField(help_text=_("Estimated time to complete the task"))
    actual_duration = models.DurationField(null=True, blank=True)
    actual_start_time = models.DateTimeField(null=True, blank=True)
    actual_end_time = models.DateTimeField(null=True, blank=True)
    paused_time = models.DurationField(default=timedelta(0))
    pause_started = models.DateTimeField(null=True, blank=True)
    required_skills = models.JSONField(default=dict, help_text=_('Skills required for this task'))
    required_equipment = models.JSONField(default=dict, help_text=_('Equipment needed for this task'))
    quality_checklist = models.JSONField(default=dict, help_text=_('Quality control checklist'))
    quality_check_passed = models.BooleanField(null=True, blank=True)
    notes = models.TextField(blank=True)
    attachments = models.FileField(upload_to='task_attachments/%Y/%m/', null=True, blank=True)
    blocking_issues = models.TextField(blank=True, help_text=_('Current blocking issues'))
    review_comments = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    status_history = models.JSONField(default=list, help_text=_('History of status changes'))

    class Meta:
        ordering = ['-priority', 'created_at']
        verbose_name = _('Task')
        verbose_name_plural = _('Tasks')
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['priority']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.title} - Case #{self.case.case_number}"

    def clean(self):
        if self.actual_end_time and self.actual_start_time and self.actual_end_time <= self.actual_start_time:
            raise ValidationError(_('Actual end time must be after actual start time'))

    def save(self, *args, **kwargs):
        self.clean()
        if self.pk:
            old_task = Task.objects.get(pk=self.pk)
            if old_task.status != self.status:
                self.track_status_change(old_task.status)
        if self.status == 'completed' and not self.actual_end_time:
            self.actual_end_time = timezone.now()
            if self.actual_start_time:
                self.actual_duration = self.actual_end_time - self.actual_start_time - self.paused_time
        if self.status == 'in_progress' and not self.actual_start_time:
            self.actual_start_time = timezone.now()
        self.progress = self.calculate_progress()
        super().save(*args, **kwargs)

    def start_task(self):
        if self.can_start():
            self.status = 'in_progress'
            self.actual_start_time = timezone.now()
            self.save()
            return True
        return False

    def complete_task(self):
        if self.can_complete():
            self.status = 'completed'
            self.actual_end_time = timezone.now()
            self.calculate_actual_duration()
            self.progress = 100
            self.save()
            return True
        return False

    def pause_task(self, reason=None):
        if self.status == 'in_progress':
            self.status = 'paused'
            self.pause_started = timezone.now()
            if reason:
                self.notes = f"{self.notes}\nPaused: {reason}"
            self.save()
            return True
        return False

    def resume_task(self):
        if self.status == 'paused' and self.pause_started:
            pause_duration = timezone.now() - self.pause_started
            self.paused_time += pause_duration
            self.status = 'in_progress'
            self.pause_started = None
            self.save()
            return True
        return False

    def is_delayed(self):
        # Check if task is delayed based on schedule items
        if self.status not in ['completed', 'cancelled']:
            schedule_items = self.schedule_items.all()
            if schedule_items.exists():
                latest_end_time = schedule_items.order_by('-end_time').first().end_time
                return timezone.now() > latest_end_time
        return False

    def calculate_progress(self):
        if self.status == 'completed':
            return 100
        elif self.status == 'in_progress' and self.actual_start_time:
            if self.pause_started:
                elapsed = self.pause_started - self.actual_start_time - self.paused_time
            else:
                elapsed = timezone.now() - self.actual_start_time - self.paused_time
            total_duration = self.estimated_duration.total_seconds()
            elapsed_seconds = elapsed.total_seconds()
            progress = min((elapsed_seconds / total_duration) * 100, 99)
            return round(progress, 2)
        return 0

    def calculate_actual_duration(self):
        if self.actual_start_time and self.actual_end_time:
            self.actual_duration = self.actual_end_time - self.actual_start_time - self.paused_time

    def can_start(self):
        if self.status != 'pending':
            return False
        for dependency in self.dependencies.all():
            if dependency.status != 'completed':
                return False
        return True

    def can_complete(self):
        if self.status != 'in_progress':
            return False
        if self.quality_checklist and not self.quality_check_passed:
            return False
        return True

    def track_status_change(self, old_status):
        change = {
            'from_status': old_status,
            'to_status': self.status,
            'changed_at': timezone.now().isoformat(),
            'changed_by': str(self.assigned_to) if self.assigned_to else 'System'
        }
        if isinstance(self.status_history, list):
            self.status_history.append(change)
        else:
            self.status_history = [change]

    @property
    def time_spent(self):
        if not self.actual_start_time:
            return timezone.timedelta(0)
        end_time = self.actual_end_time if self.actual_end_time else timezone.now()
        return end_time - self.actual_start_time - self.paused_time

    @property
    def efficiency_score(self):
        if self.status == 'completed' and self.actual_duration and self.estimated_duration:
            ratio = self.estimated_duration.total_seconds() / self.actual_duration.total_seconds()
            return min(round(ratio * 100, 2), 100)
        return None

    @property
    def progress_percentage(self):
        return self.calculate_progress()

# ---------------------------
# WORKFLOW TEMPLATE
# ---------------------------
class WorkflowTemplate(models.Model):
    PRIORITY_CHOICES = [(1, _('High')), (2, _('Normal')), (3, _('Low'))]
    STATUS_CHOICES = [('active', _('Active')), ('inactive', _('Inactive')), ('archived', _('Archived'))]

    name = models.CharField(max_length=100, unique=True, verbose_name=_('Template Name'), help_text=_('Name of the workflow template'))
    description = models.TextField(blank=True, verbose_name=_('Description'), help_text=_('Detailed description of the workflow template'))
    default_duration = models.DurationField(verbose_name=_('Default Duration'), help_text=_('Expected duration for this workflow'))
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2, verbose_name=_('Priority'), help_text=_('Default priority level for this workflow'))
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name=_('Status'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('Created At'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('Updated At'))
    created_by = models.ForeignKey('accounts.CustomUser', on_delete=models.SET_NULL, null=True, related_name='created_workflows', verbose_name=_('Created By'))
    is_default = models.BooleanField(default=False, verbose_name=_('Is Default Template'), help_text=_('Whether this is a default template'))
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, verbose_name=_('Estimated Cost'), help_text=_('Estimated cost for this workflow'))

    class Meta:
        ordering = ['name']
        verbose_name = _('Workflow Template')
        verbose_name_plural = _('Workflow Templates')
        indexes = [models.Index(fields=['name']), models.Index(fields=['status']), models.Index(fields=['created_at'])]

    def __str__(self):
        return f"{self.name} ({self.get_status_display()})"

    def clean(self):
        if self.default_duration.total_seconds() <= 0:
            raise ValidationError({'default_duration': _('Duration must be positive')})
        if self.is_default:
            existing_default = WorkflowTemplate.objects.filter(is_default=True).exclude(pk=self.pk)
            if existing_default.exists():
                raise ValidationError({'is_default': _('Another template is already set as default')})

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def duplicate(self, new_name=None):
        if new_name is None:
            new_name = f"{self.name} (Copy)"
        new_template = WorkflowTemplate.objects.create(
            name=new_name, description=self.description, default_duration=self.default_duration,
            priority=self.priority, status='inactive', created_by=self.created_by,
            estimated_cost=self.estimated_cost, is_default=False
        )
        return new_template

    def get_total_stages(self):
        return self.stages.count()

    def get_critical_path(self):
        stages = self.stages.all().order_by('order')
        critical_path = []
        current_duration = timedelta()
        for stage in stages:
            if stage.is_critical:
                critical_path.append(stage)
                current_duration += stage.estimated_duration
        return {'stages': critical_path, 'total_duration': current_duration}

    def is_valid_for_case(self, case):
        return True

    def activate(self):
        self.status = 'active'
        self.save()

    def deactivate(self):
        self.status = 'inactive'
        self.save()

    def archive(self):
        self.status = 'archived'
        self.save()

# ---------------------------
# WORKFLOW STAGE
# ---------------------------
class WorkflowStage(models.Model):
    STATUS_CHOICES = [('pending', _('Pending')), ('in_progress', _('In Progress')), ('completed', _('Completed')), ('blocked', _('Blocked'))]
    COMPLEXITY_CHOICES = [(1, _('Simple')), (2, _('Medium')), (3, _('Complex'))]

    workflow = models.ForeignKey(WorkflowTemplate, on_delete=models.CASCADE, related_name='stages', verbose_name=_('Workflow Template'))
    name = models.CharField(max_length=100, verbose_name=_('Stage Name'))
    description = models.TextField(blank=True, verbose_name=_('Description'))
    department = models.ForeignKey('Department', on_delete=models.CASCADE, related_name='workflow_stages', verbose_name=_('Department'))
    order = models.PositiveSmallIntegerField(verbose_name=_('Order'), help_text=_('Order in the workflow sequence'))
    estimated_duration = models.DurationField(verbose_name=_('Estimated Duration'))
    dependencies = models.ManyToManyField('self', symmetrical=False, blank=True, related_name='dependent_stages', verbose_name=_('Dependencies'))
    is_critical = models.BooleanField(default=False, verbose_name=_('Critical Stage'), help_text=_('Whether this stage is on the critical path'))
    complexity = models.IntegerField(choices=COMPLEXITY_CHOICES, default=1, verbose_name=_('Complexity Level'))
    required_skills = models.JSONField(default=dict, verbose_name=_('Required Skills'), help_text=_('Skills needed for this stage'), blank=True, null=True)
    required_equipment = models.JSONField(default=dict, verbose_name=_('Required Equipment'), help_text=_('Equipment needed for this stage'), blank=True, null=True)
    quality_checklist = models.JSONField(default=dict, verbose_name=_('Quality Checklist'), help_text=_('Quality control points for this stage'), blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    notes = models.TextField(blank=True, verbose_name=_('Additional Notes'))

    class Meta:
        ordering = ['workflow', 'order']
        verbose_name = _('Workflow Stage')
        verbose_name_plural = _('Workflow Stages')
        unique_together = [['workflow', 'order']]
        indexes = [
            models.Index(fields=['workflow', 'order']),
            models.Index(fields=['department']),
            models.Index(fields=['is_critical'])
        ]

    def __str__(self):
        return f"{self.workflow.name} - {self.name} (Stage {self.order})"

    def clean(self):
        if self.estimated_duration.total_seconds() <= 0:
            raise ValidationError({'estimated_duration': _('Duration must be positive')})
        if self.pk and self.dependencies.filter(pk=self.pk).exists():
            raise ValidationError({'dependencies': _('Cannot depend on itself')})

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def get_dependencies_list(self):
        return list(self.dependencies.all().values_list('id', flat=True))

    def get_dependent_stages(self):
        return self.dependent_stages.all()

    def can_start(self, case):
        dependencies = self.dependencies.all()
        for dependency in dependencies:
            if not case.is_stage_completed(dependency):
                return False
        return True

    def get_required_resources(self):
        return {
            'skills': self.required_skills,
            'equipment': self.required_equipment
        }

    def estimate_completion_time(self, department_workload=None):
        base_duration = self.estimated_duration
        if department_workload:
            workload_factor = 1 + (department_workload / 100)
            return base_duration * workload_factor
        return base_duration

    def get_quality_checks(self):
        return self.quality_checklist

    def check_resource_availability(self, start_time):
        department = self.department
        return department.check_resource_availability(
            start_time,
            self.estimated_duration,
            self.required_equipment
        )

    @property
    def has_dependencies(self):
        return self.dependencies.exists()

    @property
    def is_blocked(self):
        return hasattr(self, 'status') and self.status == 'blocked'

    def mark_as_completed(self, case):
        from case.models import StageHistory
        StageHistory.objects.create(
            case=case,
            stage=self,
            start_date_time=timezone.now(),
            end_date_time=timezone.now(),
            in_progress=False
        )

    def get_next_stages(self):
        return WorkflowStage.objects.filter(
            workflow=self.workflow,
            order__gt=self.order
        ).exclude(
            dependencies__in=self.get_incomplete_dependencies()
        )

    def get_incomplete_dependencies(self):
        return self.dependencies.exclude(
            stagehistory__status='completed'
        )

# ---------------------------
# SKILL
# ---------------------------
class Skill(models.Model):
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)

    def __str__(self):
        return self.name

# ---------------------------
# TRYOUT AND RELATED MODELS
# ---------------------------
class Tryout(models.Model):
    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled')
    ]
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='tryouts')
    location = models.ForeignKey('TryoutLocation', on_delete=models.CASCADE, related_name='tryouts', null=True, blank=True, default=None)
    date_time = models.DateTimeField(default=timezone.now)
    status = models.CharField(max_length=50, choices=STATUS_CHOICES)
    notes = models.TextField(blank=True, null=True)
    outcome = models.TextField(blank=True, null=True)
    feedback = models.OneToOneField('TryoutFeedback', on_delete=models.SET_NULL, null=True, blank=True, related_name='tryout')
    duration = models.DurationField(null=True, blank=True)
    confirmation_status = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    modified_at = models.DateTimeField(auto_now=True, null=True, blank=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_tryouts')
    modified_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='modified_tryouts')

    def update_status(self, new_status):
        self.status = new_status
        self.save()

    def confirm_tryout(self):
        self.confirmation_status = True
        self.save()

    def add_feedback(self, feedback):
        self.feedback = feedback
        self.save()

    def calculate_duration(self, end_time):
        if self.date_time and end_time:
            self.duration = end_time - self.date_time
            self.save(update_fields=['duration'])

    def __str__(self):
        location_name = self.location.name if self.location else "No Location"
        return f"Tryout for Case #{self.case.case_number} at {location_name} - {self.status} on {self.date_time.strftime('%Y-%m-%d %H:%M')}"

class TryoutAttachment(models.Model):
    tryout = models.ForeignKey(Tryout, related_name='tryout_attachments', on_delete=models.CASCADE)
    attachment = models.FileField(upload_to='tryout_attachments/')

    def is_image(self):
        if self.attachment:
            return self.attachment.name.lower().endswith(('.png', '.jpg', '.jpeg', '.gif'))
        return False

    def __str__(self):
        return f"Attachment for Tryout #{self.tryout.id}"

class TryoutLocation(models.Model):
    address = models.TextField()
    coordinates = models.TextField()
    name = models.CharField(max_length=50)

    def __str__(self):
        return self.name

class TryoutFeedback(models.Model):
    rating = models.IntegerField(choices=[(i, str(i)) for i in range(1, 6)], default=5)
    comments = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"Feedback for Tryout #{self.tryout.id}" if hasattr(self, 'tryout') else "Tryout Feedback"

# ---------------------------
# STAGE HISTORY
# ---------------------------
class StageHistory(models.Model):
    case = models.ForeignKey(Case, on_delete=models.CASCADE)
    stage = models.ForeignKey(WorkflowStage, on_delete=models.CASCADE)
    start_date_time = models.DateTimeField()
    end_date_time = models.DateTimeField(null=True, blank=True)
    in_progress = models.BooleanField(default=False)

    def __str__(self):
        return f"Stage {self.stage.name} for Case #{self.case.case_number}"

# ---------------------------
# DEPARTMENT
# ---------------------------
class Department(models.Model):
    name = models.CharField(max_length=50, db_index=True)
    capacity = models.PositiveSmallIntegerField(help_text="Maximum number of concurrent tasks")
    working_hours_start = models.TimeField()
    working_hours_end = models.TimeField()
    is_active = models.BooleanField(default=True, help_text="Whether the department is currently active")
    manager = models.ForeignKey('accounts.CustomUser', on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_departments')
    description = models.TextField(blank=True)
    capacity_hours = models.FloatField(default=160)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name

    def __repr__(self):
        return f"Department(name='{self.name}', capacity={self.capacity})"

    def clean(self):
        if self.working_hours_start >= self.working_hours_end:
            raise ValidationError(_('End time must be after start time.'))

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def get_working_hours(self):
        return f"{self.working_hours_start.strftime('%H:%M')} - {self.working_hours_end.strftime('%H:%M')}"

    def is_within_working_hours(self, time_val):
        return self.working_hours_start <= time_val <= self.working_hours_end

    def get_current_workload(self):
        from case.models import Task
        return Task.objects.filter(workflow_stage__department=self, status='in_progress').count()

    def has_capacity(self):
        return self.get_current_workload() < self.capacity

# ---------------------------
# NOTIFICATION (Legacy - Optional)
# ---------------------------
class Notification(models.Model):
    user = models.ForeignKey('accounts.CustomUser', on_delete=models.CASCADE)
    message = models.CharField(max_length=255)
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.message

# ---------------------------
# CASE STATUS HISTORY
# ---------------------------
class CaseStatusHistory(models.Model):
    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='status_history')
    old_status = models.CharField(max_length=50)
    new_status = models.CharField(max_length=50)
    changed_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    changed_at = models.DateTimeField(auto_now=True)
