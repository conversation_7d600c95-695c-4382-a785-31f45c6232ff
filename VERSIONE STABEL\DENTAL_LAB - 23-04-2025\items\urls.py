from django.urls import path
from . import views

app_name = 'items'

urlpatterns = [
    # Category URLs
    path('categories/', views.category_list, name='category_list'),
    path('categories/create/', views.category_create, name='category_create'),
    path('categories/edit/<int:pk>/', views.category_edit, name='category_edit'),
    path('categories/delete/<int:pk>/', views.category_delete, name='category_delete'),

    # Item URLs
    path('', views.item_list, name='item_list'),
    path('item/create/', views.item_create, name='item_create'),
    path('item/edit/<int:pk>/', views.item_edit, name='item_edit'),
    path('item/delete/<int:pk>/', views.item_delete, name='item_delete'),
    path('item/detail/<int:pk>/', views.item_detail, name='item_detail'),
    path('item/export/', views.export_items_csv, name='export_items_csv'),

    # RawMaterial URLs
    path('raw-material/', views.raw_material_list, name='raw_material_list'),
    path('raw-material/create/', views.raw_material_create, name='raw_material_create'),
    path('raw-material/edit/<int:pk>/', views.raw_material_edit, name='raw_material_edit'),
    path('raw-material/delete/<int:pk>/', views.raw_material_delete, name='raw_material_delete'),
    path('raw-material/export/', views.export_raw_materials_csv, name='export_raw_materials_csv'),

    # Unit URLs
    path('unit/', views.unit_list, name='unit_list'),
    path('unit/create/', views.unit_create, name='unit_create'),
    path('unit/edit/<int:unit_id>/', views.unit_edit, name='unit_edit'),
    path('unit/delete/<int:unit_id>/', views.unit_delete, name='unit_delete'),

    # Supplier URLs
    path('suppliers/', views.supplier_list, name='supplier_list'),
    path('suppliers/create/', views.supplier_create, name='supplier_create'),
    path('suppliers/edit/<int:pk>/', views.supplier_edit, name='supplier_edit'),
    path('suppliers/delete/<int:pk>/', views.supplier_delete, name='supplier_delete'),

    # Inventory URLs
    path('item-inventory/', views.item_inventory, name='item_inventory'),
    path('item-inventory/export/', views.export_item_inventory_csv, name='export_item_inventory_csv'),
    path('raw-material-inventory/', views.raw_material_inventory, name='raw_material_inventory'),
    path('raw-material-inventory/export/', views.export_raw_material_inventory_csv, name='export_raw_material_inventory_csv'),

    # New Inventory Management URLs
    path('mrp/', views.material_requirements_planning, name='material_requirements_planning'),
    path('stock-alerts/', views.stock_alerts_dashboard, name='stock_alerts_dashboard'),
    path('purchase-recommendations/', views.purchase_recommendations, name='purchase_recommendations'),
    path('case-material-analysis/<int:case_id>/', views.case_material_analysis, name='case_material_analysis'),
    path('create-purchase-order/', views.create_purchase_order_from_recommendation, name='create_purchase_order_from_recommendation'),
]
