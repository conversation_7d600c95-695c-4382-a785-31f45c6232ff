# Generated migration for FinancialAuditLog model

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FinancialAuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_type', models.CharField(choices=[('payment_created', 'Payment Created'), ('payment_updated', 'Payment Updated'), ('payment_deleted', 'Payment Deleted'), ('payment_allocated', 'Payment Allocated to Invoice'), ('allocation_reversed', 'Payment Allocation Reversed'), ('invoice_created', 'Invoice Created'), ('invoice_updated', 'Invoice Updated'), ('invoice_status_changed', 'Invoice Status Changed'), ('currency_conversion', 'Currency Conversion'), ('exchange_rate_updated', 'Exchange Rate Updated'), ('account_balance_adjusted', 'Account Balance Adjusted')], db_index=True, max_length=50)),
                ('timestamp', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ('object_type', models.CharField(help_text='Type of object affected', max_length=50)),
                ('object_id', models.PositiveIntegerField(help_text='ID of the affected object')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, help_text='Amount involved in the operation', max_digits=12, null=True)),
                ('currency_code', models.CharField(blank=True, help_text='Currency code for the amount', max_length=3, null=True)),
                ('details', models.JSONField(default=dict, help_text='Additional details about the operation')),
                ('old_values', models.JSONField(blank=True, default=dict, help_text='Values before the operation')),
                ('new_values', models.JSONField(blank=True, default=dict, help_text='Values after the operation')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('session_key', models.CharField(blank=True, max_length=40, null=True)),
                ('user', models.ForeignKey(blank=True, help_text='User who performed the operation', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Financial Audit Log',
                'verbose_name_plural': 'Financial Audit Logs',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddIndex(
            model_name='financialauditlog',
            index=models.Index(fields=['operation_type', 'timestamp'], name='common_fina_operati_b8e8a5_idx'),
        ),
        migrations.AddIndex(
            model_name='financialauditlog',
            index=models.Index(fields=['object_type', 'object_id'], name='common_fina_object__c8b9a2_idx'),
        ),
        migrations.AddIndex(
            model_name='financialauditlog',
            index=models.Index(fields=['user', 'timestamp'], name='common_fina_user_id_3f2a1b_idx'),
        ),
    ]
