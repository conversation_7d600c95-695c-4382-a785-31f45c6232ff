"""
Enhanced security middleware and utilities
"""

import logging
import hashlib
from datetime import datetime, timedelta
from django.core.cache import cache
from django.contrib.auth import logout
from django.contrib.sessions.models import Session
from django.utils import timezone
from django.conf import settings
from django.http import HttpResponseForbidden
from django.shortcuts import redirect
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from django.dispatch import receiver
from typing import Optional, Dict, Any
import json

logger = logging.getLogger(__name__)

class SecurityService:
    """
    Service for enhanced security features
    """
    
    # Security settings
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION = 30  # minutes
    SESSION_TIMEOUT = 60   # minutes
    MAX_CONCURRENT_SESSIONS = 3
    
    @classmethod
    def get_client_ip(cls, request) -> str:
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip
    
    @classmethod
    def get_login_attempt_key(cls, identifier: str) -> str:
        """Generate cache key for login attempts"""
        return f"login_attempts_{hashlib.md5(identifier.encode()).hexdigest()}"
    
    @classmethod
    def get_lockout_key(cls, identifier: str) -> str:
        """Generate cache key for account lockout"""
        return f"account_lockout_{hashlib.md5(identifier.encode()).hexdigest()}"
    
    @classmethod
    def is_account_locked(cls, identifier: str) -> bool:
        """Check if account is locked due to failed login attempts"""
        lockout_key = cls.get_lockout_key(identifier)
        return cache.get(lockout_key, False)
    
    @classmethod
    def record_login_attempt(cls, identifier: str, success: bool) -> Dict[str, Any]:
        """Record login attempt and handle lockout logic"""
        attempt_key = cls.get_login_attempt_key(identifier)
        lockout_key = cls.get_lockout_key(identifier)
        
        if success:
            # Clear failed attempts on successful login
            cache.delete(attempt_key)
            cache.delete(lockout_key)
            return {'locked': False, 'attempts': 0}
        
        # Increment failed attempts
        attempts = cache.get(attempt_key, 0) + 1
        cache.set(attempt_key, attempts, timeout=cls.LOCKOUT_DURATION * 60)
        
        # Lock account if max attempts reached
        if attempts >= cls.MAX_LOGIN_ATTEMPTS:
            cache.set(lockout_key, True, timeout=cls.LOCKOUT_DURATION * 60)
            logger.warning(f"Account locked for {identifier} after {attempts} failed attempts")
            return {'locked': True, 'attempts': attempts, 'lockout_duration': cls.LOCKOUT_DURATION}
        
        return {'locked': False, 'attempts': attempts, 'remaining': cls.MAX_LOGIN_ATTEMPTS - attempts}
    
    @classmethod
    def validate_session_security(cls, request) -> bool:
        """Validate session security (IP consistency, timeout, etc.)"""
        if not request.user.is_authenticated:
            return True
        
        session = request.session
        
        # Check session timeout
        last_activity = session.get('last_activity')
        if last_activity:
            last_activity_time = datetime.fromisoformat(last_activity)
            if timezone.now() - last_activity_time > timedelta(minutes=cls.SESSION_TIMEOUT):
                logger.info(f"Session timeout for user {request.user.email}")
                return False
        
        # Check IP consistency (optional - can be disabled for mobile users)
        if getattr(settings, 'ENFORCE_IP_CONSISTENCY', False):
            session_ip = session.get('ip_address')
            current_ip = cls.get_client_ip(request)
            if session_ip and session_ip != current_ip:
                logger.warning(f"IP address changed for user {request.user.email}: {session_ip} -> {current_ip}")
                return False
        
        # Update last activity
        session['last_activity'] = timezone.now().isoformat()
        
        return True
    
    @classmethod
    def limit_concurrent_sessions(cls, user) -> bool:
        """Limit number of concurrent sessions per user"""
        if user.is_superuser:
            return True  # No limit for superusers
        
        # Get all active sessions for this user
        active_sessions = Session.objects.filter(
            expire_date__gte=timezone.now()
        )
        
        user_sessions = []
        for session in active_sessions:
            session_data = session.get_decoded()
            if session_data.get('_auth_user_id') == str(user.id):
                user_sessions.append(session)
        
        # If too many sessions, delete the oldest ones
        if len(user_sessions) >= cls.MAX_CONCURRENT_SESSIONS:
            sessions_to_delete = user_sessions[:-cls.MAX_CONCURRENT_SESSIONS + 1]
            for session in sessions_to_delete:
                session.delete()
                logger.info(f"Deleted old session for user {user.email}")
        
        return True
    
    @classmethod
    def log_security_event(cls, event_type: str, user=None, request=None, details: Dict = None):
        """Log security events for audit"""
        from common.audit import FinancialAuditService
        
        event_details = {
            'event_type': event_type,
            'timestamp': timezone.now().isoformat(),
            'user_agent': request.META.get('HTTP_USER_AGENT', '') if request else '',
            'ip_address': cls.get_client_ip(request) if request else '',
        }
        
        if details:
            event_details.update(details)
        
        # Log to audit system
        try:
            FinancialAuditService._create_audit_log(
                operation_type='security_event',
                user=user,
                object_type='SecurityEvent',
                object_id=0,
                details=event_details,
                request=request
            )
        except Exception as e:
            logger.error(f"Failed to log security event: {e}")
        
        # Also log to standard logger
        logger.info(f"Security event: {event_type} for user {user.email if user else 'anonymous'}")

class SecurityMiddleware:
    """
    Enhanced security middleware
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Pre-processing
        self.process_request(request)
        
        response = self.get_response(request)
        
        # Post-processing
        self.process_response(request, response)
        
        return response
    
    def process_request(self, request):
        """Process request for security checks"""
        # Skip security checks for certain paths
        skip_paths = ['/admin/', '/static/', '/media/']
        if any(request.path.startswith(path) for path in skip_paths):
            return
        
        # Validate session security
        if request.user.is_authenticated:
            if not SecurityService.validate_session_security(request):
                logout(request)
                messages.warning(
                    request,
                    _("Your session has expired for security reasons. Please log in again.")
                )
                return redirect('accounts:login')
        
        # Add security headers
        request.security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin'
        }
    
    def process_response(self, request, response):
        """Add security headers to response"""
        if hasattr(request, 'security_headers'):
            for header, value in request.security_headers.items():
                response[header] = value
        
        return response

class LoginAttemptMiddleware:
    """
    Middleware to handle login attempt limiting
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        return self.get_response(request)
    
    def process_view(self, request, view_func, view_args, view_kwargs):
        """Check for account lockout on login attempts"""
        if request.path == '/accounts/login/' and request.method == 'POST':
            email = request.POST.get('username', '').lower()
            ip_address = SecurityService.get_client_ip(request)
            
            # Check if account is locked
            if SecurityService.is_account_locked(email) or SecurityService.is_account_locked(ip_address):
                messages.error(
                    request,
                    _("Account temporarily locked due to multiple failed login attempts. "
                      "Please try again in {} minutes.").format(SecurityService.LOCKOUT_DURATION)
                )
                return redirect('accounts:login')
        
        return None

# Signal handlers for security events
@receiver(user_logged_in)
def handle_user_login(sender, request, user, **kwargs):
    """Handle successful user login"""
    ip_address = SecurityService.get_client_ip(request)
    
    # Record successful login attempt
    SecurityService.record_login_attempt(user.email, True)
    SecurityService.record_login_attempt(ip_address, True)
    
    # Limit concurrent sessions
    SecurityService.limit_concurrent_sessions(user)
    
    # Store session information
    request.session['ip_address'] = ip_address
    request.session['last_activity'] = timezone.now().isoformat()
    request.session['user_agent'] = request.META.get('HTTP_USER_AGENT', '')
    
    # Update user's last login IP
    user.last_login_ip = ip_address
    user.save(update_fields=['last_login_ip'])
    
    # Log security event
    SecurityService.log_security_event(
        'user_login',
        user=user,
        request=request,
        details={'login_method': 'password'}
    )

@receiver(user_logged_out)
def handle_user_logout(sender, request, user, **kwargs):
    """Handle user logout"""
    if user:
        SecurityService.log_security_event(
            'user_logout',
            user=user,
            request=request
        )

@receiver(user_login_failed)
def handle_login_failure(sender, credentials, request, **kwargs):
    """Handle failed login attempt"""
    email = credentials.get('username', '').lower()
    ip_address = SecurityService.get_client_ip(request)
    
    # Record failed attempts
    email_result = SecurityService.record_login_attempt(email, False)
    ip_result = SecurityService.record_login_attempt(ip_address, False)
    
    # Log security event
    SecurityService.log_security_event(
        'login_failed',
        request=request,
        details={
            'email': email,
            'email_attempts': email_result['attempts'],
            'ip_attempts': ip_result['attempts']
        }
    )
    
    # Add warning message if approaching lockout
    if email_result['attempts'] >= SecurityService.MAX_LOGIN_ATTEMPTS - 1:
        messages.warning(
            request,
            _("Warning: Account will be locked after one more failed attempt.")
        )

class PasswordPolicyValidator:
    """
    Enhanced password policy validator
    """
    
    MIN_LENGTH = 8
    REQUIRE_UPPERCASE = True
    REQUIRE_LOWERCASE = True
    REQUIRE_DIGITS = True
    REQUIRE_SPECIAL_CHARS = True
    SPECIAL_CHARS = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    
    @classmethod
    def validate_password(cls, password: str) -> Dict[str, Any]:
        """Validate password against policy"""
        errors = []
        
        if len(password) < cls.MIN_LENGTH:
            errors.append(f"Password must be at least {cls.MIN_LENGTH} characters long")
        
        if cls.REQUIRE_UPPERCASE and not any(c.isupper() for c in password):
            errors.append("Password must contain at least one uppercase letter")
        
        if cls.REQUIRE_LOWERCASE and not any(c.islower() for c in password):
            errors.append("Password must contain at least one lowercase letter")
        
        if cls.REQUIRE_DIGITS and not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one digit")
        
        if cls.REQUIRE_SPECIAL_CHARS and not any(c in cls.SPECIAL_CHARS for c in password):
            errors.append(f"Password must contain at least one special character: {cls.SPECIAL_CHARS}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'strength': cls._calculate_strength(password)
        }
    
    @classmethod
    def _calculate_strength(cls, password: str) -> str:
        """Calculate password strength"""
        score = 0
        
        if len(password) >= cls.MIN_LENGTH:
            score += 1
        if len(password) >= 12:
            score += 1
        if any(c.isupper() for c in password):
            score += 1
        if any(c.islower() for c in password):
            score += 1
        if any(c.isdigit() for c in password):
            score += 1
        if any(c in cls.SPECIAL_CHARS for c in password):
            score += 1
        
        if score <= 2:
            return 'weak'
        elif score <= 4:
            return 'medium'
        else:
            return 'strong'
