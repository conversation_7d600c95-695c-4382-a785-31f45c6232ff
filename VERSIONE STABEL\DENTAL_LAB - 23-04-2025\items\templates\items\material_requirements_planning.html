{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Material Requirements Planning (MRP)" %}{% endblock %}

{% block content %}
<div class="container-fluid my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">
            <i class="bi bi-diagram-3"></i> {% trans "Material Requirements Planning (MRP)" %}
        </h1>
        <div>
            <a href="{% url 'items:stock_alerts_dashboard' %}" class="btn btn-warning me-2">
                <i class="bi bi-exclamation-triangle"></i> {% trans "Stock Alerts" %}
            </a>
            <a href="{% url 'items:purchase_recommendations' %}" class="btn btn-primary">
                <i class="bi bi-cart-plus"></i> {% trans "Purchase Recommendations" %}
            </a>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">{% trans "Cases Analyzed" %}</h6>
                            <h3 class="mb-0">{{ total_cases_analyzed }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-clipboard-data fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">{% trans "Material Types" %}</h6>
                            <h3 class="mb-0">{{ total_material_requirements|length }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-boxes fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">{% trans "Shortage Cost" %}</h6>
                            <h3 class="mb-0">${{ total_shortage_cost|floatformat:2 }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-currency-dollar fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">{% trans "Recommendations" %}</h6>
                            <h3 class="mb-0">{{ purchase_recommendations|length }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-lightbulb fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Material Requirements Summary -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-check"></i> {% trans "Aggregated Material Requirements" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if total_material_requirements %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>{% trans "Material" %}</th>
                                        <th>{% trans "Required" %}</th>
                                        <th>{% trans "Available" %}</th>
                                        <th>{% trans "Shortage" %}</th>
                                        <th>{% trans "Status" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for req in total_material_requirements %}
                                        <tr>
                                            <td>
                                                <strong>{{ req.raw_material.name }}</strong>
                                                <br><small class="text-muted">{{ req.raw_material.description|truncatechars:50 }}</small>
                                            </td>
                                            <td>{{ req.required_quantity }} {{ req.unit.name }}</td>
                                            <td>{{ req.available_quantity }} {{ req.unit.name }}</td>
                                            <td>
                                                {% if req.shortage_quantity > 0 %}
                                                    <span class="text-danger">{{ req.shortage_quantity }} {{ req.unit.name }}</span>
                                                {% else %}
                                                    <span class="text-success">{% trans "Sufficient" %}</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if req.shortage_quantity > 0 %}
                                                    <span class="badge bg-danger">{% trans "Shortage" %}</span>
                                                {% else %}
                                                    <span class="badge bg-success">{% trans "Available" %}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-inbox fs-1 text-muted"></i>
                            <p class="text-muted mt-2">{% trans "No material requirements found." %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-cart-plus"></i> {% trans "Top Purchase Recommendations" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if purchase_recommendations %}
                        {% for rec in purchase_recommendations|slice:":5" %}
                            <div class="d-flex justify-content-between align-items-center mb-3 p-2 border rounded">
                                <div>
                                    <strong>{{ rec.raw_material.name }}</strong>
                                    <br><small class="text-muted">{{ rec.reason|truncatechars:40 }}</small>
                                    <br><small class="text-info">{{ rec.supplier.name }}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-{{ rec.priority == 'urgent' and 'danger' or rec.priority == 'high' and 'warning' or 'info' }}">
                                        {{ rec.priority|title }}
                                    </span>
                                    <br><small class="text-muted">${{ rec.estimated_cost|floatformat:2 }}</small>
                                </div>
                            </div>
                        {% endfor %}
                        <div class="text-center mt-3">
                            <a href="{% url 'items:purchase_recommendations' %}" class="btn btn-outline-primary btn-sm">
                                {% trans "View All Recommendations" %}
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="bi bi-check-circle fs-2 text-success"></i>
                            <p class="text-muted mt-2">{% trans "No purchase recommendations at this time." %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Case Analysis Details -->
    {% if case_plans %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clipboard-data"></i> {% trans "Case-by-Case Analysis" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="caseAnalysisAccordion">
                    {% for plan in case_plans %}
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading{{ forloop.counter }}">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                        data-bs-target="#collapse{{ forloop.counter }}" aria-expanded="false">
                                    <div class="d-flex justify-content-between w-100 me-3">
                                        <span>
                                            <strong>Case #{{ plan.case_id }}</strong>
                                            {% if not plan.feasible %}
                                                <span class="badge bg-danger ms-2">{% trans "Material Shortage" %}</span>
                                            {% else %}
                                                <span class="badge bg-success ms-2">{% trans "Feasible" %}</span>
                                            {% endif %}
                                        </span>
                                        <span class="text-muted">
                                            {% trans "Cost:" %} ${{ plan.total_cost|floatformat:2 }}
                                            {% if plan.total_shortage_cost > 0 %}
                                                | {% trans "Shortage:" %} ${{ plan.total_shortage_cost|floatformat:2 }}
                                            {% endif %}
                                        </span>
                                    </div>
                                </button>
                            </h2>
                            <div id="collapse{{ forloop.counter }}" class="accordion-collapse collapse" 
                                 data-bs-parent="#caseAnalysisAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>{% trans "Items Required" %}</h6>
                                            <ul class="list-group list-group-flush">
                                                {% for item_data in plan.items %}
                                                    <li class="list-group-item d-flex justify-content-between">
                                                        <span>{{ item_data.item.name }} ({{ item_data.quantity }})</span>
                                                        <span class="text-muted">${{ item_data.estimated_cost|floatformat:2 }}</span>
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>{% trans "Material Requirements" %}</h6>
                                            {% if plan.missing_materials %}
                                                <div class="alert alert-warning">
                                                    <strong>{% trans "Missing Materials:" %}</strong>
                                                    <ul class="mb-0 mt-2">
                                                        {% for missing in plan.missing_materials %}
                                                            <li>{{ missing.raw_material.name }}: {{ missing.shortage_quantity }} {{ missing.unit.name }}</li>
                                                        {% endfor %}
                                                    </ul>
                                                </div>
                                            {% else %}
                                                <div class="alert alert-success">
                                                    <i class="bi bi-check-circle"></i> {% trans "All materials available" %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
