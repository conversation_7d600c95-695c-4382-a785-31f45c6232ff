#!/usr/bin/env python
"""
Data Consistency Audit Script
Checks for data integrity issues in the dental lab system.
"""

import os
import sys
import django
from decimal import Decimal
from collections import defaultdict
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

from django.db import connection
from django.db.models import Sum, Count, Q, F
from django.core.exceptions import ValidationError

# Import all models
from accounts.models import CustomUser, UserDepartment
from billing.models import Invoice, InvoiceItem, PurchaseOrder, PurchaseOrderItem
from case.models import Case, CaseItem, WorkflowStage, Task, Department
from Dentists.models import Dentist
from finance.models import Account, Payment, InvoicePayment, SupplierPayment
from items.models import Currency, ExchangeRate, Item, RawMaterial, Supplier
from patients.models import Patient
from scheduling.models import Schedule, ScheduleItem

class DataAudit:
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.stats = {}

    def log_issue(self, category, description, severity='ERROR'):
        """Log a data integrity issue"""
        issue = {
            'category': category,
            'description': description,
            'severity': severity,
            'timestamp': datetime.now()
        }
        if severity == 'ERROR':
            self.issues.append(issue)
        else:
            self.warnings.append(issue)
        print(f"[{severity}] {category}: {description}")

    def check_orphaned_records(self):
        """Check for orphaned records due to missing foreign key constraints"""
        print("\n=== Checking for Orphaned Records ===")

        # Check for cases without valid dentists
        orphaned_cases = Case.objects.filter(dentist__isnull=True)
        if orphaned_cases.exists():
            self.log_issue('ORPHANED_RECORDS',
                          f"Found {orphaned_cases.count()} cases without dentists")

        # Check for invoices without valid cases
        orphaned_invoices = Invoice.objects.filter(case__isnull=True)
        if orphaned_invoices.exists():
            self.log_issue('ORPHANED_RECORDS',
                          f"Found {orphaned_invoices.count()} invoices without cases")

        # Check for payments without valid accounts
        orphaned_payments = Payment.objects.filter(account__isnull=True)
        if orphaned_payments.exists():
            self.log_issue('ORPHANED_RECORDS',
                          f"Found {orphaned_payments.count()} payments without accounts")

        # Check for case items without valid items
        orphaned_case_items = CaseItem.objects.filter(item__isnull=True)
        if orphaned_case_items.exists():
            self.log_issue('ORPHANED_RECORDS',
                          f"Found {orphaned_case_items.count()} case items without items")

    def check_financial_consistency(self):
        """Check financial data consistency"""
        print("\n=== Checking Financial Consistency ===")

        # Check invoice totals vs item totals
        for invoice in Invoice.objects.all():
            # Use the correct related name for invoice items
            calculated_total = invoice.invoice_items.aggregate(
                total=Sum(F('quantity') * F('selling_price'))
            )['total'] or Decimal('0.00')

            if abs(invoice.total_amount - calculated_total) > Decimal('0.01'):
                self.log_issue('FINANCIAL_MISMATCH',
                              f"Invoice {invoice.id}: stored total {invoice.total_amount} "
                              f"vs calculated {calculated_total}")

        # Check payment allocations
        for payment in Payment.objects.all():
            allocated_amount = InvoicePayment.objects.filter(
                payment=payment
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

            if allocated_amount > payment.amount:
                self.log_issue('FINANCIAL_MISMATCH',
                              f"Payment {payment.id}: allocated {allocated_amount} "
                              f"exceeds payment amount {payment.amount}")

    def check_status_consistency(self):
        """Check status consistency across related objects"""
        print("\n=== Checking Status Consistency ===")

        # Check case vs schedule status consistency
        for case in Case.objects.filter(schedule__isnull=False):
            schedule = case.schedule
            if case.status == 'completed' and schedule.status != 'completed':
                self.log_issue('STATUS_MISMATCH',
                              f"Case {case.case_number} is completed but "
                              f"schedule status is {schedule.status}")

        # Check invoice vs case status
        for invoice in Invoice.objects.all():
            if invoice.case.status == 'cancelled' and invoice.status != 'cancelled':
                self.log_issue('STATUS_MISMATCH',
                              f"Invoice {invoice.id} for cancelled case "
                              f"{invoice.case.case_number} is not cancelled")

    def check_currency_consistency(self):
        """Check currency handling consistency"""
        print("\n=== Checking Currency Consistency ===")

        # Check for missing exchange rates
        currencies = Currency.objects.all()
        base_currency = Currency.objects.filter(code='ALL').first()

        if not base_currency:
            self.log_issue('CURRENCY_ISSUE', "Base currency 'ALL' not found")
            return

        for currency in currencies:
            if currency != base_currency:
                rate = ExchangeRate.objects.filter(
                    from_currency=currency,
                    to_currency=base_currency
                ).first()
                if not rate:
                    self.log_issue('CURRENCY_ISSUE',
                                  f"Missing exchange rate from {currency.code} to ALL")

    def check_workflow_consistency(self):
        """Check workflow and stage consistency"""
        print("\n=== Checking Workflow Consistency ===")

        # Check for cases with invalid current stages
        for case in Case.objects.filter(current_stage__isnull=False):
            if case.workflow_template and case.current_stage.workflow != case.workflow_template:
                self.log_issue('WORKFLOW_MISMATCH',
                              f"Case {case.case_number} current stage doesn't match workflow template")

        # Check for tasks without valid workflow stages
        invalid_tasks = Task.objects.filter(workflow_stage__isnull=True)
        if invalid_tasks.exists():
            self.log_issue('WORKFLOW_ISSUE',
                          f"Found {invalid_tasks.count()} tasks without workflow stages")

    def generate_statistics(self):
        """Generate system statistics"""
        print("\n=== System Statistics ===")

        self.stats = {
            'total_cases': Case.objects.count(),
            'total_invoices': Invoice.objects.count(),
            'total_payments': Payment.objects.count(),
            'total_users': CustomUser.objects.count(),
            'total_dentists': Dentist.objects.count(),
            'total_patients': Patient.objects.count(),
            'active_cases': Case.objects.exclude(status__in=['completed', 'cancelled']).count(),
            'unpaid_invoices': Invoice.objects.filter(status='unpaid').count(),
        }

        for key, value in self.stats.items():
            print(f"{key.replace('_', ' ').title()}: {value}")

    def run_full_audit(self):
        """Run complete data audit"""
        print("Starting Data Consistency Audit...")
        print("=" * 50)

        self.check_orphaned_records()
        self.check_financial_consistency()
        self.check_status_consistency()
        self.check_currency_consistency()
        self.check_workflow_consistency()
        self.generate_statistics()

        print("\n" + "=" * 50)
        print("AUDIT SUMMARY")
        print("=" * 50)
        print(f"Total Issues Found: {len(self.issues)}")
        print(f"Total Warnings: {len(self.warnings)}")

        if self.issues:
            print("\nCRITICAL ISSUES:")
            for issue in self.issues:
                print(f"- {issue['category']}: {issue['description']}")

        if self.warnings:
            print("\nWARNINGS:")
            for warning in self.warnings:
                print(f"- {warning['category']}: {warning['description']}")

        return len(self.issues) == 0

if __name__ == '__main__':
    audit = DataAudit()
    success = audit.run_full_audit()

    if not success:
        print("\n⚠️  Data integrity issues found! Please review and fix before proceeding.")
        sys.exit(1)
    else:
        print("\n✅ Data audit completed successfully!")
        sys.exit(0)
