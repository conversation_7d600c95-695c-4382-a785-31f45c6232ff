# 🧪 WORKFLOW SYSTEM TESTING GUIDE

## 📋 **<PERSON><PERSON><PERSON>K TESTING CHECKLIST**

### **1. Basic System Tests**
```bash
# Test workflow system components
python test_workflow_system.py

# Test validation system integration
python manage.py validate_system --type=all

# Test workflow management commands
python manage.py test_workflow --test-type=all
```

### **2. Interactive Testing via Django Shell**
```bash
python manage.py shell
```

Then run these commands in the shell:

```python
# Import required modules
from case.models import Case, Task
from case.services import CaseService, TaskService
from common.workflow_automation import workflow_automation, TransitionContext
from common.status_synchronization import status_sync
from common.dependency_management import dependency_manager

# Test 1: Get a case and check valid transitions
case = Case.objects.first()
if case:
    print(f"Case #{case.case_number} current status: {case.status}")
    
    # Get valid transitions
    valid_transitions = CaseService.get_valid_case_transitions(case)
    print(f"Valid transitions: {[t.to_state for t in valid_transitions]}")
    
    # Test status update with workflow
    success, message, updated_case = CaseService.update_case_status(
        case, 'in_progress', reason="Testing workflow"
    )
    print(f"Status update result: {success} - {message}")

# Test 2: Task workflow
task = Task.objects.first()
if task:
    print(f"Task #{task.id} current status: {task.status}")
    
    # Test task start
    if task.status == 'pending':
        success, message = TaskService.start_task(task, task.assigned_to or task.case.dentist_user)
        print(f"Task start result: {success} - {message}")

# Test 3: Auto-progression
if case:
    success, messages = CaseService.auto_progress_case(case)
    print(f"Auto-progression: {success}")
    for msg in messages:
        print(f"  - {msg}")

# Test 4: Status synchronization
if case:
    changed, new_status, message = status_sync.sync_case_status_from_tasks(case)
    print(f"Status sync: {changed} - {new_status} - {message}")

# Test 5: Dependency management
if task:
    dependency_chain = dependency_manager.get_task_dependency_chain(task)
    print(f"Dependency chain: {dependency_chain}")
```

### **3. Web Interface Testing**

1. **Case Management**
   - Create a new case and observe automatic status transitions
   - Assign technicians and watch for auto-progression to 'in_progress'
   - Complete tasks and verify case status updates

2. **Task Management**
   - Start tasks and verify workflow transitions
   - Complete tasks and check for case auto-progression
   - Test task dependencies and blocking

3. **Status Monitoring**
   - Check case list for consistent statuses
   - Verify task completion triggers case progression
   - Monitor workflow history and stage transitions

### **4. Advanced Testing Scenarios**

#### **Scenario A: Complete Case Workflow**
```python
# Create a test case workflow
from django.contrib.auth import get_user_model
User = get_user_model()

# Get or create test user
user = User.objects.first()

# Create case with workflow template
case = Case.objects.create(
    case_number="TEST001",
    dentist_user=user,
    patient="Test Patient",
    status="pending_acceptance"
)

# Test progression through all stages
stages = ['in_progress', 'quality_check', 'ready_to_ship', 'shipped', 'delivered', 'completed']
for stage in stages:
    success, message, updated_case = CaseService.update_case_status(case, stage, user=user)
    print(f"Transition to {stage}: {success} - {message}")
```

#### **Scenario B: Task Dependencies**
```python
# Test task dependency workflow
if case.tasks.count() >= 2:
    task1 = case.tasks.first()
    task2 = case.tasks.last()
    
    # Add dependency
    success, message = dependency_manager.add_task_dependency(task2, task1)
    print(f"Dependency added: {success} - {message}")
    
    # Complete first task and check second task status
    TaskService.complete_task(task1, user)
    
    # Check if second task is unblocked
    dependency_chain = dependency_manager.get_task_dependency_chain(task2)
    print(f"Task 2 can start: {dependency_chain.get('can_start', False)}")
```

#### **Scenario C: Critical Path Analysis**
```python
# Analyze critical path for a case
if case:
    critical_path = dependency_manager.get_critical_path(case)
    print(f"Critical path duration: {critical_path.get('total_duration', 0)} hours")
    
    for task_info in critical_path.get('critical_path', []):
        print(f"Critical task: {task_info['title']} ({task_info['duration']} hours)")
```

## 🔍 **WHAT TO LOOK FOR**

### **✅ Expected Behaviors**
- Cases automatically progress when conditions are met
- Tasks block/unblock based on dependencies
- Status changes are logged and validated
- Business rules prevent invalid transitions
- Related entities stay synchronized

### **❌ Issues to Watch For**
- Status inconsistencies between cases and tasks
- Failed transitions due to missing conditions
- Circular dependencies in task chains
- Performance issues with large dependency graphs
- Missing validation errors

## 📊 **MONITORING COMMANDS**

### **System Health Check**
```bash
# Check overall system validation
python manage.py validate_system --type=summary

# Check workflow-specific issues
python manage.py test_workflow --test-type=sync

# Monitor status consistency
python manage.py test_workflow --test-type=dependencies
```

### **Performance Testing**
```bash
# Test with specific case
python manage.py test_workflow --case-id=1 --test-type=all

# Test with specific task
python manage.py test_workflow --task-id=1 --test-type=dependencies
```

## 🚨 **TROUBLESHOOTING**

### **Common Issues & Solutions**

1. **Workflow Transitions Fail**
   - Check business rule conditions
   - Verify user permissions
   - Review validation errors in logs

2. **Status Synchronization Problems**
   - Run status consistency check
   - Manually sync with `status_sync.sync_case_status_from_tasks(case)`
   - Check for orphaned tasks or cases

3. **Dependency Issues**
   - Check for circular dependencies
   - Verify task relationships
   - Use dependency chain analysis

4. **Performance Issues**
   - Monitor database queries
   - Check for N+1 query problems
   - Consider caching for large workflows

## 📝 **TESTING LOG TEMPLATE**

```
Date: ___________
Tester: ___________

Basic Tests:
[ ] Workflow system initialization
[ ] State machine transitions
[ ] Status synchronization
[ ] Dependency management
[ ] Auto-progression

Advanced Tests:
[ ] Complete case workflow
[ ] Task dependency chains
[ ] Critical path calculation
[ ] Error handling
[ ] Performance under load

Issues Found:
1. ________________________________
2. ________________________________
3. ________________________________

Overall Assessment: ________________
```

## 🎯 **SUCCESS CRITERIA**

The workflow system is working correctly if:
- ✅ All basic tests pass
- ✅ Cases progress automatically through stages
- ✅ Task completion triggers case updates
- ✅ Dependencies block/unblock tasks correctly
- ✅ Status remains consistent across entities
- ✅ Business rules are enforced
- ✅ No circular dependencies exist
- ✅ Performance is acceptable

---

**Use this guide to thoroughly test the workflow system while I implement the scheduling engine enhancement!**
