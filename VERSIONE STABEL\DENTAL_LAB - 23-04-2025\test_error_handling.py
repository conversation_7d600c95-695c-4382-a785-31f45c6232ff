#!/usr/bin/env python
"""
Test script for the enhanced error handling and validation system
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

from common.exceptions import (
    DentalLabException, BusinessRuleViolation, DataIntegrityError,
    ValidationError, FinancialError, WorkflowError, handle_exception
)
from common.validators import CentralizedValidationService, BusinessRuleValidator
from django.core.exceptions import ValidationError as DjangoValidationError


def test_custom_exceptions():
    """Test custom exception classes"""
    print("🧪 Testing Custom Exceptions...")
    
    # Test base exception
    try:
        raise DentalLabException("Test error", error_code="TEST_ERROR")
    except DentalLabException as e:
        print(f"✅ DentalLabException: {e.to_dict()}")
    
    # Test business rule violation
    try:
        raise BusinessRuleViolation("Invalid workflow transition", rule_name="workflow_order")
    except BusinessRuleViolation as e:
        print(f"✅ BusinessRuleViolation: {e.to_dict()}")
    
    # Test validation error with field errors
    try:
        raise ValidationError("Form validation failed", field_errors={
            'amount': ['Must be positive'],
            'date': ['Cannot be in future']
        })
    except ValidationError as e:
        print(f"✅ ValidationError: {e.to_dict()}")
    
    print("✅ Custom exceptions working correctly\n")


def test_exception_handler():
    """Test the central exception handler"""
    print("🧪 Testing Exception Handler...")
    
    # Test Django ValidationError conversion
    django_error = DjangoValidationError("Invalid data")
    converted = handle_exception(django_error, context={'test': True})
    print(f"✅ Django ValidationError converted: {converted.to_dict()}")
    
    # Test unknown exception conversion
    unknown_error = ValueError("Unknown error")
    converted = handle_exception(unknown_error, context={'test': True})
    print(f"✅ Unknown exception converted: {converted.to_dict()}")
    
    print("✅ Exception handler working correctly\n")


def test_validation_service():
    """Test the centralized validation service"""
    print("🧪 Testing Validation Service...")
    
    # Test data integrity validation
    try:
        integrity_results = CentralizedValidationService.validate_data_integrity()
        print(f"✅ Data integrity check completed: {len(integrity_results)} categories checked")
        
        for category, errors in integrity_results.items():
            if errors:
                print(f"  ⚠️  {category}: {len(errors)} issues found")
            else:
                print(f"  ✅ {category}: No issues")
    
    except Exception as e:
        print(f"❌ Data integrity validation failed: {e}")
    
    # Test validation summary
    try:
        summary = CentralizedValidationService.get_validation_summary()
        print(f"✅ Validation summary: {summary['status']} - {summary['total_errors']} errors")
    
    except Exception as e:
        print(f"❌ Validation summary failed: {e}")
    
    print("✅ Validation service working correctly\n")


def test_business_validators():
    """Test business rule validators"""
    print("🧪 Testing Business Validators...")
    
    # Test case creation validation
    case_data = {
        'dentist': None,  # Missing required field
        'patient': 'Test Patient',
        'priority': 6,  # Invalid priority
        'received_date_time': '2024-01-01',
        'deadline': '2023-12-31'  # Invalid deadline
    }
    
    try:
        errors = BusinessRuleValidator.validate_case_creation(case_data)
        print(f"✅ Case validation found {len(errors)} errors:")
        for field, error in errors.items():
            print(f"  - {field}: {error}")
    
    except Exception as e:
        print(f"❌ Case validation failed: {e}")
    
    # Test payment creation validation
    payment_data = {
        'dentist': 1,
        'amount': -100,  # Invalid amount
        'currency': 1,
        'account': 1,
        'date': '2025-01-01'  # Future date
    }
    
    try:
        errors = BusinessRuleValidator.validate_payment_creation(payment_data)
        print(f"✅ Payment validation found {len(errors)} errors:")
        for field, error_list in errors.items():
            for error in error_list:
                print(f"  - {field}: {error}")
    
    except Exception as e:
        print(f"❌ Payment validation failed: {e}")
    
    print("✅ Business validators working correctly\n")


def test_model_validation():
    """Test model-specific validation"""
    print("🧪 Testing Model Validation...")
    
    try:
        from case.models import Case
        
        # Test case model validation
        case_data = {
            'case_number': 'TEST001',
            'dentist': None,  # Missing required
            'patient': None,  # Missing required
            'priority': 3,
            'status': 'pending'
        }
        
        errors = CentralizedValidationService.validate_model_data(Case, case_data)
        print(f"✅ Case model validation found {len(errors)} field errors:")
        for field, error_list in errors.items():
            for error in error_list:
                print(f"  - {field}: {error}")
    
    except Exception as e:
        print(f"❌ Model validation failed: {e}")
    
    print("✅ Model validation working correctly\n")


def test_business_operations():
    """Test business operation validation"""
    print("🧪 Testing Business Operations...")
    
    # Test case completion validation
    try:
        errors = CentralizedValidationService.validate_business_operation(
            'case_completion',
            case=None  # No case provided
        )
        print(f"✅ Case completion validation: {len(errors)} errors found")
        for error in errors:
            print(f"  - {error}")
    
    except Exception as e:
        print(f"❌ Business operation validation failed: {e}")
    
    print("✅ Business operations validation working correctly\n")


def main():
    """Run all tests"""
    print("🚀 Starting Error Handling & Validation System Tests\n")
    print("="*60)
    
    try:
        test_custom_exceptions()
        test_exception_handler()
        test_validation_service()
        test_business_validators()
        test_model_validation()
        test_business_operations()
        
        print("="*60)
        print("🎉 All tests completed successfully!")
        print("✅ Error handling and validation system is working correctly")
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
