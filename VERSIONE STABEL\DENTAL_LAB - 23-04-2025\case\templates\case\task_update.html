{% extends 'base.html' %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
<style>
    /* Color Variables */
    :root {
        --primary: #4f46e5;
        --primary-light: #6366f1;
        --primary-dark: #4338ca;
        --secondary: #64748b;
        --success: #22c55e;
        --success-light: #86efac;
        --danger: #ef4444;
        --danger-light: #fca5a5;
        --warning: #f59e0b;
        --warning-light: #fcd34d;
        --info: #3b82f6;
        --info-light: #93c5fd;
        --gray-50: #f8fafc;
        --gray-100: #f1f5f9;
        --gray-200: #e2e8f0;
        --gray-300: #cbd5e1;
        --gray-400: #94a3b8;
        --gray-500: #64748b;
        --gray-600: #475569;
        --gray-700: #334155;
        --gray-800: #1e293b;
        --gray-900: #0f172a;
    }

    /* Layout Styles */
    .task-update-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem 1rem;
    }

    /* Card Styles */
    .task-form-card {
        background-color: white;
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .task-form-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    /* Header Styles */
    .form-header {
        background: linear-gradient(to right, var(--primary-dark), var(--primary));
        color: white;
        padding: 1.5rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .form-header h2 {
        margin: 0;
        font-weight: 600;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
    }

    .form-header h2 i {
        margin-right: 0.75rem;
        font-size: 1.75rem;
    }

    /* Form Content */
    .form-content {
        padding: 2rem;
    }

    /* Form Section Styles */
    .form-section {
        background-color: var(--gray-50);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border: 1px solid var(--gray-200);
        transition: all 0.3s ease;
    }

    .form-section:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }

    .section-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 1.25rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 0.5rem;
        color: var(--primary);
    }

    /* Form Controls */
    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        display: block;
        color: var(--gray-700);
    }

    .form-control, .form-select, .select2-container--default .select2-selection--single {
        width: 100%;
        padding: 0.625rem 0.875rem;
        border: 1px solid var(--gray-300);
        border-radius: 0.5rem;
        background-color: white;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        outline: none;
        transform: translateY(-1px);
    }

    textarea.form-control {
        min-height: 100px;
        resize: vertical;
    }

    /* Required Field Indicator */
    .required-field::after {
        content: "*";
        color: var(--danger);
        margin-left: 0.25rem;
    }

    /* Form Grid */
    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    /* Error States */
    .invalid-feedback {
        display: block;
        color: var(--danger);
        font-size: 0.875rem;
        margin-top: 0.375rem;
        animation: shake 0.5s ease-in-out;
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    /* Button Styles */
    .form-actions {
        display: flex;
        justify-content: space-between;
        padding: 1.5rem;
        background-color: var(--gray-50);
        border-top: 1px solid var(--gray-200);
    }

    .btn {
        padding: 0.625rem 1.25rem;
        border-radius: 0.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background-color: var(--primary);
        color: white;
        border: none;
    }

    .btn-primary:hover {
        background-color: var(--primary-light);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2);
    }

    .btn-secondary {
        background-color: white;
        color: var(--gray-700);
        border: 1px solid var(--gray-300);
    }

    .btn-secondary:hover {
        background-color: var(--gray-50);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
    }

    /* Alert Styles */
    .alert {
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid transparent;
    }

    .alert-info {
        background-color: rgba(59, 130, 246, 0.1);
        border-color: var(--info-light);
        color: var(--info);
    }

    .alert-success {
        background-color: rgba(34, 197, 94, 0.1);
        border-color: var(--success-light);
        color: var(--success);
    }

    .alert-warning {
        background-color: rgba(245, 158, 11, 0.1);
        border-color: var(--warning-light);
        color: var(--warning);
    }

    .alert-danger {
        background-color: rgba(239, 68, 68, 0.1);
        border-color: var(--danger-light);
        color: var(--danger);
    }

    /* Field Type Styles */
    .case-item-field {
        border-left: 3px solid var(--success);
        padding-left: 0.75rem;
        transition: all 0.3s ease;
    }

    .case-item-field:hover {
        background-color: rgba(34, 197, 94, 0.05);
    }

    /* Tooltip Styles */
    .tooltip-icon {
        color: var(--gray-400);
        margin-left: 0.5rem;
        cursor: help;
        position: relative;
    }

    .tooltip-icon:hover::after {
        content: attr(data-tooltip);
        position: absolute;
        top: -30px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--gray-800);
        color: white;
        padding: 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        white-space: nowrap;
        z-index: 10;
    }

    /* Select2 Customization */
    .select2-container--default .select2-selection--single {
        height: auto;
        padding: 0.625rem 0.875rem;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 100%;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: var(--primary);
    }

    /* Status Styles */
    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-pending {
        background-color: var(--primary-light);
        color: white;
    }

    .status-in_progress {
        background-color: var(--warning);
        color: var(--gray-900);
    }

    .status-completed {
        background-color: var(--success);
        color: white;
    }

    .status-cancelled {
        background-color: var(--danger);
        color: white;
    }

    /* Priority Styles */
    .priority-indicator {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        margin-right: 0.5rem;
    }

    .priority-1 { background-color: var(--success); }
    .priority-2 { background-color: var(--info); }
    .priority-3 { background-color: var(--warning); }
    .priority-4 { background-color: var(--danger); }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .form-grid {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column-reverse;
            gap: 1rem;
        }

        .form-actions > div {
            width: 100%;
            display: flex;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="task-update-container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}" class="text-decoration-none"><i class="bi bi-house-door"></i> Home</a></li>
            <li class="breadcrumb-item"><a href="{% url 'case:task_list' %}" class="text-decoration-none"><i class="bi bi-list-task"></i> Tasks</a></li>
            {% if task.case %}
            <li class="breadcrumb-item"><a href="{% url 'case:case_detail' task.case.case_number %}" class="text-decoration-none"><i class="bi bi-folder2-open"></i> Case #{{ task.case.case_number }}</a></li>
            {% endif %}
            <li class="breadcrumb-item"><a href="{% url 'case:task_detail' task.id %}" class="text-decoration-none"><i class="bi bi-clipboard-check"></i> Task #{{ task.id }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
        </ol>
    </nav>

    <div class="task-form-card">
        <!-- Header -->
        <div class="form-header d-flex justify-content-between align-items-center">
            <h2>
                <i class="bi bi-pencil-square"></i>
                Update Task {% if task.case %}<span class="badge bg-light text-primary ms-2">Case #{{ task.case.case_number }}</span>{% endif %}
            </h2>
            <div>
                <a href="{% url 'case:task_detail' task.id %}" class="btn btn-sm btn-outline-light">
                    <i class="bi bi-eye"></i> View Task
                </a>
            </div>
        </div>

        <!-- Form Content -->
        <div class="form-content">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} d-flex align-items-center">
                        {% if message.tags == 'success' %}
                            <i class="bi bi-check-circle-fill me-2 fs-4"></i>
                        {% elif message.tags == 'warning' %}
                            <i class="bi bi-exclamation-triangle-fill me-2 fs-4"></i>
                        {% elif message.tags == 'error' or message.tags == 'danger' %}
                            <i class="bi bi-x-circle-fill me-2 fs-4"></i>
                        {% else %}
                            <i class="bi bi-info-circle-fill me-2 fs-4"></i>
                        {% endif %}
                        <div>{{ message }}</div>
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                {% csrf_token %}

                <!-- Basic Information Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i>
                        Basic Information
                    </h3>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="{{ form.case.id_for_label }}" class="required-field">Case</label>
                            <div class="input-group">
                                {{ form.case }}
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-folder2-open text-primary"></i>
                                </span>
                            </div>
                            <small class="text-muted">The case this task belongs to</small>
                            {% if form.case.errors %}
                                <div class="invalid-feedback">{{ form.case.errors.as_text }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group case-item-field">
                            <label for="{{ form.case_item.id_for_label }}">
                                Case Item
                                <i class="bi bi-question-circle tooltip-icon" data-tooltip="Link this task to a specific item in the case"></i>
                            </label>
                            <div class="input-group">
                                {{ form.case_item }}
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-box-seam text-success"></i>
                                </span>
                            </div>
                            <small class="text-muted">Optional: Associate this task with a specific case item</small>
                            {% if form.case_item.errors %}
                                <div class="invalid-feedback">{{ form.case_item.errors.as_text }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-group mt-3">
                        <label for="{{ form.title.id_for_label }}" class="required-field">Title</label>
                        <div class="input-group">
                            {{ form.title }}
                            <span class="input-group-text bg-light">
                                <i class="bi bi-type-h1 text-primary"></i>
                            </span>
                        </div>
                        <small class="text-muted">A clear, concise title for the task</small>
                        {% if form.title.errors %}
                            <div class="invalid-feedback">{{ form.title.errors.as_text }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="{{ form.description.id_for_label }}">Description</label>
                        {{ form.description }}
                        <small class="text-muted">Detailed explanation of what needs to be done</small>
                        {% if form.description.errors %}
                            <div class="invalid-feedback">{{ form.description.errors.as_text }}</div>
                        {% endif %}
                    </div>
                </div>

                <!-- Assignment Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-person-fill"></i>
                        Assignment Details
                    </h3>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="{{ form.workflow_stage.id_for_label }}" class="required-field">
                                Workflow Stage
                                <i class="bi bi-question-circle tooltip-icon" data-tooltip="The production stage this task belongs to"></i>
                            </label>
                            <div class="input-group">
                                {{ form.workflow_stage }}
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-diagram-3 text-primary"></i>
                                </span>
                            </div>
                            <small class="text-muted">The workflow stage this task is part of</small>
                            {% if form.workflow_stage.errors %}
                                <div class="invalid-feedback">{{ form.workflow_stage.errors.as_text }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.assigned_to.id_for_label }}">
                                Assigned To
                                <i class="bi bi-question-circle tooltip-icon" data-tooltip="The person responsible for completing this task"></i>
                            </label>
                            <div class="input-group">
                                {{ form.assigned_to }}
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-person-badge text-primary"></i>
                                </span>
                            </div>
                            <small class="text-muted">Who will be responsible for this task</small>
                            {% if form.assigned_to.errors %}
                                <div class="invalid-feedback">{{ form.assigned_to.errors.as_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Status & Priority Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-flag-fill"></i>
                        Status & Priority
                    </h3>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="{{ form.status.id_for_label }}" class="required-field">
                                Status
                                <i class="bi bi-question-circle tooltip-icon" data-tooltip="Current status of the task"></i>
                            </label>
                            <div class="input-group">
                                {{ form.status }}
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-flag text-primary"></i>
                                </span>
                            </div>
                            <small class="text-muted">Current status of the task</small>
                            {% if form.status.errors %}
                                <div class="invalid-feedback">{{ form.status.errors.as_text }}</div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.priority.id_for_label }}" class="required-field">
                                Priority
                                <i class="bi bi-question-circle tooltip-icon" data-tooltip="Higher priority tasks appear first in work queues"></i>
                            </label>
                            <div class="input-group">
                                {{ form.priority }}
                                <span class="input-group-text bg-light">
                                    <i class="bi bi-exclamation-triangle text-warning"></i>
                                </span>
                            </div>
                            <small class="text-muted">How urgent is this task (affects scheduling)</small>
                            {% if form.priority.errors %}
                                <div class="invalid-feedback">{{ form.priority.errors.as_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Duration Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-clock-fill"></i>
                        Duration
                    </h3>

                    <div class="form-group">
                        <label for="{{ form.estimated_duration.id_for_label }}" class="required-field">
                            Estimated Duration
                            <i class="bi bi-question-circle tooltip-icon" data-tooltip="Format: HH:MM:SS (e.g., 01:30:00 for 1.5 hours)"></i>
                        </label>
                        <div class="input-group">
                            {{ form.estimated_duration }}
                            <span class="input-group-text bg-light">
                                <i class="bi bi-hourglass-split text-primary"></i>
                            </span>
                        </div>
                        <small class="text-muted">How long this task is expected to take (HH:MM:SS)</small>
                        {% if form.estimated_duration.errors %}
                            <div class="invalid-feedback">{{ form.estimated_duration.errors.as_text }}</div>
                        {% endif %}
                    </div>

                    <div class="alert alert-info mt-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-info-circle-fill me-2 fs-4"></i>
                            <div>
                                <strong>Note:</strong> Task scheduling is now handled through the Scheduling module.<br>
                                After updating this task, you can schedule it in the Calendar view by going to the Scheduling section.
                            </div>
                        </div>
                        <div class="mt-2 text-end">
                            <a href="{% url 'scheduling:schedule_list' %}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-calendar-week"></i> Go to Scheduling
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-file-earmark-text-fill"></i>
                        Additional Information
                    </h3>

                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}">
                            Notes
                            <i class="bi bi-question-circle tooltip-icon" data-tooltip="Any additional information about this task"></i>
                        </label>
                        {{ form.notes }}
                        <small class="text-muted">Additional instructions, references, or context</small>
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">{{ form.notes.errors.as_text }}</div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <label for="{{ form.attachments.id_for_label }}">
                            Attachments
                            <i class="bi bi-question-circle tooltip-icon" data-tooltip="Upload files related to this task (images, documents, etc.)"></i>
                        </label>
                        {{ form.attachments }}
                        <small class="text-muted">Upload relevant files (images, documents, etc.)</small>
                        {% if form.attachments.errors %}
                            <div class="invalid-feedback">{{ form.attachments.errors.as_text }}</div>
                        {% endif %}
                    </div>

                    <!-- Hidden Fields -->
                    {{ form.required_skills }}
                    {{ form.required_equipment }}
                    {{ form.quality_checklist }}
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <div>
                        {% if task.case %}
                            <a href="{% url 'case:case_detail' task.case.case_number %}" class="btn btn-secondary">
                                <i class="bi bi-x-lg"></i> Cancel
                            </a>
                        {% else %}
                            <a href="{% url 'case:task_list' %}" class="btn btn-secondary">
                                <i class="bi bi-x-lg"></i> Cancel
                            </a>
                        {% endif %}
                    </div>
                    <div>
                        <a href="{% url 'case:task_detail' task.id %}" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-eye"></i> View Task
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg"></i> Update Task
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        document.querySelectorAll('.tooltip-icon').forEach(icon => {
            icon.addEventListener('mouseenter', function() {
                this.classList.add('text-primary');
            });
            icon.addEventListener('mouseleave', function() {
                this.classList.remove('text-primary');
            });
        });

        // Initialize Select2 with custom styling
        $('.select2, #id_case, #id_case_item, #id_workflow_stage, #id_assigned_to, #id_status, #id_priority').select2({
            theme: 'classic',
            width: '100%',
            placeholder: 'Select an option',
            allowClear: true
        }).on('select2:open', function() {
            // Add animation when dropdown opens
            document.querySelector('.select2-dropdown').classList.add('animate__animated', 'animate__fadeIn');
        });

        // Update case_item dropdown when case is selected
        $('#id_case').on('change', function() {
            const caseId = $(this).val();
            const caseItemSelect = $('#id_case_item');
            const loadingOption = new Option('Loading items...', '', true, true);

            // Show loading state
            caseItemSelect.empty().append(loadingOption).trigger('change');
            caseItemSelect.prop('disabled', true);

            // Add loading spinner to the case item field
            const caseItemField = document.querySelector('.case-item-field');
            caseItemField.classList.add('loading-field');

            if (caseId) {
                const apiUrl = `/case/api/case/${caseId}/items/`;

                // Fetch case items for the selected case
                $.ajax({
                    url: apiUrl,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        // Clear loading option
                        caseItemSelect.empty();
                        caseItemSelect.append(new Option('Select Case Item', '', true, true));

                        // Add options for each case item
                        if (data && data.length > 0) {
                            $.each(data, function(index, item) {
                                const statusBadge = getStatusBadge(item.status);
                                const option = new Option(`${item.item_name} (${item.quantity}) ${statusBadge}`, item.id);
                                caseItemSelect.append(option);
                            });

                            // Show success message
                            showNotification('success', `Found ${data.length} items for this case`);

                            // If editing an existing task with a case_item, select it
                            const currentCaseItemId = '{{ task.case_item.id|default:"" }}';
                            if (currentCaseItemId) {
                                caseItemSelect.val(currentCaseItemId).trigger('change');
                            }
                        } else {
                            caseItemSelect.append(new Option('No items found for this case', '', true, true));
                            showNotification('warning', 'No items found for this case');
                        }

                        // Enable select and remove loading state
                        caseItemSelect.prop('disabled', false);
                        caseItemField.classList.remove('loading-field');
                    },
                    error: function(xhr, status, error) {
                        caseItemSelect.empty();
                        caseItemSelect.append(new Option('Select Case Item', '', true, true));
                        caseItemSelect.append(new Option('Error loading items', '', false, false));

                        showNotification('danger', `Failed to load items: ${status}`);
                        console.error('API error:', xhr.responseText);

                        // Enable select and remove loading state
                        caseItemSelect.prop('disabled', false);
                        caseItemField.classList.remove('loading-field');
                    }
                });
            } else {
                // No case selected
                caseItemSelect.empty();
                caseItemSelect.append(new Option('Select Case Item', '', true, true));
                caseItemSelect.append(new Option('Please select a case first', '', false, false));

                showNotification('info', 'Please select a case first');

                // Enable select and remove loading state
                caseItemSelect.prop('disabled', false);
                caseItemField.classList.remove('loading-field');
            }
        });

        // Helper function to get status badge HTML
        function getStatusBadge(status) {
            const statusColors = {
                'pending': 'secondary',
                'in_progress': 'primary',
                'completed': 'success',
                'cancelled': 'danger'
            };
            const color = statusColors[status] || 'secondary';
            return `[${status}]`;
        }

        // Helper function to show notifications
        function showNotification(type, message) {
            const notificationArea = document.getElementById('notification-area') || createNotificationArea();
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show`;
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi bi-${getIconForType(type)} me-2"></i>
                    <div>${message}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            notificationArea.appendChild(notification);

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        }

        function getIconForType(type) {
            switch(type) {
                case 'success': return 'check-circle-fill';
                case 'warning': return 'exclamation-triangle-fill';
                case 'danger': return 'x-circle-fill';
                default: return 'info-circle-fill';
            }
        }

        function createNotificationArea() {
            const area = document.createElement('div');
            area.id = 'notification-area';
            area.className = 'position-fixed top-0 end-0 p-3';
            area.style.zIndex = '1050';
            area.style.maxWidth = '300px';
            document.body.appendChild(area);
            return area;
        }

        // Priority Color Coding with animation
        const prioritySelect = document.querySelector('#id_priority');
        if (prioritySelect) {
            const updatePriorityStyle = () => {
                const value = prioritySelect.value;
                // Remove all existing classes
                prioritySelect.className = 'form-select';

                // Add appropriate classes based on priority
                switch(value) {
                    case '4': // Urgent
                        prioritySelect.classList.add('bg-danger', 'text-white');
                        prioritySelect.style.fontWeight = 'bold';
                        break;
                    case '3': // High
                        prioritySelect.classList.add('bg-warning');
                        prioritySelect.style.fontWeight = 'bold';
                        break;
                    case '2': // Medium
                        prioritySelect.classList.add('bg-info', 'text-white');
                        prioritySelect.style.fontWeight = 'normal';
                        break;
                    case '1': // Low
                        prioritySelect.classList.add('bg-success', 'text-white');
                        prioritySelect.style.fontWeight = 'normal';
                        break;
                }

                // Add subtle animation
                prioritySelect.animate([{opacity: 0.7}, {opacity: 1}], {
                    duration: 300,
                    easing: 'ease-in-out'
                });
            };

            prioritySelect.addEventListener('change', updatePriorityStyle);
            // Initial styling
            updatePriorityStyle();
        }

        // Status Color Coding
        const statusSelect = document.querySelector('#id_status');
        if (statusSelect) {
            function updateStatusStyle() {
                const value = statusSelect.value;
                const statusColors = {
                    'pending': 'secondary',
                    'in_progress': 'primary',
                    'completed': 'success',
                    'delayed': 'warning',
                    'cancelled': 'danger'
                };

                // Reset styles
                statusSelect.style.backgroundColor = '';
                statusSelect.style.color = '';
                statusSelect.style.fontWeight = '';

                // Apply new styles based on status
                const color = statusColors[value] || 'secondary';
                if (color === 'primary' || color === 'success' || color === 'danger') {
                    statusSelect.style.color = 'white';
                }

                if (color === 'warning' || color === 'delayed') {
                    statusSelect.style.color = 'black';
                }

                statusSelect.style.backgroundColor = `var(--${color})`;
                statusSelect.style.fontWeight = (color === 'danger' || color === 'warning') ? 'bold' : 'normal';
            }

            statusSelect.addEventListener('change', updateStatusStyle);
            // Initial styling
            updateStatusStyle();
        }

        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('.required-field');

            // Check all required fields
            requiredFields.forEach(label => {
                const inputId = label.getAttribute('for');
                const input = document.getElementById(inputId) || label.closest('.form-group').querySelector('input, select, textarea');

                if (input && (!input.value || input.value.trim() === '')) {
                    isValid = false;
                    input.classList.add('is-invalid');

                    // Create or update error message
                    let errorDiv = input.nextElementSibling;
                    if (!errorDiv || !errorDiv.classList.contains('invalid-feedback')) {
                        errorDiv = document.createElement('div');
                        errorDiv.className = 'invalid-feedback';
                        input.parentNode.insertBefore(errorDiv, input.nextSibling);
                    }
                    errorDiv.textContent = 'This field is required';
                }
            });

            if (!isValid) {
                event.preventDefault();
                event.stopPropagation();

                // Scroll to first error
                const firstError = document.querySelector('.is-invalid');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstError.focus();
                }

                // Show error message at top of form
                showNotification('danger', 'Please fix the errors in the form before submitting.');
            }
        });

        // Clear validation errors when input changes
        form.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('input', function() {
                this.classList.remove('is-invalid');
                const errorDiv = this.nextElementSibling;
                if (errorDiv && errorDiv.classList.contains('invalid-feedback')) {
                    errorDiv.textContent = '';
                }
            });
        });

        // Add section hover effects
        document.querySelectorAll('.form-section').forEach(section => {
            section.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
                this.style.boxShadow = '0 6px 12px -2px rgba(0, 0, 0, 0.1)';
            });

            section.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.05)';
            });
        });

        // Add CSS for loading state
        const style = document.createElement('style');
        style.textContent = `
            .loading-field {
                position: relative;
            }
            .loading-field::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(255, 255, 255, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10;
            }
            .loading-field::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 24px;
                height: 24px;
                border: 3px solid var(--gray-300);
                border-top: 3px solid var(--primary);
                border-radius: 50%;
                animation: spin 1s linear infinite;
                z-index: 11;
            }
            @keyframes spin {
                from { transform: translate(-50%, -50%) rotate(0deg); }
                to { transform: translate(-50%, -50%) rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // Trigger change event on page load if case is already selected
        if ($('#id_case').val()) {
            $('#id_case').trigger('change');
        }
    });
</script>
{% endblock %}