"""
Management command to check inventory levels and generate alerts/recommendations
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
import logging

from items.services import (
    MaterialRequirementPlanningService, 
    StockAlertService, 
    CostTrackingService
)

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Check inventory levels and generate alerts and purchase recommendations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days-ahead',
            type=int,
            default=30,
            help='Number of days to look ahead for material planning (default: 30)'
        )
        parser.add_argument(
            '--send-alerts',
            action='store_true',
            help='Send email alerts for critical stock levels'
        )
        parser.add_argument(
            '--create-purchase-orders',
            action='store_true',
            help='Automatically create purchase orders for urgent recommendations'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Verbose output'
        )

    def handle(self, *args, **options):
        self.verbosity = options['verbosity']
        self.verbose = options['verbose']
        
        try:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Starting inventory check at {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )
            )
            
            # Initialize services
            alert_service = StockAlertService()
            mrp_service = MaterialRequirementPlanningService()
            
            # Check stock levels
            self.stdout.write("Checking stock levels...")
            alert_summary = alert_service.generate_alert_summary()
            
            # Display alert summary
            self._display_alert_summary(alert_summary)
            
            # Generate purchase recommendations
            self.stdout.write("Generating purchase recommendations...")
            days_ahead = options['days_ahead']
            recommendations = mrp_service.generate_purchase_recommendations(days_ahead)
            
            # Display recommendations summary
            self._display_recommendations_summary(recommendations)
            
            # Send alerts if requested
            if options['send_alerts']:
                self._send_email_alerts(alert_summary, recommendations)
            
            # Create purchase orders if requested
            if options['create_purchase_orders']:
                self._create_urgent_purchase_orders(recommendations)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"Inventory check completed at {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )
            )
            
        except Exception as e:
            logger.error(f"Error in inventory check command: {str(e)}")
            raise CommandError(f"Inventory check failed: {str(e)}")

    def _display_alert_summary(self, alert_summary):
        """Display stock alert summary"""
        self.stdout.write("\n" + "="*50)
        self.stdout.write(self.style.HTTP_INFO("STOCK ALERT SUMMARY"))
        self.stdout.write("="*50)
        
        self.stdout.write(f"Total Alerts: {alert_summary['total_alerts']}")
        
        if alert_summary['critical_count'] > 0:
            self.stdout.write(
                self.style.ERROR(f"Critical Alerts: {alert_summary['critical_count']}")
            )
        
        if alert_summary['low_count'] > 0:
            self.stdout.write(
                self.style.WARNING(f"Low Stock Alerts: {alert_summary['low_count']}")
            )
        
        if alert_summary['reorder_count'] > 0:
            self.stdout.write(
                self.style.NOTICE(f"Reorder Alerts: {alert_summary['reorder_count']}")
            )
        
        if alert_summary['overstocked_count'] > 0:
            self.stdout.write(f"Overstocked Items: {alert_summary['overstocked_count']}")
        
        # Display critical alerts details
        if alert_summary['alerts']['critical'] and self.verbose:
            self.stdout.write("\nCRITICAL ALERTS:")
            for alert in alert_summary['alerts']['critical'][:10]:  # Show first 10
                item_name = alert['item'].name
                severity = alert['severity']
                current_stock = alert['current_stock']
                self.stdout.write(
                    self.style.ERROR(f"  - {item_name}: {current_stock} ({severity})")
                )

    def _display_recommendations_summary(self, recommendations):
        """Display purchase recommendations summary"""
        self.stdout.write("\n" + "="*50)
        self.stdout.write(self.style.HTTP_INFO("PURCHASE RECOMMENDATIONS"))
        self.stdout.write("="*50)
        
        if not recommendations:
            self.stdout.write(self.style.SUCCESS("No purchase recommendations needed."))
            return
        
        # Group by priority
        urgent = [r for r in recommendations if r.priority == 'urgent']
        high = [r for r in recommendations if r.priority == 'high']
        medium = [r for r in recommendations if r.priority == 'medium']
        low = [r for r in recommendations if r.priority == 'low']
        
        self.stdout.write(f"Total Recommendations: {len(recommendations)}")
        
        if urgent:
            self.stdout.write(
                self.style.ERROR(f"Urgent: {len(urgent)} items")
            )
            total_urgent_cost = sum(r.estimated_cost for r in urgent)
            self.stdout.write(f"  Total Cost: ${total_urgent_cost:.2f}")
        
        if high:
            self.stdout.write(
                self.style.WARNING(f"High Priority: {len(high)} items")
            )
        
        if medium:
            self.stdout.write(f"Medium Priority: {len(medium)} items")
        
        if low:
            self.stdout.write(f"Low Priority: {len(low)} items")
        
        # Display urgent recommendations details
        if urgent and self.verbose:
            self.stdout.write("\nURGENT RECOMMENDATIONS:")
            for rec in urgent[:5]:  # Show first 5
                self.stdout.write(
                    self.style.ERROR(
                        f"  - {rec.raw_material.name}: {rec.recommended_quantity} "
                        f"from {rec.supplier.name} (${rec.estimated_cost:.2f})"
                    )
                )

    def _send_email_alerts(self, alert_summary, recommendations):
        """Send email alerts for critical issues"""
        try:
            if not hasattr(settings, 'EMAIL_HOST') or not settings.EMAIL_HOST:
                self.stdout.write(
                    self.style.WARNING("Email not configured. Skipping email alerts.")
                )
                return
            
            critical_count = alert_summary['critical_count']
            urgent_recommendations = [r for r in recommendations if r.priority == 'urgent']
            
            if critical_count == 0 and len(urgent_recommendations) == 0:
                self.stdout.write("No critical alerts to send.")
                return
            
            # Prepare email content
            subject = f"Dental Lab Inventory Alert - {critical_count} Critical Issues"
            
            message = f"""
Inventory Alert Summary - {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}

CRITICAL STOCK ALERTS: {critical_count}
URGENT PURCHASE RECOMMENDATIONS: {len(urgent_recommendations)}

Please review the inventory dashboard for detailed information.

This is an automated message from the Dental Lab Management System.
            """
            
            # Get admin email addresses (you may want to configure this)
            recipient_list = getattr(settings, 'INVENTORY_ALERT_EMAILS', ['<EMAIL>'])
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=recipient_list,
                fail_silently=False,
            )
            
            self.stdout.write(
                self.style.SUCCESS(f"Email alerts sent to {len(recipient_list)} recipients.")
            )
            
        except Exception as e:
            logger.error(f"Error sending email alerts: {str(e)}")
            self.stdout.write(
                self.style.ERROR(f"Failed to send email alerts: {str(e)}")
            )

    def _create_urgent_purchase_orders(self, recommendations):
        """Automatically create purchase orders for urgent recommendations"""
        try:
            urgent_recommendations = [r for r in recommendations if r.priority == 'urgent']
            
            if not urgent_recommendations:
                self.stdout.write("No urgent recommendations to process.")
                return
            
            from billing.models import PurchaseOrder, PurchaseOrderItem
            
            # Group by supplier
            supplier_groups = {}
            for rec in urgent_recommendations:
                supplier = rec.supplier
                if supplier not in supplier_groups:
                    supplier_groups[supplier] = []
                supplier_groups[supplier].append(rec)
            
            created_orders = []
            
            for supplier, supplier_recommendations in supplier_groups.items():
                # Create purchase order
                po = PurchaseOrder.objects.create(
                    supplier=supplier,
                    order_date=timezone.now().date(),
                    expected_delivery_date=max(r.delivery_date for r in supplier_recommendations).date(),
                    status='draft',
                    notes='Auto-generated from urgent inventory recommendations'
                )
                
                # Create purchase order items
                total_amount = 0
                for rec in supplier_recommendations:
                    PurchaseOrderItem.objects.create(
                        purchase_order=po,
                        raw_material=rec.raw_material,
                        description=f'{rec.raw_material.name} - {rec.reason}',
                        quantity=rec.recommended_quantity,
                        unit=rec.raw_material.unit,
                        price_per_unit=rec.estimated_cost / rec.recommended_quantity,
                        currency=rec.raw_material.currency
                    )
                    total_amount += rec.estimated_cost
                
                # Update total amount
                po.total_amount = total_amount
                po.save()
                
                created_orders.append(po)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"Created {len(created_orders)} purchase order(s) for urgent recommendations."
                )
            )
            
            for po in created_orders:
                self.stdout.write(f"  - PO #{po.id} for {po.supplier.name}: ${po.total_amount:.2f}")
            
        except Exception as e:
            logger.error(f"Error creating purchase orders: {str(e)}")
            self.stdout.write(
                self.style.ERROR(f"Failed to create purchase orders: {str(e)}")
            )
