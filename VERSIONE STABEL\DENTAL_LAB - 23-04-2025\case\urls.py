# urls.py

from django.urls import path
from case import views
app_name = 'case'


urlpatterns = [
    path('list/', views.case_list, name='case_list'),
    path('case/<int:case_number>/', views.case_detail, name='case_detail'),

    path('create/', views.case_create, name='case_create'),
    path('case/<int:case_number>/update/', views.case_update, name='case_update'),
    path('<int:case_number>/status/', views.case_status_update, name='case_status_update'),

    path('<int:case_number>/delete/',  views.case_delete, name='case_delete'),
    path('case/calendar/', views.case_calendar, name='case_calendar'),
    # path('case/case/<int:case_number>/', views.case_detail, name='case_detail'),
    path('case_management_dashboard', views.case_management_dashboard, name='case_management_dashboard'),
    path('dashboard', views.dashboard, name='dashboard'),
    path('api/case-status/', views.case_status_data, name='case-status-data'),
    path('search/', views.search_cases, name='search_cases'),
    path('case-status-data/', views.case_status_data, name='case_status_data'),
    path('case-gantt/', views.case_gantt_data, name='case_gantt_data'),
    path('case-gantt-chart/', views.case_gantt_data, name='case_gantt_chart'),


    path('gantt/', views.case_gantt_view, name='case_gantt'),
    path('dhtmlx-gantt/', views.dhtmlx_gantt_view, name='dhtmlx_gantt_view'),

    # API endpoints për funksionalitetet AJAX
    path('api/cases/<int:case_number>/', views.case_detail_api, name='case_detail_api'),
    path('api/cases/<int:case_number>/history/', views.case_history_api, name='case_history_api'),
    path('api/cases/<int:case_number>/status/', views.case_status_update_api, name='case_status_update_api'),




    path('tryout/create/<int:case_number>/', views.tryout_create, name='tryout_create'),
    path('tryout/create/', views.tryout_create, name='tryout_create_general'),
    path('tryout/update/<int:id>/', views.tryout_update, name='tryout_update'),
    path('tryouts/', views.tryout_list, name='tryout_list'),
    path('tryout/detail/<int:id>/', views.tryout_detail, name='tryout_detail'),
    path('tryout/delete/<int:id>/', views.tryout_delete, name='tryout_delete'),

    path('case/<int:case_id>/task/create/', views.task_create, name='task_create'),
    path('task/create/', views.task_create_general, name='task_create_general'),


    path('task/<int:task_id>/update/', views.task_update, name='task_update'),
    path('task/<int:task_id>/delete/', views.TaskDeleteView.as_view(), name='task_delete'),
    path('task/<int:task_id>/', views.task_detail, name='task_detail'),
    path('tasks/', views.task_list, name='task_list'),

    # Task action URLs
    path('tasks/<int:task_id>/start/', views.task_start, name='task_start'),
    path('tasks/<int:task_id>/complete/', views.task_complete, name='task_complete'),

    path('workflow-stages/', views.WorkflowStageListView.as_view(), name='workflow_stage_list'),
    path('workflow-stages/<int:pk>/', views.WorkflowStageDetailView.as_view(), name='workflow_stage_detail'),
    path('workflow-stages/create/', views.WorkflowStageCreateView.as_view(), name='workflow_stage_create'),
    path('workflow-stages/<int:pk>/update/', views.WorkflowStageUpdateView.as_view(), name='workflow_stage_update'),
    path('workflow-stages/<int:pk>/delete/', views.WorkflowStageDeleteView.as_view(), name='workflow_stage_delete'),


    path('api/workflow-stages/<int:department_id>/', views.get_workflow_stages, name='workflow_stages'),
    path('api/workflow-stages/<int:department_id>/', views.get_workflow_stages, name='get_workflow_stages'),


    path('dentist/dashboard/', views.dentist_dashboard, name='dentist_dashboard'),
    path('dentist/create-case/', views.dentist_case_create, name='dentist_case_create'),
    path('dentist/cases/', views.dentist_case_list, name='dentist_case_list'),



    path('', views.ManagerDashboardView.as_view(), name='manager_dashboard'),
    path('performance-dashboard/', views.performance_dashboard, name='performance_dashboard'),

    path('api/patients/', views.patient_search, name='patient_search'),

    # API endpoint to fetch case items for a specific case
    path('api/case/<int:case_id>/items/', views.case_items_api, name='case_items_api'),

    # Debug URL - temporary
    path('debug-teeth/', views.debug_teeth_selection, name='debug_teeth'),


]