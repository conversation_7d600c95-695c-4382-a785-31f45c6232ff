"""
Workflow Automation Service
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError

from .state_machine import WorkflowStateMachine, StateTransition, TransitionContext, StateTransitionResult
from .exceptions import WorkflowError, BusinessRuleViolation
from .validators import CentralizedValidationService

logger = logging.getLogger(__name__)


class WorkflowAutomationService:
    """
    Service for automating workflow processes
    """
    
    def __init__(self):
        self.state_machines: Dict[str, WorkflowStateMachine] = {}
        self._initialize_state_machines()
    
    def _initialize_state_machines(self):
        """Initialize state machines for different entity types"""
        self._setup_case_state_machine()
        self._setup_task_state_machine()
        self._setup_schedule_state_machine()
    
    def _setup_case_state_machine(self):
        """Setup state machine for Case entities"""
        case_sm = WorkflowStateMachine('case')
        
        # Define case transitions
        transitions = [
            # Initial transitions
            StateTransition('pending_acceptance', 'on_hold', auto_trigger=False),
            StateTransition('pending_acceptance', 'in_progress', 
                          condition=self._case_can_start,
                          action=self._case_start_action,
                          auto_trigger=True, priority=1),
            
            # Progress transitions
            StateTransition('in_progress', 'quality_check',
                          condition=self._case_work_completed,
                          action=self._case_quality_check_action,
                          auto_trigger=True, priority=2),
            
            StateTransition('quality_check', 'revision_needed',
                          condition=self._case_quality_failed,
                          action=self._case_revision_action),
            
            StateTransition('quality_check', 'ready_to_ship',
                          condition=self._case_quality_passed,
                          action=self._case_ready_ship_action,
                          auto_trigger=True, priority=3),
            
            StateTransition('revision_needed', 'in_progress',
                          action=self._case_restart_action),
            
            # Completion transitions
            StateTransition('ready_to_ship', 'shipped',
                          action=self._case_ship_action),
            
            StateTransition('shipped', 'delivered',
                          action=self._case_deliver_action),
            
            StateTransition('delivered', 'completed',
                          action=self._case_complete_action,
                          auto_trigger=True, priority=4),
            
            # Hold and cancel transitions
            StateTransition('in_progress', 'on_hold'),
            StateTransition('on_hold', 'in_progress'),
            StateTransition('pending_acceptance', 'cancelled'),
            StateTransition('on_hold', 'cancelled'),
        ]
        
        for transition in transitions:
            case_sm.add_transition(transition)
        
        # Add validators
        case_sm.add_global_validator(self._validate_case_transition)
        case_sm.add_state_validator('completed', self._validate_case_completion)
        
        # Add state actions
        case_sm.add_state_action('in_progress', self._on_case_in_progress)
        case_sm.add_state_action('completed', self._on_case_completed)
        
        self.state_machines['case'] = case_sm
    
    def _setup_task_state_machine(self):
        """Setup state machine for Task entities"""
        task_sm = WorkflowStateMachine('task')
        
        # Define task transitions
        transitions = [
            StateTransition('pending', 'in_progress',
                          condition=self._task_can_start,
                          action=self._task_start_action),
            
            StateTransition('in_progress', 'paused',
                          action=self._task_pause_action),
            
            StateTransition('paused', 'in_progress',
                          action=self._task_resume_action),
            
            StateTransition('in_progress', 'review',
                          condition=self._task_work_completed,
                          action=self._task_review_action,
                          auto_trigger=True),
            
            StateTransition('review', 'completed',
                          condition=self._task_review_passed,
                          action=self._task_complete_action,
                          auto_trigger=True),
            
            StateTransition('review', 'in_progress',
                          condition=self._task_review_failed,
                          action=self._task_rework_action),
            
            StateTransition('pending', 'blocked',
                          condition=self._task_is_blocked),
            
            StateTransition('blocked', 'pending',
                          condition=self._task_unblocked),
            
            StateTransition('pending', 'cancelled'),
            StateTransition('in_progress', 'cancelled'),
        ]
        
        for transition in transitions:
            task_sm.add_transition(transition)
        
        # Add validators and actions
        task_sm.add_global_validator(self._validate_task_transition)
        task_sm.add_state_action('completed', self._on_task_completed)
        
        self.state_machines['task'] = task_sm
    
    def _setup_schedule_state_machine(self):
        """Setup state machine for Schedule entities"""
        schedule_sm = WorkflowStateMachine('schedule')
        
        # Define schedule transitions
        transitions = [
            StateTransition('pending', 'in_progress',
                          condition=self._schedule_can_start,
                          action=self._schedule_start_action,
                          auto_trigger=True),
            
            StateTransition('in_progress', 'completed',
                          condition=self._schedule_all_items_completed,
                          action=self._schedule_complete_action,
                          auto_trigger=True),
            
            StateTransition('in_progress', 'delayed',
                          condition=self._schedule_is_delayed,
                          action=self._schedule_delay_action,
                          auto_trigger=True),
            
            StateTransition('delayed', 'in_progress',
                          action=self._schedule_resume_action),
            
            StateTransition('pending', 'cancelled'),
            StateTransition('in_progress', 'cancelled'),
        ]
        
        for transition in transitions:
            schedule_sm.add_transition(transition)
        
        self.state_machines['schedule'] = schedule_sm
    
    # Case transition conditions and actions
    def _case_can_start(self, case) -> bool:
        """Check if case can start"""
        return (
            case.workflow_template is not None and
            case.current_stage is not None and
            case.assigned_technicians.exists()
        )
    
    def _case_work_completed(self, case) -> bool:
        """Check if all case work is completed"""
        if not case.current_stage:
            return False
        
        # Check if all tasks in current stage are completed
        stage_tasks = case.tasks.filter(workflow_stage=case.current_stage)
        return stage_tasks.exists() and all(task.status == 'completed' for task in stage_tasks)
    
    def _case_quality_passed(self, case) -> bool:
        """Check if quality check passed"""
        # Check if all tasks have passed quality check
        return all(
            task.quality_check_passed for task in case.tasks.all()
            if task.quality_check_passed is not None
        )
    
    def _case_quality_failed(self, case) -> bool:
        """Check if quality check failed"""
        return any(
            task.quality_check_passed is False for task in case.tasks.all()
        )
    
    def _case_start_action(self, case, context: TransitionContext):
        """Action when case starts"""
        case.actual_start_time = timezone.now()
        if context.auto_create_tasks:
            case.create_stage_tasks()
    
    def _case_quality_check_action(self, case, context: TransitionContext):
        """Action when case enters quality check"""
        # Create quality check tasks if needed
        pass
    
    def _case_revision_action(self, case, context: TransitionContext):
        """Action when case needs revision"""
        # Mark failed tasks for rework
        for task in case.tasks.filter(quality_check_passed=False):
            task.status = 'pending'
            task.save()
    
    def _case_ready_ship_action(self, case, context: TransitionContext):
        """Action when case is ready to ship"""
        case.ready_to_ship_date = timezone.now()
    
    def _case_ship_action(self, case, context: TransitionContext):
        """Action when case is shipped"""
        case.shipped_date = timezone.now()
    
    def _case_deliver_action(self, case, context: TransitionContext):
        """Action when case is delivered"""
        case.delivered_date = timezone.now()
    
    def _case_complete_action(self, case, context: TransitionContext):
        """Action when case is completed"""
        case.complete_case()
    
    def _case_restart_action(self, case, context: TransitionContext):
        """Action when case is restarted after revision"""
        # Reset task statuses for rework
        pass
    
    # Task transition conditions and actions
    def _task_can_start(self, task) -> bool:
        """Check if task can start"""
        return (
            task.assigned_to is not None and
            not self._task_is_blocked(task)
        )
    
    def _task_is_blocked(self, task) -> bool:
        """Check if task is blocked"""
        return bool(task.blocking_issues)
    
    def _task_unblocked(self, task) -> bool:
        """Check if task is no longer blocked"""
        return not bool(task.blocking_issues)
    
    def _task_work_completed(self, task) -> bool:
        """Check if task work is completed"""
        return task.progress >= 100
    
    def _task_review_passed(self, task) -> bool:
        """Check if task review passed"""
        return task.quality_check_passed is True
    
    def _task_review_failed(self, task) -> bool:
        """Check if task review failed"""
        return task.quality_check_passed is False
    
    def _task_start_action(self, task, context: TransitionContext):
        """Action when task starts"""
        task.start_task()
    
    def _task_pause_action(self, task, context: TransitionContext):
        """Action when task is paused"""
        task.pause_task(context.reason)
    
    def _task_resume_action(self, task, context: TransitionContext):
        """Action when task is resumed"""
        task.resume_task()
    
    def _task_review_action(self, task, context: TransitionContext):
        """Action when task enters review"""
        # Notify quality control team
        pass
    
    def _task_complete_action(self, task, context: TransitionContext):
        """Action when task is completed"""
        task.complete_task()
    
    def _task_rework_action(self, task, context: TransitionContext):
        """Action when task needs rework"""
        task.progress = 0
        task.quality_check_passed = None
    
    # Schedule transition conditions and actions
    def _schedule_can_start(self, schedule) -> bool:
        """Check if schedule can start"""
        return schedule.start_date <= timezone.now()
    
    def _schedule_all_items_completed(self, schedule) -> bool:
        """Check if all schedule items are completed"""
        items = schedule.items.all()
        return items.exists() and all(item.status == 'completed' for item in items)
    
    def _schedule_is_delayed(self, schedule) -> bool:
        """Check if schedule is delayed"""
        return timezone.now() > schedule.end_date and schedule.status != 'completed'
    
    def _schedule_start_action(self, schedule, context: TransitionContext):
        """Action when schedule starts"""
        schedule.actual_start_date = timezone.now()
    
    def _schedule_complete_action(self, schedule, context: TransitionContext):
        """Action when schedule is completed"""
        schedule.actual_end_date = timezone.now()
        schedule.update_case_status()
    
    def _schedule_delay_action(self, schedule, context: TransitionContext):
        """Action when schedule is delayed"""
        # Notify stakeholders about delay
        pass
    
    def _schedule_resume_action(self, schedule, context: TransitionContext):
        """Action when schedule resumes from delay"""
        # Update schedule dates
        pass
    
    # Validators
    def _validate_case_transition(self, case, from_state: str, to_state: str, context: TransitionContext):
        """Global validator for case transitions"""
        errors = CentralizedValidationService.validate_business_operation(
            'workflow_transition',
            case=case,
            target_stage=to_state
        )
        if errors:
            raise WorkflowError(f"Case transition validation failed: {'; '.join(errors)}")
    
    def _validate_case_completion(self, case, context: TransitionContext):
        """Validator for case completion"""
        errors = CentralizedValidationService.validate_business_operation(
            'case_completion',
            case=case
        )
        if errors:
            raise WorkflowError(f"Case completion validation failed: {'; '.join(errors)}")
    
    def _validate_task_transition(self, task, from_state: str, to_state: str, context: TransitionContext):
        """Global validator for task transitions"""
        # Add task-specific validation logic
        pass
    
    # State entry actions
    def _on_case_in_progress(self, case, context: TransitionContext):
        """Action when case enters in_progress state"""
        # Update schedule status
        if hasattr(case, 'schedule'):
            schedule_sm = self.state_machines.get('schedule')
            if schedule_sm:
                schedule_sm.auto_progress(case.schedule)
    
    def _on_case_completed(self, case, context: TransitionContext):
        """Action when case enters completed state"""
        # Trigger any completion workflows
        pass
    
    def _on_task_completed(self, task, context: TransitionContext):
        """Action when task is completed"""
        # Check if case can progress to next stage
        case_sm = self.state_machines.get('case')
        if case_sm:
            case_sm.auto_progress(task.case)
    
    # Public interface methods
    def transition_entity(self, entity_type: str, entity, target_state: str, context: TransitionContext = None) -> tuple:
        """Transition an entity to a target state"""
        if entity_type not in self.state_machines:
            raise WorkflowError(f"No state machine defined for entity type: {entity_type}")
        
        if context is None:
            context = TransitionContext()
        
        state_machine = self.state_machines[entity_type]
        return state_machine.transition_to(entity, target_state, context)
    
    def auto_progress_entity(self, entity_type: str, entity) -> List[tuple]:
        """Auto-progress an entity through its workflow"""
        if entity_type not in self.state_machines:
            raise WorkflowError(f"No state machine defined for entity type: {entity_type}")
        
        state_machine = self.state_machines[entity_type]
        return state_machine.auto_progress(entity)
    
    def get_valid_transitions(self, entity_type: str, entity) -> List[StateTransition]:
        """Get valid transitions for an entity"""
        if entity_type not in self.state_machines:
            return []
        
        state_machine = self.state_machines[entity_type]
        current_state = state_machine._get_current_state(entity)
        return state_machine.get_valid_transitions(entity, current_state)


# Global instance
workflow_automation = WorkflowAutomationService()
