{% extends 'base.html' %}
{% load i18n %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
<style>
    :root {
        --primary-color: #4361ee;
        --primary-light: rgba(67, 97, 238, 0.1);
        --secondary-color: #3f37c9;
        --success-color: #4cc9f0;
        --info-color: #4895ef;
        --warning-color: #f72585;
        --danger-color: #e63946;
        --light-color: #f8f9fa;
        --dark-color: #212529;

        --border-radius: 0.375rem;
        --border-radius-lg: 0.5rem;
        --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* Page Layout */
    .page-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 1.5rem;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding: 1.5rem;
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
    }

    .page-title {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .page-title i {
        font-size: 1.5rem;
        color: var(--primary-color);
        background-color: var(--primary-light);
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    /* Form Card */
    .form-card {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .form-header {
        padding: 1.5rem;
        background-color: var(--primary-light);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .form-title {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .form-title i {
        font-size: 1.25rem;
    }

    .form-body {
        padding: 1.5rem;
    }

    /* Form Elements */
    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .form-section:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: var(--primary-color);
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group:last-child {
        margin-bottom: 0;
    }

    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
        color: var(--dark-color);
    }

    .form-control {
        border-radius: var(--border-radius);
        border: 1px solid #ced4da;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
    }

    .form-select {
        border-radius: var(--border-radius);
        border: 1px solid #ced4da;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
    }

    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: var(--danger-color);
    }

    /* Form Actions */
    .form-actions {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
    }

    .form-actions .btn {
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        border-radius: var(--border-radius);
        transition: all 0.2s ease;
    }

    .form-actions .btn i {
        font-size: 1rem;
    }

    .form-actions .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .form-actions .btn-primary:hover {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
    }

    .form-actions .btn-secondary {
        background-color: var(--light-color);
        border-color: var(--light-color);
        color: var(--dark-color);
    }

    .form-actions .btn-secondary:hover {
        background-color: #e2e6ea;
        border-color: #e2e6ea;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .page-container {
            padding: 1rem;
        }

        .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .form-actions {
            flex-direction: column;
        }

        .form-actions .btn {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <!-- Page Header -->
    <div class="page-header animate__animated animate__fadeIn">
        <div>
            <h1 class="page-title">
                <i class="bi bi-diagram-3"></i>
                {% if form.instance.pk %}
                    {% trans "Edit Workflow Stage" %}
                {% else %}
                    {% trans "Create Workflow Stage" %}
                {% endif %}
            </h1>
            <p class="text-muted mb-0">
                {% if form.instance.pk %}
                    {% trans "Update the details of an existing workflow stage" %}
                {% else %}
                    {% trans "Define a new stage in your workflow process" %}
                {% endif %}
            </p>
        </div>
        <div>
            <a href="{% url 'case:workflow_stage_list' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> {% trans "Back to List" %}
            </a>
        </div>
    </div>

    <!-- Form Card -->
    <div class="form-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
        <div class="form-header">
            <h2 class="form-title">
                <i class="bi bi-{% if form.instance.pk %}pencil-square{% else %}plus-circle{% endif %}"></i>
                {% if form.instance.pk %}
                    {% trans "Edit" %}: {{ form.instance.name }}
                {% else %}
                    {% trans "Create New Workflow Stage" %}
                {% endif %}
            </h2>
        </div>
        <div class="form-body">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} animate__animated animate__fadeIn">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                {% csrf_token %}

                <!-- Basic Information Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i> {% trans "Basic Information" %}
                    </h3>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.name.id_for_label }}" class="form-label">{% trans "Stage Name" %} <span class="text-danger">*</span></label>
                                {{ form.name }}
                                {% if form.name.help_text %}
                                    <div class="form-text">{{ form.name.help_text }}</div>
                                {% endif %}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback">{{ form.name.errors.as_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.department.id_for_label }}" class="form-label">{% trans "Department" %} <span class="text-danger">*</span></label>
                                {{ form.department }}
                                {% if form.department.help_text %}
                                    <div class="form-text">{{ form.department.help_text }}</div>
                                {% endif %}
                                {% if form.department.errors %}
                                    <div class="invalid-feedback">{{ form.department.errors.as_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.order.id_for_label }}" class="form-label">{% trans "Order in Sequence" %} <span class="text-danger">*</span></label>
                                {{ form.order }}
                                {% if form.order.help_text %}
                                    <div class="form-text">{{ form.order.help_text }}</div>
                                {% endif %}
                                {% if form.order.errors %}
                                    <div class="invalid-feedback">{{ form.order.errors.as_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.estimated_duration.id_for_label }}" class="form-label">{% trans "Estimated Duration" %} <span class="text-danger">*</span></label>
                                {{ form.estimated_duration }}
                                <div class="form-text">{% trans "Format: HH:MM:SS (e.g., 48:00:00 for 2 days)" %}</div>
                                {% if form.estimated_duration.errors %}
                                    <div class="invalid-feedback">{{ form.estimated_duration.errors.as_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.is_critical.id_for_label }}" class="form-label">{% trans "Critical Stage" %}</label>
                                <div class="form-check form-switch">
                                    {{ form.is_critical }}
                                    <label class="form-check-label" for="{{ form.is_critical.id_for_label }}">
                                        {% trans "Mark as critical stage" %}
                                    </label>
                                </div>
                                <div class="form-text">{% trans "Critical stages are on the critical path and affect the overall workflow timeline" %}</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.complexity.id_for_label }}" class="form-label">{% trans "Complexity Level" %}</label>
                                {{ form.complexity }}
                                {% if form.complexity.help_text %}
                                    <div class="form-text">{{ form.complexity.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-card-text"></i> {% trans "Additional Information" %}
                    </h3>

                    <div class="form-group">
                        <label for="{{ form.description.id_for_label }}" class="form-label">{% trans "Description" %}</label>
                        {{ form.description }}
                        <div class="form-text">{% trans "Detailed description of what happens during this stage" %}</div>
                    </div>

                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">{% trans "Additional Notes" %}</label>
                        {{ form.notes }}
                    </div>
                </div>

                <!-- Dependencies Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-link-45deg"></i> {% trans "Dependencies" %}
                    </h3>

                    <div class="form-group">
                        <label for="{{ form.dependencies.id_for_label }}" class="form-label">{% trans "Stage Dependencies" %}</label>
                        {{ form.dependencies }}
                        <div class="form-text">{% trans "Select stages that must be completed before this stage can start" %}</div>
                    </div>
                </div>

                <!-- Resource Requirements Section -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="bi bi-tools"></i> {% trans "Resource Requirements" %}
                    </h3>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.required_skills.id_for_label }}" class="form-label">{% trans "Required Skills" %}</label>
                                {{ form.required_skills }}
                                <div class="form-text">{% trans "Skills needed for this stage" %}</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.required_equipment.id_for_label }}" class="form-label">{% trans "Required Equipment" %}</label>
                                {{ form.required_equipment }}
                                <div class="form-text">{% trans "Equipment needed for this stage" %}</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="{{ form.quality_checklist.id_for_label }}" class="form-label">{% trans "Quality Checklist" %}</label>
                        {{ form.quality_checklist }}
                        <div class="form-text">{% trans "Quality control points for this stage" %}</div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg"></i>
                        {% if form.instance.pk %}
                            {% trans "Update Stage" %}
                        {% else %}
                            {% trans "Create Stage" %}
                        {% endif %}
                    </button>
                    <a href="{% url 'case:workflow_stage_list' %}" class="btn btn-secondary">
                        <i class="bi bi-x-lg"></i> {% trans "Cancel" %}
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize form validation
        const forms = document.querySelectorAll('.needs-validation');
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });

        // Initialize select2 if available
        if (typeof $.fn.select2 !== 'undefined') {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
        }
    });
</script>
{% endblock %}