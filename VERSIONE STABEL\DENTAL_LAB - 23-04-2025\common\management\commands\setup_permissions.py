"""
Management command to set up permission groups and roles
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from common.permissions import PermissionService
from accounts.models import CustomUser

class Command(BaseCommand):
    help = 'Set up permission groups and assign roles to users'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--create-groups',
            action='store_true',
            help='Create permission groups',
        )
        parser.add_argument(
            '--assign-user',
            type=str,
            help='Email of user to assign role to',
        )
        parser.add_argument(
            '--role',
            type=str,
            help='Role to assign to user',
            choices=['super_admin', 'lab_manager', 'department_manager', 
                    'technician', 'accountant', 'dentist', 'receptionist']
        )
        parser.add_argument(
            '--list-roles',
            action='store_true',
            help='List all available roles and their permissions',
        )
        parser.add_argument(
            '--list-users',
            action='store_true',
            help='List all users and their current roles',
        )
    
    def handle(self, *args, **options):
        if options['create_groups']:
            self._create_permission_groups()
        
        elif options['list_roles']:
            self._list_roles()
        
        elif options['list_users']:
            self._list_users()
        
        elif options['assign_user'] and options['role']:
            self._assign_user_role(options['assign_user'], options['role'])
        
        else:
            self.stdout.write(
                self.style.ERROR(
                    'Please specify an action: --create-groups, --list-roles, '
                    '--list-users, or --assign-user with --role'
                )
            )
    
    def _create_permission_groups(self):
        """Create permission groups"""
        self.stdout.write('Creating permission groups...')
        
        created_groups = PermissionService.create_permission_groups()
        
        if created_groups:
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ Created {len(created_groups)} permission groups: {", ".join(created_groups)}'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('✅ All permission groups already exist')
            )
        
        # Display summary
        self.stdout.write('\n📋 Permission Groups Summary:')
        for role_name, categories in PermissionService.ROLE_PERMISSIONS.items():
            group = Group.objects.get(name=role_name)
            permission_count = group.permissions.count()
            self.stdout.write(
                f'  • {role_name}: {permission_count} permissions ({", ".join(categories)})'
            )
    
    def _list_roles(self):
        """List all available roles and their permissions"""
        self.stdout.write(self.style.SUCCESS('📋 Available Roles and Permissions:'))
        self.stdout.write('=' * 60)
        
        for role_name, categories in PermissionService.ROLE_PERMISSIONS.items():
            self.stdout.write(f'\n🔑 {role_name.upper().replace("_", " ")}')
            self.stdout.write('-' * 40)
            
            for category in categories:
                permissions = PermissionService.PERMISSION_CATEGORIES.get(category, [])
                self.stdout.write(f'  📂 {category.replace("_", " ").title()}:')
                for perm in permissions:
                    self.stdout.write(f'    • {perm.replace("_", " ").title()}')
    
    def _list_users(self):
        """List all users and their current roles"""
        self.stdout.write(self.style.SUCCESS('👥 Users and Their Roles:'))
        self.stdout.write('=' * 60)
        
        users = CustomUser.objects.filter(is_active=True).order_by('first_name', 'last_name')
        
        for user in users:
            roles = [group.name for group in user.groups.all()]
            role_display = ', '.join(roles) if roles else 'No roles assigned'
            
            user_type = 'Superuser' if user.is_superuser else dict(user.USER_TYPE_CHOICES).get(user.user_type, 'Unknown')
            
            self.stdout.write(
                f'👤 {user.get_full_name()} ({user.email})'
            )
            self.stdout.write(f'   Type: {user_type}')
            self.stdout.write(f'   Roles: {role_display}')
            self.stdout.write('')
    
    def _assign_user_role(self, email, role_name):
        """Assign role to user"""
        try:
            user = CustomUser.objects.get(email=email)
        except CustomUser.DoesNotExist:
            raise CommandError(f'User with email {email} not found')
        
        # Check if role exists
        if role_name not in PermissionService.ROLE_PERMISSIONS:
            raise CommandError(f'Role {role_name} does not exist')
        
        # Assign role
        success = PermissionService.assign_user_to_role(user, role_name)
        
        if success:
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ Successfully assigned role "{role_name}" to {user.get_full_name()} ({email})'
                )
            )
            
            # Show permissions summary
            group = Group.objects.get(name=role_name)
            permission_count = group.permissions.count()
            self.stdout.write(f'   📋 User now has {permission_count} permissions')
            
        else:
            raise CommandError(f'Failed to assign role {role_name} to user {email}')
