"""
Common services for data consistency and business logic
"""

import logging
from decimal import Decimal, ROUND_HALF_UP
from django.db import transaction, models
from django.db.models import Sum, F
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.core.cache import cache
from typing import Optional, Dict, Any, List
import hashlib
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class CurrencyService:
    """
    Enhanced currency conversion service with caching and audit trails
    """

    CACHE_TIMEOUT = 3600  # 1 hour cache timeout
    CACHE_PREFIX = 'exchange_rate'

    @classmethod
    def _get_cache_key(cls, from_currency_code: str, to_currency_code: str, date: str = None) -> str:
        """Generate cache key for exchange rate"""
        date_str = date.strftime('%Y-%m-%d') if date else 'latest'
        key_data = f"{from_currency_code}_{to_currency_code}_{date_str}"
        return f"{cls.CACHE_PREFIX}_{hashlib.md5(key_data.encode()).hexdigest()}"

    @classmethod
    def get_exchange_rate(cls, from_currency_code: str, to_currency_code: str,
                         date: datetime = None, use_cache: bool = True) -> Optional[Decimal]:
        """
        Get exchange rate with caching and fallback logic
        """
        # Same currency, no conversion needed
        if from_currency_code == to_currency_code:
            return Decimal('1.0')

        if date is None:
            date = timezone.now().date()

        # Try cache first
        if use_cache:
            cache_key = cls._get_cache_key(from_currency_code, to_currency_code, date)
            cached_rate = cache.get(cache_key)
            if cached_rate is not None:
                logger.debug(f"Cache hit for {from_currency_code} to {to_currency_code}")
                return cached_rate

        # Get from database
        from items.models import ExchangeRate

        try:
            # Try direct rate
            rate_obj = ExchangeRate.objects.filter(
                from_currency__code=from_currency_code,
                to_currency__code=to_currency_code,
                date__lte=date
            ).order_by('-date').first()

            if rate_obj:
                rate = rate_obj.rate
                cls._cache_rate(from_currency_code, to_currency_code, date, rate)
                return rate

            # Try inverse rate
            inverse_rate_obj = ExchangeRate.objects.filter(
                from_currency__code=to_currency_code,
                to_currency__code=from_currency_code,
                date__lte=date
            ).order_by('-date').first()

            if inverse_rate_obj:
                rate = Decimal('1.0') / inverse_rate_obj.rate
                cls._cache_rate(from_currency_code, to_currency_code, date, rate)
                return rate

            # Try conversion through base currency (ALL)
            base_currency = 'ALL'
            if from_currency_code != base_currency and to_currency_code != base_currency:
                from_to_base = cls.get_exchange_rate(from_currency_code, base_currency, date, use_cache)
                base_to_target = cls.get_exchange_rate(base_currency, to_currency_code, date, use_cache)

                if from_to_base and base_to_target:
                    rate = from_to_base * base_to_target
                    cls._cache_rate(from_currency_code, to_currency_code, date, rate)
                    return rate

            logger.warning(f"No exchange rate found for {from_currency_code} to {to_currency_code} on {date}")
            return None

        except Exception as e:
            logger.error(f"Error getting exchange rate: {e}")
            return None

    @classmethod
    def _cache_rate(cls, from_currency_code: str, to_currency_code: str,
                   date: datetime, rate: Decimal):
        """Cache exchange rate"""
        cache_key = cls._get_cache_key(from_currency_code, to_currency_code, date)
        cache.set(cache_key, rate, cls.CACHE_TIMEOUT)

    @classmethod
    def convert_amount(cls, amount: Decimal, from_currency_code: str,
                      to_currency_code: str, date: datetime = None) -> Dict[str, Any]:
        """
        Convert amount with detailed result including audit information
        """
        result = {
            'original_amount': amount,
            'from_currency': from_currency_code,
            'to_currency': to_currency_code,
            'date': date or timezone.now().date(),
            'converted_amount': None,
            'exchange_rate': None,
            'success': False,
            'error': None,
            'conversion_method': None
        }

        try:
            # Validate inputs
            if amount < 0:
                result['error'] = "Amount cannot be negative"
                return result

            # Get exchange rate
            rate = cls.get_exchange_rate(from_currency_code, to_currency_code, date)

            if rate is None:
                result['error'] = f"No exchange rate available from {from_currency_code} to {to_currency_code}"
                return result

            # Perform conversion
            converted_amount = (amount * rate).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

            result.update({
                'converted_amount': converted_amount,
                'exchange_rate': rate,
                'success': True,
                'conversion_method': 'direct' if rate != Decimal('1.0') else 'same_currency'
            })

            # Log conversion for audit
            cls._log_conversion(result)

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"Currency conversion error: {e}")

        return result

    @classmethod
    def _log_conversion(cls, conversion_result: Dict[str, Any]):
        """Log currency conversion for audit trail"""
        if conversion_result['success'] and conversion_result['conversion_method'] != 'same_currency':
            logger.info(
                f"Currency conversion: {conversion_result['original_amount']} "
                f"{conversion_result['from_currency']} -> "
                f"{conversion_result['converted_amount']} "
                f"{conversion_result['to_currency']} "
                f"(rate: {conversion_result['exchange_rate']})"
            )

    @classmethod
    def clear_cache(cls, from_currency_code: str = None, to_currency_code: str = None):
        """Clear exchange rate cache"""
        if from_currency_code and to_currency_code:
            # Clear specific rate
            cache_key = cls._get_cache_key(from_currency_code, to_currency_code)
            cache.delete(cache_key)
        else:
            # Clear all exchange rate cache
            cache.delete_many([key for key in cache._cache.keys() if key.startswith(cls.CACHE_PREFIX)])

    @classmethod
    def update_exchange_rate(cls, from_currency_code: str, to_currency_code: str,
                           rate: Decimal, date: datetime = None) -> bool:
        """
        Update exchange rate and clear related cache
        """
        from items.models import Currency, ExchangeRate

        try:
            with transaction.atomic():
                from_currency = Currency.objects.get(code=from_currency_code)
                to_currency = Currency.objects.get(code=to_currency_code)

                if date is None:
                    date = timezone.now().date()

                # Create or update exchange rate
                exchange_rate, created = ExchangeRate.objects.update_or_create(
                    from_currency=from_currency,
                    to_currency=to_currency,
                    date=date,
                    defaults={'rate': rate}
                )

                # Clear cache for this rate
                cls.clear_cache(from_currency_code, to_currency_code)
                cls.clear_cache(to_currency_code, from_currency_code)  # Clear inverse too

                action = "Created" if created else "Updated"
                logger.info(f"{action} exchange rate: {from_currency_code} to {to_currency_code} = {rate}")

                return True

        except Exception as e:
            logger.error(f"Failed to update exchange rate: {e}")
            return False

    @classmethod
    def get_supported_currencies(cls) -> List[str]:
        """Get list of supported currency codes"""
        from items.models import Currency
        return list(Currency.objects.values_list('code', flat=True))

    @classmethod
    def validate_currency_pair(cls, from_currency_code: str, to_currency_code: str) -> Dict[str, Any]:
        """Validate if currency pair is supported"""
        supported_currencies = cls.get_supported_currencies()

        result = {
            'valid': True,
            'errors': []
        }

        if from_currency_code not in supported_currencies:
            result['valid'] = False
            result['errors'].append(f"Currency {from_currency_code} is not supported")

        if to_currency_code not in supported_currencies:
            result['valid'] = False
            result['errors'].append(f"Currency {to_currency_code} is not supported")

        return result

class FinancialTransactionService:
    """
    Service for atomic financial transaction processing
    """

    @staticmethod
    @transaction.atomic
    def process_payment(payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process payment with atomic transaction handling
        """
        from finance.models import Payment, Account
        from Dentists.models import Dentist
        from items.models import Currency

        result = {
            'success': False,
            'payment': None,
            'errors': [],
            'warnings': []
        }

        try:
            # Validate required fields
            required_fields = ['dentist_id', 'amount', 'currency_id', 'account_id', 'date']
            for field in required_fields:
                if not payment_data.get(field):
                    result['errors'].append(f"Missing required field: {field}")

            if result['errors']:
                return result

            # Validate entities exist
            try:
                dentist = Dentist.objects.get(id=payment_data['dentist_id'])
                currency = Currency.objects.get(id=payment_data['currency_id'])
                account = Account.objects.get(id=payment_data['account_id'])
            except (Dentist.DoesNotExist, Currency.DoesNotExist, Account.DoesNotExist) as e:
                result['errors'].append(f"Invalid reference: {e}")
                return result

            # Validate amount
            amount = Decimal(str(payment_data['amount']))
            if amount <= 0:
                result['errors'].append("Payment amount must be positive")
                return result

            # Check account currency compatibility
            if account.currency != currency:
                # Convert amount to account currency
                conversion = CurrencyService.convert_amount(
                    amount, currency.code, account.currency.code, payment_data['date']
                )

                if not conversion['success']:
                    result['errors'].append(f"Currency conversion failed: {conversion['error']}")
                    return result

                account_amount = conversion['converted_amount']
                result['warnings'].append(
                    f"Amount converted from {amount} {currency.code} to "
                    f"{account_amount} {account.currency.code}"
                )
            else:
                account_amount = amount

            # Create payment
            payment = Payment.objects.create(
                dentist=dentist,
                amount=amount,
                currency=currency,
                account=account,
                date=payment_data['date'],
                payment_method=payment_data.get('payment_method', 'cash'),
                reference=payment_data.get('reference', '')
            )

            # Update account balance
            account.adjust_balance(account_amount, 'add')

            result.update({
                'success': True,
                'payment': payment,
                'account_amount': account_amount
            })

            logger.info(f"Payment processed: {amount} {currency.code} from {dentist}")

        except Exception as e:
            result['errors'].append(f"Payment processing failed: {e}")
            logger.error(f"Payment processing error: {e}")

        return result

    @staticmethod
    @transaction.atomic
    def allocate_payment_to_invoice(payment_id: int, invoice_id: int,
                                   allocation_amount: Decimal) -> Dict[str, Any]:
        """
        Allocate payment to invoice with validation and currency conversion
        """
        from finance.models import Payment, InvoicePayment
        from billing.models import Invoice

        result = {
            'success': False,
            'allocation': None,
            'errors': [],
            'warnings': []
        }

        try:
            # Get payment and invoice
            try:
                payment = Payment.objects.get(id=payment_id)
                invoice = Invoice.objects.get(id=invoice_id)
            except (Payment.DoesNotExist, Invoice.DoesNotExist) as e:
                result['errors'].append(f"Invalid reference: {e}")
                return result

            # Validate allocation amount
            if allocation_amount <= 0:
                result['errors'].append("Allocation amount must be positive")
                return result

            # Check if payment has sufficient remaining amount
            remaining_payment = CalculationService.calculate_payment_allocation_remaining(payment)
            if allocation_amount > remaining_payment:
                result['errors'].append(
                    f"Allocation amount {allocation_amount} exceeds remaining payment amount {remaining_payment}"
                )
                return result

            # Check if invoice needs this payment
            invoice_remaining = invoice.total_amount - (invoice.get_paid_amount() or Decimal('0.00'))
            if allocation_amount > invoice_remaining:
                result['errors'].append(
                    f"Allocation amount {allocation_amount} exceeds invoice remaining amount {invoice_remaining}"
                )
                return result

            # Handle currency conversion if needed
            final_amount = allocation_amount
            exchange_rate = None
            is_converted = False

            if payment.currency != invoice.currency:
                conversion = CurrencyService.convert_amount(
                    allocation_amount, payment.currency.code, invoice.currency.code, payment.date
                )

                if not conversion['success']:
                    result['errors'].append(f"Currency conversion failed: {conversion['error']}")
                    return result

                final_amount = conversion['converted_amount']
                exchange_rate = conversion['exchange_rate']
                is_converted = True

                result['warnings'].append(
                    f"Amount converted from {allocation_amount} {payment.currency.code} to "
                    f"{final_amount} {invoice.currency.code}"
                )

            # Create allocation
            allocation = InvoicePayment.objects.create(
                payment=payment,
                invoice=invoice,
                amount=final_amount,
                original_amount=allocation_amount if is_converted else None,
                original_currency=payment.currency if is_converted else None,
                exchange_rate=exchange_rate,
                is_converted=is_converted
            )

            # Update invoice status
            FinancialTransactionService._update_invoice_status(invoice)

            result.update({
                'success': True,
                'allocation': allocation,
                'final_amount': final_amount
            })

            logger.info(f"Payment allocation created: {final_amount} from Payment {payment_id} to Invoice {invoice_id}")

        except Exception as e:
            result['errors'].append(f"Payment allocation failed: {e}")
            logger.error(f"Payment allocation error: {e}")

        return result

    @staticmethod
    def _update_invoice_status(invoice):
        """Update invoice status based on payment amount"""
        total_paid = invoice.get_paid_amount() or Decimal('0.00')

        if total_paid >= invoice.total_amount:
            invoice.status = 'paid'
        elif total_paid > 0:
            invoice.status = 'partial'
        else:
            invoice.status = 'unpaid'

        invoice.save(update_fields=['status'])

    @staticmethod
    @transaction.atomic
    def reverse_payment_allocation(allocation_id: int) -> Dict[str, Any]:
        """
        Reverse a payment allocation
        """
        from finance.models import InvoicePayment

        result = {
            'success': False,
            'errors': []
        }

        try:
            allocation = InvoicePayment.objects.get(id=allocation_id)
            invoice = allocation.invoice

            # Delete allocation
            allocation.delete()

            # Update invoice status
            FinancialTransactionService._update_invoice_status(invoice)

            result['success'] = True
            logger.info(f"Payment allocation {allocation_id} reversed")

        except InvoicePayment.DoesNotExist:
            result['errors'].append("Allocation not found")
        except Exception as e:
            result['errors'].append(f"Reversal failed: {e}")
            logger.error(f"Payment allocation reversal error: {e}")

        return result

    @staticmethod
    def get_payment_summary(payment_id: int) -> Dict[str, Any]:
        """
        Get comprehensive payment summary with allocations
        """
        from finance.models import Payment

        try:
            payment = Payment.objects.get(id=payment_id)

            # Calculate allocations
            total_allocated = Decimal('0.00')
            allocations = []

            for allocation in payment.invoice_allocations.all():
                total_allocated += allocation.amount
                allocations.append({
                    'id': allocation.id,
                    'invoice_id': allocation.invoice.id,
                    'amount': allocation.amount,
                    'original_amount': allocation.original_amount,
                    'is_converted': allocation.is_converted,
                    'exchange_rate': allocation.exchange_rate,
                    'allocation_date': allocation.allocation_date
                })

            remaining = payment.amount - total_allocated

            return {
                'payment_id': payment.id,
                'total_amount': payment.amount,
                'currency': payment.currency.code,
                'total_allocated': total_allocated,
                'remaining_amount': remaining,
                'allocations': allocations,
                'is_fully_allocated': remaining <= Decimal('0.01')
            }

        except Payment.DoesNotExist:
            return {'error': 'Payment not found'}
        except Exception as e:
            logger.error(f"Payment summary error: {e}")
            return {'error': str(e)}

class DataConsistencyService:
    """
    Service for maintaining data consistency across the application
    """

    @staticmethod
    @transaction.atomic
    def recalculate_invoice_total(invoice):
        """
        Recalculate and update invoice total based on invoice items
        """
        from billing.models import Invoice, InvoiceItem

        if not isinstance(invoice, Invoice):
            raise ValueError("Expected Invoice instance")

        # Calculate total from invoice items
        calculated_total = invoice.invoice_items.aggregate(
            total=Sum(F('quantity') * F('selling_price'))
        )['total'] or Decimal('0.00')

        # Update if different
        if abs(invoice.total_amount - calculated_total) > Decimal('0.01'):
            old_total = invoice.total_amount
            invoice.total_amount = calculated_total
            invoice.save(update_fields=['total_amount'])

            logger.info(f"Updated Invoice {invoice.id} total from {old_total} to {calculated_total}")
            return True

        return False

    @staticmethod
    @transaction.atomic
    def fix_all_invoice_totals():
        """
        Fix all invoice totals in the system
        """
        from billing.models import Invoice

        fixed_count = 0
        for invoice in Invoice.objects.all():
            if DataConsistencyService.recalculate_invoice_total(invoice):
                fixed_count += 1

        logger.info(f"Fixed {fixed_count} invoice totals")
        return fixed_count

    @staticmethod
    @transaction.atomic
    def create_missing_exchange_rates():
        """
        Create missing exchange rates for all currencies to base currency
        """
        from items.models import Currency, ExchangeRate

        base_currency = Currency.objects.filter(code='ALL').first()
        if not base_currency:
            logger.error("Base currency 'ALL' not found")
            return 0

        # Default exchange rates (these should be updated with real rates)
        default_rates = {
            'USD': Decimal('92.50'),  # 1 USD = 92.50 ALL
            'EUR': Decimal('101.20'), # 1 EUR = 101.20 ALL
            'GBP': Decimal('117.80'), # 1 GBP = 117.80 ALL
            'CHF': Decimal('103.45'), # 1 CHF = 103.45 ALL
            'CAD': Decimal('68.30'),  # 1 CAD = 68.30 ALL
            'AUD': Decimal('61.20'),  # 1 AUD = 61.20 ALL
            'JPY': Decimal('0.62'),   # 1 JPY = 0.62 ALL
            'CNY': Decimal('12.85'),  # 1 CNY = 12.85 ALL
            'SEK': Decimal('8.45'),   # 1 SEK = 8.45 ALL
            'NZD': Decimal('56.70'),  # 1 NZD = 56.70 ALL
        }

        created_count = 0
        for currency in Currency.objects.exclude(code='ALL'):
            # Check if exchange rate exists
            rate_exists = ExchangeRate.objects.filter(
                from_currency=currency,
                to_currency=base_currency
            ).exists()

            if not rate_exists:
                rate_value = default_rates.get(currency.code, Decimal('1.00'))
                ExchangeRate.objects.create(
                    from_currency=currency,
                    to_currency=base_currency,
                    rate=rate_value,
                    date=timezone.now().date()
                )
                created_count += 1
                logger.info(f"Created exchange rate: {currency.code} to ALL = {rate_value}")

        return created_count

    @staticmethod
    def validate_case_data(case_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Validate case data before creation/update
        """
        errors = {}

        # Check required fields
        required_fields = ['dentist', 'patient']
        for field in required_fields:
            if not case_data.get(field):
                errors.setdefault(field, []).append(f"{field} is required")

        # Validate dates
        if case_data.get('deadline') and case_data.get('received_date_time'):
            if case_data['deadline'] < case_data['received_date_time']:
                errors.setdefault('deadline', []).append("Deadline cannot be before received date")

        # Validate priority
        priority = case_data.get('priority')
        if priority is not None and not (1 <= priority <= 5):
            errors.setdefault('priority', []).append("Priority must be between 1 and 5")

        return errors

    @staticmethod
    def validate_financial_transaction(transaction_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """
        Validate financial transaction data
        """
        errors = {}

        # Check amount is positive
        amount = transaction_data.get('amount')
        if amount is not None and amount <= 0:
            errors.setdefault('amount', []).append("Amount must be positive")

        # Check currency exists
        currency = transaction_data.get('currency')
        if currency:
            from items.models import Currency
            if not Currency.objects.filter(id=currency).exists():
                errors.setdefault('currency', []).append("Invalid currency")

        # Check account exists for payments
        account = transaction_data.get('account')
        if account:
            from finance.models import Account
            if not Account.objects.filter(id=account).exists():
                errors.setdefault('account', []).append("Invalid account")

        return errors

    @staticmethod
    @transaction.atomic
    def sync_case_schedule_status(case):
        """
        Synchronize case and schedule status
        """
        from case.models import Case
        from scheduling.models import Schedule

        if not hasattr(case, 'schedule'):
            return False

        schedule = case.schedule
        status_changed = False

        # Update schedule status based on case status
        if case.status == 'completed' and schedule.status != 'completed':
            schedule.status = 'completed'
            schedule.actual_end_date = timezone.now()
            schedule.save(update_fields=['status', 'actual_end_date'])
            status_changed = True

        elif case.status == 'cancelled' and schedule.status != 'cancelled':
            schedule.status = 'cancelled'
            schedule.save(update_fields=['status'])
            status_changed = True

        elif case.status == 'in_progress' and schedule.status == 'pending':
            schedule.status = 'in_progress'
            schedule.actual_start_date = timezone.now()
            schedule.save(update_fields=['status', 'actual_start_date'])
            status_changed = True

        if status_changed:
            logger.info(f"Synchronized status for Case {case.case_number} and Schedule {schedule.id}")

        return status_changed

    @staticmethod
    @transaction.atomic
    def fix_all_status_inconsistencies():
        """
        Fix all status inconsistencies in the system
        """
        from case.models import Case
        from billing.models import Invoice

        fixed_count = 0

        # Fix case-schedule status mismatches
        for case in Case.objects.filter(schedule__isnull=False):
            if DataConsistencyService.sync_case_schedule_status(case):
                fixed_count += 1

        # Fix invoice status for cancelled cases
        for invoice in Invoice.objects.filter(case__status='cancelled').exclude(status='cancelled'):
            invoice.status = 'cancelled'
            invoice.save(update_fields=['status'])
            fixed_count += 1
            logger.info(f"Cancelled Invoice {invoice.id} for cancelled case {invoice.case.case_number}")

        return fixed_count

class CalculationService:
    """
    Service for centralized calculations
    """

    @staticmethod
    def calculate_invoice_total(invoice_items) -> Decimal:
        """
        Calculate total amount from invoice items
        """
        total = Decimal('0.00')
        for item in invoice_items:
            item_total = item.quantity * item.selling_price
            total += item_total

        return total.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_case_cost_estimate(case) -> Decimal:
        """
        Calculate estimated cost for a case based on items
        """
        total_cost = Decimal('0.00')

        for case_item in case.case_items.all():
            item_cost = case_item.item.cost() * case_item.quantity
            total_cost += item_cost

        return total_cost.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def calculate_payment_allocation_remaining(payment) -> Decimal:
        """
        Calculate remaining amount available for allocation from a payment
        """
        from finance.models import InvoicePayment

        allocated_amount = InvoicePayment.objects.filter(
            payment=payment
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

        remaining = payment.amount - allocated_amount
        return remaining.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

class ValidationService:
    """
    Service for centralized validation logic
    """

    @staticmethod
    def validate_workflow_transition(case, target_stage):
        """
        Validate if a case can transition to a target workflow stage
        """
        errors = []

        if not case.workflow_template:
            errors.append("Case has no workflow template assigned")
            return errors

        if target_stage.workflow != case.workflow_template:
            errors.append("Target stage does not belong to case workflow template")
            return errors

        # Check if all dependencies are completed
        if hasattr(target_stage, 'dependencies'):
            incomplete_deps = target_stage.dependencies.exclude(
                stagehistory__status='completed'
            )
            if incomplete_deps.exists():
                dep_names = [dep.name for dep in incomplete_deps]
                errors.append(f"Incomplete dependencies: {', '.join(dep_names)}")

        return errors

    @staticmethod
    def validate_payment_allocation(payment, invoice, amount):
        """
        Validate payment allocation to invoice
        """
        errors = []

        # Check if payment has enough remaining amount
        remaining = CalculationService.calculate_payment_allocation_remaining(payment)
        if amount > remaining:
            errors.append(f"Amount {amount} exceeds remaining payment amount {remaining}")

        # Check if invoice needs this much payment
        invoice_remaining = invoice.total_amount - (invoice.get_paid_amount() or Decimal('0.00'))
        if amount > invoice_remaining:
            errors.append(f"Amount {amount} exceeds invoice remaining amount {invoice_remaining}")

        # Check currency compatibility
        if payment.currency != invoice.currency:
            # Should have exchange rate available
            from items.models import ExchangeRate
            rate = ExchangeRate.get_exchange_rate(
                payment.currency.code,
                invoice.currency.code
            )
            if not rate:
                errors.append(f"No exchange rate available from {payment.currency.code} to {invoice.currency.code}")

        return errors
