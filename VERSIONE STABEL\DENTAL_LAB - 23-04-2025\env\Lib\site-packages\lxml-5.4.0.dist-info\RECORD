lxml-5.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
lxml-5.4.0.dist-info/METADATA,sha256=7h2aUFjljZnvI_PFfKrOOl6qnWit7M87J0NsL6n2ykA,3610
lxml-5.4.0.dist-info/RECORD,,
lxml-5.4.0.dist-info/WHEEL,sha256=JI6TipV6L5PIs92M_MEmWFBmpkHoCQOVHMpLZsFLd2o,101
lxml-5.4.0.dist-info/licenses/LICENSE.txt,sha256=tircdrXghpYNqK2q-tqeBOMqrzXhzLFM71dCQFqu0nw,1517
lxml-5.4.0.dist-info/licenses/LICENSES.txt,sha256=zlP1CNDLiL20Yh4jbKNIgaP6GAX5ffzPOJYBKKTi6tk,1543
lxml-5.4.0.dist-info/top_level.txt,sha256=NjD988wqaKq512nshNdLt-uDxsjkp4Bh51m6N-dhUrk,5
lxml/ElementInclude.py,sha256=pEvLKSyhNWtZTRr6liUJD-1mIqmbxbcurxcqZv7vNHg,8804
lxml/__init__.py,sha256=JHeOHWfGV1cGj-CFWqLgebl8oi8PAzpMmwijQj_omtI,596
lxml/__pycache__/ElementInclude.cpython-313.pyc,,
lxml/__pycache__/__init__.cpython-313.pyc,,
lxml/__pycache__/_elementpath.cpython-313.pyc,,
lxml/__pycache__/builder.cpython-313.pyc,,
lxml/__pycache__/cssselect.cpython-313.pyc,,
lxml/__pycache__/doctestcompare.cpython-313.pyc,,
lxml/__pycache__/pyclasslookup.cpython-313.pyc,,
lxml/__pycache__/sax.cpython-313.pyc,,
lxml/__pycache__/usedoctest.cpython-313.pyc,,
lxml/_elementpath.cp313-win_amd64.pyd,sha256=gKlQWfx1xU-SAO5mhqTMKPZ-ROouD8I4p8Mu11FHb9k,148480
lxml/_elementpath.py,sha256=waZwwmgKbOlNaztLRUOXWBnHnZdQwDWwv7M0GgHwJ44,11229
lxml/apihelpers.pxi,sha256=4S__cOXO4gq5tsr453GJPeRdbqI5B830SFYsxyUPok0,65403
lxml/builder.cp313-win_amd64.pyd,sha256=M1iSSwcWVS5j-Pq-okWZM5DQT5DXS-JiyFa1HMS9oLs,82432
lxml/builder.py,sha256=5P0LKYgwJws2fmKAZOr9gkqsBtY-lUC9LDF2W5B5Aik,8332
lxml/classlookup.pxi,sha256=rRtWHz9AajMqkm7RiG_PkOCsaQTRkgK757pRj3RvCxA,23015
lxml/cleanup.pxi,sha256=Zll_xQcmMBkUQtkWugiJ7TDQWPR0IHKjD5OesmQ0dB8,8669
lxml/cssselect.py,sha256=ur77NVsTk24-t_1AFo5EjfEqyheuw01BRG-_Ob5DDQY,3407
lxml/debug.pxi,sha256=HdUEKw8hU1Wzz4nO3hheqrauZlPATsv7N4QPQxCdth4,3372
lxml/docloader.pxi,sha256=tP6b_zZ5s0-Zj2kf2mFksI8U5whArwOVDOVuaZcbgtM,5950
lxml/doctestcompare.py,sha256=1r23O3Ki1BypqzkVWh97FEdRq-ENvmFEWuABOWdE0Lo,18219
lxml/dtd.pxi,sha256=hO4U0Ql3xzpqtV8XDZXkJncNEbb_ML8QkaYL8SBDTD0,15760
lxml/etree.cp313-win_amd64.pyd,sha256=3d6DBvEKU-2xAd63JTKw7SMuM91Z3XvxxvUVbiHMUeg,4108288
lxml/etree.h,sha256=rrJ2b-3YWpNtCL_j2Wd2VhXtcv9EbDT6Ju3ldIb8fyQ,9912
lxml/etree.pyx,sha256=PtHstVcD9V2S0EO8F5ZvJL8L_qAF0vCWvqzTsxrUftM,138249
lxml/etree_api.h,sha256=8qTtQmUdvNqy2grAE8N4BSn-gIMPpiyoe5C4Geo5KzU,17063
lxml/extensions.pxi,sha256=s3IkovN6q7I1lvAEs3rh7F-8TtTjTGXRSkptYV9012I,32921
lxml/html/ElementSoup.py,sha256=dQSpzuzUT3LKCGh71H4rccB5RCwoMpyayRY-qbtWBM0,330
lxml/html/__init__.py,sha256=FAipGsw63JJBS4YHDorF5OQTWotfgUYM1695bFw44Wo,66225
lxml/html/__pycache__/ElementSoup.cpython-313.pyc,,
lxml/html/__pycache__/__init__.cpython-313.pyc,,
lxml/html/__pycache__/_diffcommand.cpython-313.pyc,,
lxml/html/__pycache__/_html5builder.cpython-313.pyc,,
lxml/html/__pycache__/_setmixin.cpython-313.pyc,,
lxml/html/__pycache__/builder.cpython-313.pyc,,
lxml/html/__pycache__/clean.cpython-313.pyc,,
lxml/html/__pycache__/defs.cpython-313.pyc,,
lxml/html/__pycache__/diff.cpython-313.pyc,,
lxml/html/__pycache__/formfill.cpython-313.pyc,,
lxml/html/__pycache__/html5parser.cpython-313.pyc,,
lxml/html/__pycache__/soupparser.cpython-313.pyc,,
lxml/html/__pycache__/usedoctest.cpython-313.pyc,,
lxml/html/_diffcommand.py,sha256=MfccaYAAKCtzCRe_MCXihC3vnuPUKiJbmOx85Dt37To,2167
lxml/html/_html5builder.py,sha256=XfqNFDQ5HUOWTqubeOe1m5qmIut6I_3Egye75wer7tE,3330
lxml/html/_setmixin.py,sha256=6cUyIeiMIn5zUytcWHsdWZXyMJXVsfJVVQoAIIe9h7Q,1244
lxml/html/builder.py,sha256=X4-ZNqczoi9h035AN-4BmfSYDfsVyKpXXGsYPUFAh48,4625
lxml/html/clean.py,sha256=B_rsm3Mz7pn_Z8LnkXeSocL257--sqWJGaxBp2LlmB8,524
lxml/html/defs.py,sha256=w_8kGoMweUNZxTjQ9UlMeUo8wyTTvzjDG4ub24j8RRg,4371
lxml/html/diff.cp313-win_amd64.pyd,sha256=Ouy9ePYKO6u6h0aomxXjbP91rEd8oi7bedD-duEvH7g,275456
lxml/html/diff.py,sha256=Nygvg_a29fblqyj07CeCtl9_OV3AWPFTBjwqEOk8-PM,31271
lxml/html/formfill.py,sha256=8yXFIO4DNVY-HG8q4O4OtxIQ8qmLpXYQ8T1rFUTkK8c,9980
lxml/html/html5parser.py,sha256=iupCVDO_6I1PNWstkE6omdDh-rEc0KMaIXo1OCP9noM,8894
lxml/html/soupparser.py,sha256=fQ_ZjWcXwKTrbPD8lsPe9YNPsQmk2o9zCd1fJGFX0zY,10511
lxml/html/usedoctest.py,sha256=eP0SnLLZFfYHNIegfLWPeBqO-4yyKKjStjtUAUGJN6w,262
lxml/includes/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/__pycache__/__init__.cpython-313.pyc,,
lxml/includes/c14n.pxd,sha256=sO2g4HgndNGlhsSmMI767T8KfZcNtjpFilzZ3OD4eqU,1135
lxml/includes/config.pxd,sha256=ECKwhEax5IW2KFn1BQdwR2M0FbWciVRFMJW7pHxJSqI,99
lxml/includes/dtdvalid.pxd,sha256=VOt94bqe417bxvfkfbtbsd5kVnwQj4oYSHMfnV2pcsM,707
lxml/includes/etree_defs.h,sha256=XXz1CliDqEBPvxGNtnmYEwE6D1Xtdib1sMk9JvvkHDM,14624
lxml/includes/etreepublic.pxd,sha256=s2HRJSuZhwlDi3oHsSBPChA4dgTnWjWWMzBb3VxrtJw,10421
lxml/includes/extlibs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/extlibs/__pycache__/__init__.cpython-313.pyc,,
lxml/includes/extlibs/zconf.h,sha256=mzNGi-0o_nxNbownE4bGAiU0E1zkJB-O3SezPUEprCI,17043
lxml/includes/extlibs/zlib.h,sha256=iO_DwdWubbhw14FW91xs9ITbicLsGUbUwnFpHXrxcZU,98767
lxml/includes/htmlparser.pxd,sha256=UEV2wIp8RHekwUAcPWMQe8KBfEv39u0c3LHOsdvRr0k,2858
lxml/includes/libexslt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/libexslt/__pycache__/__init__.cpython-313.pyc,,
lxml/includes/libexslt/exslt.h,sha256=KxVX0CeCh0MpfTcGSr3f58MiJjx3GsJIC5Mvzrxn5Rg,3222
lxml/includes/libexslt/exsltconfig.h,sha256=ErNzH5xEZ2GFluiT7m3sdqlL-MFZPpjvXC7KLRjSGpQ,1242
lxml/includes/libexslt/exsltexports.h,sha256=_lddLoUjOXa172aAYSFfQXkp8xv8Q2WZxkKAT64Ev7g,1140
lxml/includes/libexslt/libexslt.h,sha256=h5Y5ny1LzW297MMCfRX-wnfQG0fTXqkTCp255GQNPj0,679
lxml/includes/libxml/HTMLparser.h,sha256=5OM28DqXmI6L3mmSx6k4khVuKZrm-EbXmhY0f4XW-mA,9809
lxml/includes/libxml/HTMLtree.h,sha256=LftyEurOqreJZt4B5GK2pqvY_bY2ub4TUjCfcchPFaw,3649
lxml/includes/libxml/SAX.h,sha256=mr-5to3NiHgQAlE4G4W4N_ht4I_nTvtAYTGpRMu_77M,4661
lxml/includes/libxml/SAX2.h,sha256=HZpRkyyFRHq5ZLDlPwNB4MGWUfWwkTE_Tl1oT8e6QPc,4642
lxml/includes/libxml/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/libxml/__pycache__/__init__.cpython-313.pyc,,
lxml/includes/libxml/c14n.h,sha256=s1cBw7nxLGlHr6yzCu_PLJTYvRfoklqCGLVb417GoiI,3213
lxml/includes/libxml/catalog.h,sha256=se6ihXDRMywC8crnX-JJ0vzgSSx3QbbBAbtZQbJRF1E,4800
lxml/includes/libxml/chvalid.h,sha256=EoJ333bExUTlA_uGQOe8qOnJyaLbcnQRHrkMf6fjKLg,5317
lxml/includes/libxml/debugXML.h,sha256=s5raCA9NLszcprZsButf--5JyTSoCFfX692IRsQ6uLU,5145
lxml/includes/libxml/dict.h,sha256=PhOIy6vHQ-xnKVAjq2isoFRqI889TCK1rY2QqA_d368,1821
lxml/includes/libxml/encoding.h,sha256=D0Bqw8d4Mgdp4lXaLWR9En0ddf8mXpyJ4GoMZw0m0f8,8142
lxml/includes/libxml/entities.h,sha256=h0Omy2DQ7bEEH4NYrW1i9hSpnzp44wNgafkk84ZYt_c,4701
lxml/includes/libxml/globals.h,sha256=r-QvCDlV-JS2aAyg-fXpXgTojOFHUq5JXxK55myXDdQ,14931
lxml/includes/libxml/hash.h,sha256=ocPVGr9GVFZiTiFhyfpXYWMtB1SGhisZIrkzL9LiJz0,6637
lxml/includes/libxml/list.h,sha256=67XDm5W0TNjyE9OnXd6Is2-Yy0_449Z6RpB9wnQV7IU,3277
lxml/includes/libxml/nanoftp.h,sha256=1jalOwOgke6kV8aeZPc9WxZnGwvKVXQ8eZZ-ZwDrzMI,4150
lxml/includes/libxml/nanohttp.h,sha256=wu425iErZ1xR3HbQyMZR2Eg9kpO8MFEk_Cx07AS0FlE,1950
lxml/includes/libxml/parser.h,sha256=p49DU9gzUrmNie1yZOPANhCsQEhtHnYTLg5NbBIMS6s,40871
lxml/includes/libxml/parserInternals.h,sha256=6NQSnQDVzr1XznNRGoplwER_HtTuKK_C2Ro44v6D8AE,17241
lxml/includes/libxml/pattern.h,sha256=jIudTB3owWWdT-7qrb438-wtubZSK42SvJdHTn5JWFY,2566
lxml/includes/libxml/relaxng.h,sha256=kaua77JEbe5ME4J050VBjCIzHn9zWRGc8sF-M6bhjIc,6019
lxml/includes/libxml/schemasInternals.h,sha256=lqk2rUS2tsBd7tomThyyKXQIgS7_PXV44VpeFGe4bZg,27166
lxml/includes/libxml/schematron.h,sha256=HvC46w3UA2RDqWsRyMOIOOqwVvxDPtXldh-QZMr2CpY,4368
lxml/includes/libxml/threads.h,sha256=zy4K-2uwaGVLoZIl1uGMnRZpW5JMm9wXSizhyribwhc,1969
lxml/includes/libxml/tree.h,sha256=fre4cjgWR17stTPeNcUU3zlgmQezlHxnVgNwhGADCo0,38200
lxml/includes/libxml/uri.h,sha256=O0k1yros9uPC6isZ-Txwz5MAStOcad78ry_26ZmRvB0,2638
lxml/includes/libxml/valid.h,sha256=l0sOgDDIexRXc5mAnYl6ft2eEMSih23uo0Mmd05tHhM,13229
lxml/includes/libxml/xinclude.h,sha256=1u5xjjL67l_d_AXjGmuGCz1ZdP9oHesGmYx8OO-cJQs,3016
lxml/includes/libxml/xlink.h,sha256=TC-rXTWm0KF6G8Ey9BixzcekaYHpyEcKaXwdKT9aK_I,5191
lxml/includes/libxml/xmlIO.h,sha256=bztkE3NwGsYXhRjIZIkaA1ePtz0HbpwJwyTxVlTpmNg,10572
lxml/includes/libxml/xmlautomata.h,sha256=urpN6NMU6e7uLth7qZV-5WxT9MuCE6jwhQni7p8OEVY,3933
lxml/includes/libxml/xmlerror.h,sha256=qk2j-7CIkGMVrXJif3EADFb9TmtCRq_8NXAEDb2QKrk,37020
lxml/includes/libxml/xmlexports.h,sha256=GL37xMLc2uhXfvkWWrt5uIvSkxRvmr9XpHRvkWynp3s,1042
lxml/includes/libxml/xmlmemory.h,sha256=gKMVgPLk16j3sEbYO__tjJ-Kh0opn5XYtqigcG4RZ4k,6053
lxml/includes/libxml/xmlmodule.h,sha256=I5cuHClvL8Iz3iBxAX8e84yagpGGCDuSYoUQqZ2WqIU,1195
lxml/includes/libxml/xmlreader.h,sha256=XKS9vP0ydJIZeI7KO6xQTgjmG_xgYDayC5pMP9nmFKA,12339
lxml/includes/libxml/xmlregexp.h,sha256=hcM2ksyoT0iPqUZZMUMRZoRS1WuxtPsi68r1dBgadqY,5440
lxml/includes/libxml/xmlsave.h,sha256=hmnMQ3JqAM58Jz-sOSDS4FAT5CkfaQ8vJnRxWwZC-PQ,2345
lxml/includes/libxml/xmlschemas.h,sha256=bjBJmjocL7FFTuk492AIirX0jfQKHThHl6wMuk7E-oI,7071
lxml/includes/libxml/xmlschemastypes.h,sha256=Pj2xcKIJG7KOCJGGcYaJ_7IzJidT6tPeO0NCKksHCOk,4735
lxml/includes/libxml/xmlstring.h,sha256=1LTc15iy5slT-vh83YV8qoeXauXdzY8C_UqDb5TQ_7k,5411
lxml/includes/libxml/xmlunicode.h,sha256=pthMXVs3mfsWggKJDAJW9vDool6vakoEpLtCm65PQSk,8867
lxml/includes/libxml/xmlversion.h,sha256=DYJmx3Le2xLkPj9dRgf6FAU-L089JvinKi0s3zN59mQ,9833
lxml/includes/libxml/xmlwriter.h,sha256=6hMXHg7kXzFxQNWacviidOZT4CfhUXjPiVHkkvw5QYg,21113
lxml/includes/libxml/xpath.h,sha256=pIgW7Kl2J-0PWSbqCOB0nPRmMsCez2FxBgDptyaz-H0,16994
lxml/includes/libxml/xpathInternals.h,sha256=1Yd4MvL4U3wPwueqJisdZShzxUyyuF62OBazVQM09Ac,19049
lxml/includes/libxml/xpointer.h,sha256=Okpv5iwcg3oyD5pHiYitYPVuPn9372L4WbimO2-YL-0,3753
lxml/includes/libxslt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lxml/includes/libxslt/__pycache__/__init__.cpython-313.pyc,,
lxml/includes/libxslt/attributes.h,sha256=CyXb4ZVnJVw7_0MHcHXPcNRkLAVDTTSu6zoLKZQDWmY,996
lxml/includes/libxslt/documents.h,sha256=6GUS9iIUS1p_5veDfF1Umx1xsHxNhTu3KnEVgBnNdqU,2797
lxml/includes/libxslt/extensions.h,sha256=iJxPhwKNqRfiIn3i3L8tKifC8uIBPn11t46q-j6XzBo,7161
lxml/includes/libxslt/extra.h,sha256=iOuhGjFLi0scm2ebgA07ER-FH1_6HmPeHfSghmXu5Xw,1712
lxml/includes/libxslt/functions.h,sha256=JK5nzaB8OrCCQlxBct6MMQp8K8c_Gd614Oq82fcGvaU,2050
lxml/includes/libxslt/imports.h,sha256=udGr3UW9dg3eEiddSDVWOUCvwljGyfM57RH2QgvZw1s,1915
lxml/includes/libxslt/keys.h,sha256=y3dY_lZ3kfYUB6Xw83ORnDoS68eJp0oGUEfDthYWtMI,1208
lxml/includes/libxslt/libxslt.h,sha256=QqaLpZIb4TcrGX0XIvKJQo04WjNZfh1Pe1lkw0biQEY,872
lxml/includes/libxslt/namespaces.h,sha256=k78ED1fUDe4G35uVhro5aqJ73aTfQ-xn45z-QkjZoWk,1734
lxml/includes/libxslt/numbersInternals.h,sha256=28Z_BOHCnJET76THaXsWkt_dR8K__YOIgdYvc4Q8_hA,2092
lxml/includes/libxslt/preproc.h,sha256=xhnTXIXM6CNIP4SsXJq9zrqqYOY4rnrDUny4jWvVt6g,939
lxml/includes/libxslt/security.h,sha256=1aSHQYnDTqlYB73RAobqfcLWsmCCRb52F_yHmyNcDZ0,2756
lxml/includes/libxslt/templates.h,sha256=CMZbbisqWm9ymU-HRp2hja0M2qwxiAgVjwBjZSF4BCc,2345
lxml/includes/libxslt/transform.h,sha256=DiUJicoml0QxgdbXCerCnKQuyben6PiCy6VG9nQrhso,6518
lxml/includes/libxslt/trio.h,sha256=kn-98NsJU9_hQ8I8l2-n8J5LmWH_0K0klXHR3hKtc0o,7415
lxml/includes/libxslt/triodef.h,sha256=XgbUXOc4472RCcmHqh-NMgMMLcykrubuI-9rNEkf3Jo,6912
lxml/includes/libxslt/variables.h,sha256=7cL1EOpzphzZJD39Qn9enHZzakeyEw7Hfz9n1IeQ0cY,3261
lxml/includes/libxslt/win32config.h,sha256=mJY4lc-u7nLtqHeIHFnJwpPbiNjkN7GZobaZjDmdCx0,1164
lxml/includes/libxslt/xslt.h,sha256=j6nZHxyY3U505GbtO_aeLMrJ9xE2uux45XOlyT0oeYY,2074
lxml/includes/libxslt/xsltInternals.h,sha256=RtgEYBm8nLxBrLpV0AEb7j2xKIV9SzaWeL28uPhY3zI,59886
lxml/includes/libxslt/xsltconfig.h,sha256=xIsNA5sg1jT91U7MDQFvMSYhpY5LenQ9lMbQoecw1vA,3819
lxml/includes/libxslt/xsltexports.h,sha256=GdcJL2oHi344GornuG2iAT27yqI48L9eim0ScAj9ywg,1188
lxml/includes/libxslt/xsltlocale.h,sha256=uAGe_73hvCZP5mDvHCHgcMvbdJ7FbHHSzCDInfuDub8,986
lxml/includes/libxslt/xsltutils.h,sha256=KD208ptsQHrHB1Du4biZU2iZM0Yb6M7c2SPJtjo46JE,9428
lxml/includes/lxml-version.h,sha256=02k56LxsAoykd7rhg6Tjk_AHTCzBXw0mCyC8EA8GTk8,74
lxml/includes/relaxng.pxd,sha256=H5W5XObI5YtRpULeiRy6K6ldUZC0tDQauAFZ7PJSqE4,2679
lxml/includes/schematron.pxd,sha256=JmKDG9ElNPXJ7rZzOpQjPaIdzIZ1pzMEOT_GjZRK98U,1638
lxml/includes/tree.pxd,sha256=Uo6hPybaSgh6P9YdQfpr0mftCrSyYZ_w_Z0FyCtOr1c,20885
lxml/includes/uri.pxd,sha256=UbTG4fvHaq6jkNkwLyx6aMbbCCXGDmrE3KjPirCtW30,150
lxml/includes/xinclude.pxd,sha256=n3SdzxwdcwJxeX8TQIRZ_6ozABwg6mrMEOXDFzs5f50,826
lxml/includes/xmlerror.pxd,sha256=cVZruvjdntAoE8jy2XFxSG1l6kvsY8u9BK2MkE8GFLs,58868
lxml/includes/xmlparser.pxd,sha256=1TlqfcESIvW9vTo9UYi6Ddvr8LY7mEBWxTNxuMYvuy8,11699
lxml/includes/xmlschema.pxd,sha256=q6cHPJpi3uhkf2MLUUd2h3nCDB_4a1B8cei8aTHbN84,1737
lxml/includes/xpath.pxd,sha256=LsDoWjO5xJUB-z0_BdsItEWHhLgLpX40IdNmbYzpdv4,5739
lxml/includes/xslt.pxd,sha256=GM_Hkk7vGnWMlWlLM9YCpkUeGgWk8Lvmr1KUy5kbZ-4,8431
lxml/isoschematron/__init__.py,sha256=Ci8JiaKxfvu-hMRh7IP27TFhM03iEQ8CJoNsdXjLoM8,13622
lxml/isoschematron/__pycache__/__init__.cpython-313.pyc,,
lxml/isoschematron/resources/rng/iso-schematron.rng,sha256=cCM4qpfuTYAVtukjxlobPMlJ8nZ-au7DgONS2g4rYO4,19046
lxml/isoschematron/resources/xsl/RNG2Schtrn.xsl,sha256=gy-E6xR2p35SK9wXFThraPJEI9NdoITNHd4xmqy7oxY,3247
lxml/isoschematron/resources/xsl/XSD2Schtrn.xsl,sha256=QweRrIIM-zFcgg98GXA2CaWfIbgVE0XKEeYSfvv67A0,4563
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_abstract_expand.xsl,sha256=SSUZkAkWhFYG_YNHvW9UU6X8tmMOH1vdpRmzqu0l4QM,12015
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_dsdl_include.xsl,sha256=ZZ-d72rxQlACfrs4EmaDYxaRbxd0gPa-DPLRlVV6QRg,41116
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_message.xsl,sha256=NQal4z3EuZ_5QVoMFbJqma2SKg4-hzm_Db283-0l0kU,2069
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_schematron_skeleton_for_xslt1.xsl,sha256=Vo1F576odRkdLBmuEEWrLa5LF3SMTX9B6EEGnxUbv_8,73560
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/iso_svrl_for_xslt1.xsl,sha256=UI7xHx0leNXS9BaU6cyQ4CHeflufKmAE-zjOrihuH-E,20970
lxml/isoschematron/resources/xsl/iso-schematron-xslt1/readme.txt,sha256=OGLiFswuLJEW5EPYKOeoauuCJFEtVa6jyzBE1OcJI98,3310
lxml/iterparse.pxi,sha256=Nd26kE2NVAg2_eyhlBVleWH69rdXK6dZkR4AgpkDsys,16959
lxml/lxml.etree.h,sha256=r4YsdJ2NXeFAvntYcLJnam5a3A33G_PaPMD6kzBKyXE,10160
lxml/lxml.etree_api.h,sha256=uPFmASmuydIVaFANd1WhYAsiLPRamC4gxuRlV7u4-g0,17263
lxml/nsclasses.pxi,sha256=9TiZjPbP73H8VN0Xu1RmuRB7G-D0pTTSqLPbdTJQt5U,9410
lxml/objectify.cp313-win_amd64.pyd,sha256=AozdfvlLmnhWCou2thvRY6LUR7ciyqJCI-QH9-fNRgY,1756672
lxml/objectify.pyx,sha256=IngqdSCtxUav_NklHutFR92AuuNfr9nYchEY3Zprchc,77880
lxml/objectpath.pxi,sha256=og7LX-a88RrgJI3lUAtuJ8Hvx4RpNf57k7pmafzCuCA,11782
lxml/parser.pxi,sha256=ZvW9H2aa96jnMDG0_lsbkwijRnOYqVUfiveji_EF7KM,84040
lxml/parsertarget.pxi,sha256=0gZ5eLJ2hQ8tSApFEQx1PfzaiPqRuaRxD_GwKODIzEc,6506
lxml/proxy.pxi,sha256=phoeHZwmrN6LF3W57V5J-72ZPhGxdpI0TxgzVlD5pyU,24316
lxml/public-api.pxi,sha256=NT9BUTPhf0ZxLKm61xKT4SVmWxbb5aImmNIeTJdXNRs,6844
lxml/pyclasslookup.py,sha256=jDGYrbxuKE3Hl07PbILX_ptLkQaXYrpJSgvil4hIs3s,95
lxml/readonlytree.pxi,sha256=W3RXFM2aUGM8ZswTBP5EUW-wT1n6x7fpEGloQQa5cPE,19541
lxml/relaxng.pxi,sha256=rjTfl-rBYlHqQv4nfxVK8KZR59KqKWQp9lZv3aKYXVk,6504
lxml/sax.cp313-win_amd64.pyd,sha256=gGL3GM49JUMX3olCjUrk7aYso-iNU4N0Pxi-5E_u3eo,128000
lxml/sax.py,sha256=8YlAeeVyPL-ksvIDkOZ7HlXSvwByoiMaIujzN4nTjQw,9578
lxml/saxparser.pxi,sha256=HWkubajdSI7_SwKO1vu0uOHTljXU6NZhhBfEdEd06R8,34197
lxml/schematron.pxi,sha256=STQpiSEjL81MVePjobPhhWYJEH2KucWW519NFiWpSG8,6076
lxml/serializer.pxi,sha256=YbLINzPWp3KeshHc0R5R-FoHBLT2Eu7zqF6byr0HOwE,67534
lxml/usedoctest.py,sha256=cn3rXeL3F-ISj-HXNL9g4EwciH60b9NcZjXIRAF31iM,243
lxml/xinclude.pxi,sha256=DTre_KYISt1rzbf_qS75ix-ZbruSBnefIfNs4AqvF-M,2523
lxml/xmlerror.pxi,sha256=VFEO6Tn2RMVjKF9SXE15piak23lMaNrvE2pqT0hxfFw,51508
lxml/xmlid.pxi,sha256=Ng7Wifv0XXknjPMO6kldgrOHfCsII46UBPCkUTk96Qk,6252
lxml/xmlschema.pxi,sha256=sJumEEbkuR-vj2vDAE8vDvbjBhgzt_LRX-IwQRFgLTU,8705
lxml/xpath.pxi,sha256=iT1S7CBo2dBn19y926ktwPgAKGRC5WFnRgrncA6GwpM,19619
lxml/xslt.pxi,sha256=WtlZ2izr9g6-lrejYZV0tQKDqN-dQVBzAE4YEhls-Ac,36973
lxml/xsltext.pxi,sha256=syKkmI6GMwE_f-XPYC8rGFXJ5F9KeQigxz52CsbF4PA,11330
