#!/usr/bin/env python
"""
Test script for enhanced security and permission system
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

from django.utils import timezone
from django.contrib.auth.models import Group
from common.permissions import PermissionService
from common.security import SecurityService, PasswordPolicyValidator
from common.audit import FinancialAuditService
from accounts.models import CustomUser

class SecuritySystemTest:
    def __init__(self):
        self.test_results = []
        self.errors = []
    
    def log_test(self, test_name, success, message=""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = f"{status}: {test_name}"
        if message:
            result += f" - {message}"
        
        self.test_results.append(result)
        print(result)
        
        if not success:
            self.errors.append(f"{test_name}: {message}")
    
    def test_permission_groups(self):
        """Test permission group creation and management"""
        print("\n=== Testing Permission Groups ===")
        
        # Test 1: Check if all groups were created
        expected_groups = ['super_admin', 'lab_manager', 'department_manager', 
                          'technician', 'accountant', 'dentist', 'receptionist']
        
        existing_groups = [g.name for g in Group.objects.filter(name__in=expected_groups)]
        success = len(existing_groups) == len(expected_groups)
        self.log_test("Permission groups creation", success, 
                     f"Created {len(existing_groups)}/{len(expected_groups)} groups")
        
        # Test 2: Check group permissions
        super_admin_group = Group.objects.filter(name='super_admin').first()
        if super_admin_group:
            permission_count = super_admin_group.permissions.count()
            success = permission_count > 0
            self.log_test("Super admin permissions", success, 
                         f"Super admin has {permission_count} permissions")
        else:
            self.log_test("Super admin permissions", False, "Super admin group not found")
        
        # Test 3: Role assignment
        test_user = CustomUser.objects.first()
        if test_user:
            success = PermissionService.assign_user_to_role(test_user, 'technician')
            self.log_test("Role assignment", success, 
                         f"Assigned technician role to {test_user.email}")
            
            # Verify assignment
            user_groups = [g.name for g in test_user.groups.all()]
            success = 'technician' in user_groups
            self.log_test("Role verification", success, 
                         f"User groups: {user_groups}")
        else:
            self.log_test("Role assignment", False, "No test user found")
    
    def test_permission_checking(self):
        """Test permission checking functionality"""
        print("\n=== Testing Permission Checking ===")
        
        # Test 1: Superuser permissions
        superuser = CustomUser.objects.filter(is_superuser=True).first()
        if superuser:
            has_permission = PermissionService.user_has_permission(superuser, 'view_case')
            success = has_permission  # Superuser should have all permissions
            self.log_test("Superuser permissions", success, 
                         f"Superuser has view_case permission: {has_permission}")
        else:
            self.log_test("Superuser permissions", False, "No superuser found")
        
        # Test 2: Regular user permissions
        regular_user = CustomUser.objects.filter(is_superuser=False).first()
        if regular_user:
            # Assign a role first
            PermissionService.assign_user_to_role(regular_user, 'accountant')
            
            # Test financial permission (should have)
            has_financial = PermissionService.user_has_permission(regular_user, 'view_payment')
            
            # Test case management permission (should not have for accountant)
            has_case_mgmt = PermissionService.user_has_permission(regular_user, 'delete_case')
            
            self.log_test("Accountant financial permissions", has_financial, 
                         f"Accountant has view_payment: {has_financial}")
            self.log_test("Accountant restricted permissions", not has_case_mgmt, 
                         f"Accountant has delete_case: {has_case_mgmt}")
        else:
            self.log_test("Regular user permissions", False, "No regular user found")
    
    def test_security_service(self):
        """Test security service functionality"""
        print("\n=== Testing Security Service ===")
        
        # Test 1: Login attempt tracking
        test_identifier = "<EMAIL>"
        
        # Record failed attempt
        result = SecurityService.record_login_attempt(test_identifier, False)
        success = result['attempts'] == 1 and not result['locked']
        self.log_test("Failed login tracking", success, 
                     f"Attempts: {result['attempts']}, Locked: {result['locked']}")
        
        # Test 2: Account lockout
        # Simulate multiple failed attempts
        for i in range(SecurityService.MAX_LOGIN_ATTEMPTS):
            result = SecurityService.record_login_attempt(test_identifier, False)
        
        success = result['locked']
        self.log_test("Account lockout", success, 
                     f"Account locked after {result['attempts']} attempts")
        
        # Test 3: Successful login clears attempts
        SecurityService.record_login_attempt(test_identifier, True)
        is_locked = SecurityService.is_account_locked(test_identifier)
        success = not is_locked
        self.log_test("Login attempt reset", success, 
                     f"Account locked after successful login: {is_locked}")
    
    def test_password_policy(self):
        """Test password policy validation"""
        print("\n=== Testing Password Policy ===")
        
        # Test 1: Weak password
        weak_password = "123"
        result = PasswordPolicyValidator.validate_password(weak_password)
        success = not result['valid'] and len(result['errors']) > 0
        self.log_test("Weak password rejection", success, 
                     f"Errors: {len(result['errors'])}, Strength: {result['strength']}")
        
        # Test 2: Strong password
        strong_password = "StrongP@ssw0rd123!"
        result = PasswordPolicyValidator.validate_password(strong_password)
        success = result['valid'] and result['strength'] == 'strong'
        self.log_test("Strong password acceptance", success, 
                     f"Valid: {result['valid']}, Strength: {result['strength']}")
        
        # Test 3: Medium password
        medium_password = "Password123"
        result = PasswordPolicyValidator.validate_password(medium_password)
        success = result['strength'] in ['medium', 'strong']
        self.log_test("Medium password classification", success, 
                     f"Strength: {result['strength']}")
    
    def test_audit_logging(self):
        """Test audit logging functionality"""
        print("\n=== Testing Audit Logging ===")
        
        # Test 1: Security event logging
        test_user = CustomUser.objects.first()
        if test_user:
            try:
                FinancialAuditService.log_security_event(
                    'test_event', 
                    user=test_user,
                    test_data='security_test'
                )
                success = True
                message = "Security event logged successfully"
            except Exception as e:
                success = False
                message = f"Error: {e}"
            
            self.log_test("Security event logging", success, message)
        else:
            self.log_test("Security event logging", False, "No test user found")
        
        # Test 2: Permission denied logging
        if test_user:
            try:
                FinancialAuditService.log_permission_denied(
                    test_user, 
                    'test_resource', 
                    'test_action'
                )
                success = True
                message = "Permission denied event logged"
            except Exception as e:
                success = False
                message = f"Error: {e}"
            
            self.log_test("Permission denied logging", success, message)
        
        # Test 3: Audit trail retrieval
        try:
            logs = FinancialAuditService.get_audit_trail(
                operation_type='security_event',
                limit=5
            )
            success = len(logs) >= 0  # Should at least not error
            message = f"Retrieved {len(logs)} audit logs"
        except Exception as e:
            success = False
            message = f"Error: {e}"
        
        self.log_test("Audit trail retrieval", success, message)
    
    def test_user_activity_tracking(self):
        """Test user activity tracking"""
        print("\n=== Testing User Activity Tracking ===")
        
        test_user = CustomUser.objects.first()
        if test_user:
            # Test 1: Data access logging
            try:
                FinancialAuditService.log_data_access(
                    test_user, 
                    'TestModel', 
                    1, 
                    'view'
                )
                success = True
                message = "Data access logged"
            except Exception as e:
                success = False
                message = f"Error: {e}"
            
            self.log_test("Data access logging", success, message)
            
            # Test 2: Data modification logging
            try:
                FinancialAuditService.log_data_modification(
                    test_user,
                    'TestModel',
                    1,
                    'update',
                    old_values={'field': 'old_value'},
                    new_values={'field': 'new_value'}
                )
                success = True
                message = "Data modification logged"
            except Exception as e:
                success = False
                message = f"Error: {e}"
            
            self.log_test("Data modification logging", success, message)
            
            # Test 3: User activity summary
            try:
                summary = FinancialAuditService.get_user_activity_summary(test_user, 7)
                success = 'total_actions' in summary
                message = f"Total actions: {summary.get('total_actions', 0)}"
            except Exception as e:
                success = False
                message = f"Error: {e}"
            
            self.log_test("User activity summary", success, message)
        else:
            self.log_test("User activity tracking", False, "No test user found")
    
    def run_all_tests(self):
        """Run all security system tests"""
        print("Starting Security System Test Suite...")
        print("=" * 60)
        
        self.test_permission_groups()
        self.test_permission_checking()
        self.test_security_service()
        self.test_password_policy()
        self.test_audit_logging()
        self.test_user_activity_tracking()
        
        # Summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if "✅ PASS" in r])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        if self.errors:
            print("\nFAILED TESTS:")
            for error in self.errors:
                print(f"❌ {error}")
        
        if failed_tests == 0:
            print("\n🎉 All tests passed! Security system is working correctly.")
            return True
        else:
            print(f"\n⚠️  {failed_tests} test(s) failed. Please review the issues above.")
            return False

if __name__ == '__main__':
    tester = SecuritySystemTest()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)
