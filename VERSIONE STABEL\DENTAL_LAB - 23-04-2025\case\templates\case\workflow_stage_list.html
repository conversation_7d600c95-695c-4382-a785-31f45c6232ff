{% extends 'base.html' %}
{% load i18n %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
<style>
    :root {
        --priority-low: #6c757d;
        --priority-medium: #0d6efd;
        --priority-high: #ffc107;
        --priority-urgent: #dc3545;

        --status-pending: #6c757d;
        --status-in-progress: #0d6efd;
        --status-paused: #6610f2;
        --status-completed: #198754;
        --status-delayed: #dc3545;
        --status-blocked: #dc3545;
        --status-cancelled: #6c757d;
        --status-review: #0dcaf0;

        --border-radius-sm: 0.25rem;
        --border-radius: 0.375rem;
        --border-radius-lg: 0.5rem;
        --border-radius-xl: 1rem;

        --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

        --primary-color: #0d6efd;
        --primary-light: rgba(13, 110, 253, 0.1);
        --secondary-color: #6c757d;
        --success-color: #198754;
        --info-color: #0dcaf0;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --light-color: #f8f9fa;
        --dark-color: #212529;
    }

    /* Page Layout */
    .page-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 1.5rem;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding: 1.5rem;
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
    }

    .page-title {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .page-title i {
        font-size: 1.5rem;
        color: var(--primary-color);
        background-color: var(--primary-light);
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    /* Filter Card */
    .filter-card {
        background-color: #fff;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        margin-bottom: 1.5rem;
        border: none;
        overflow: hidden;
    }

    .filter-card .card-body {
        padding: 1.25rem;
    }

    .filter-form .form-control,
    .filter-form .form-select {
        border-radius: var(--border-radius-sm);
        font-size: 0.875rem;
    }

    .filter-form .btn {
        border-radius: var(--border-radius-sm);
        font-size: 0.875rem;
    }

    /* Workflow Card */
    .workflow-card {
        background-color: #fff;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        margin-bottom: 1.5rem;
        border: none;
        overflow: hidden;
    }

    .workflow-header {
        padding: 1rem 1.5rem;
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .workflow-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
    }

    .workflow-body {
        padding: 0;
    }

    /* Workflow Table */
    .workflow-table {
        border-collapse: separate;
        border-spacing: 0;
        margin-bottom: 0;
    }

    .workflow-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
        padding: 1rem;
        border-bottom: 2px solid #e9ecef;
    }

    .workflow-table tbody tr {
        transition: all 0.2s ease;
        border-left: 3px solid transparent;
    }

    .workflow-table tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    .workflow-table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef;
    }

    .workflow-table tbody tr.critical-stage {
        border-left-color: var(--danger-color) !important;
    }

    /* Stage Order Badge */
    .stage-order {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: rgba(13, 110, 253, 0.1);
        color: var(--primary-color);
        font-weight: 600;
        font-size: 0.875rem;
    }

    /* Department Badge */
    .department-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.35em 0.65em;
        font-size: 0.75rem;
        font-weight: 600;
        border-radius: 50rem;
        background-color: rgba(108, 117, 125, 0.1);
        color: var(--secondary-color);
    }

    /* Duration Badge */
    .duration-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.35em 0.65em;
        font-size: 0.75rem;
        font-weight: 600;
        border-radius: 50rem;
        background-color: rgba(13, 202, 240, 0.1);
        color: var(--info-color);
    }

    .duration-badge i {
        margin-right: 0.25rem;
    }

    /* Stage Name */
    .stage-name {
        font-weight: 600;
        color: var(--dark-color);
        font-size: 0.95rem;
        margin-bottom: 0.25rem;
    }

    .stage-description {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 0;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .action-buttons .btn {
        padding: 0.375rem 0.75rem;
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        border-radius: var(--border-radius);
        transition: all 0.2s ease;
    }

    .action-buttons .btn i {
        font-size: 0.875rem;
    }

    .action-buttons .btn-view {
        background-color: var(--info-color);
        border-color: var(--info-color);
        color: white;
    }

    .action-buttons .btn-edit {
        background-color: var(--warning-color);
        border-color: var(--warning-color);
        color: white;
    }

    .action-buttons .btn-delete {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
        color: white;
    }

    /* Empty State */
    .empty-state {
        padding: 3rem 2rem;
        text-align: center;
        background-color: var(--light-color);
        border-radius: var(--border-radius);
    }

    .empty-state i {
        font-size: 3rem;
        color: #adb5bd;
        margin-bottom: 1rem;
    }

    .empty-state h4 {
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .empty-state p {
        max-width: 500px;
        margin: 0 auto 1.5rem;
        color: #6c757d;
    }

    /* Critical Stage Indicator */
    .critical-indicator {
        display: inline-flex;
        align-items: center;
        padding: 0.25em 0.5em;
        font-size: 0.75rem;
        font-weight: 600;
        border-radius: 50rem;
        background-color: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);
        margin-left: 0.5rem;
    }

    /* Responsive Adjustments */
    @media (max-width: 992px) {
        .page-container {
            padding: 1rem;
        }

        .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .workflow-table th,
        .workflow-table td {
            padding: 0.75rem 1rem;
        }
    }

    @media (max-width: 768px) {
        .workflow-table {
            min-width: 900px;
        }

        .workflow-body {
            overflow-x: auto;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <!-- Page Header -->
    <div class="page-header animate__animated animate__fadeIn">
        <div>
            <h1 class="page-title">
                <i class="bi bi-diagram-3"></i> {% trans "Workflow Stages" %}
            </h1>
            <p class="text-muted mb-0">{% trans "Manage and organize workflow stages for your dental lab processes" %}</p>
        </div>
        <div>
            <a href="{% url 'case:workflow_stage_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-lg"></i> {% trans "Create New Stage" %}
            </a>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filter-card animate__animated animate__fadeIn" style="animation-delay: 0.5s">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-funnel me-2"></i>{% trans "Filter Stages" %}
            </h5>
            <button class="btn btn-sm btn-link text-muted" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="true" aria-controls="filterCollapse">
                <i class="bi bi-chevron-up"></i>
            </button>
        </div>
        <div class="collapse show" id="filterCollapse">
            <div class="card-body">
                <form method="get" class="row g-3 filter-form">
                    <div class="col-md-4">
                        <label for="search" class="form-label">{% trans "Search" %}</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" id="search" name="search" placeholder="{% trans 'Search by name or department' %}" value="{{ search_query }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="department" class="form-label">{% trans "Department" %}</label>
                        <select class="form-select" id="department" name="department">
                            <option value="">{% trans "All Departments" %}</option>
                            {% for dept in departments %}
                                <option value="{{ dept.id }}" {% if department_filter == dept.id|stringformat:"i" %}selected{% endif %}>
                                    {{ dept.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="critical" class="form-label">{% trans "Critical Stage" %}</label>
                        <select class="form-select" id="critical" name="critical">
                            <option value="">{% trans "All Stages" %}</option>
                            <option value="true" {% if critical_filter == 'true' %}selected{% endif %}>{% trans "Critical Only" %}</option>
                            <option value="false" {% if critical_filter == 'false' %}selected{% endif %}>{% trans "Non-Critical Only" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-filter me-1"></i>{% trans "Apply Filters" %}
                            </button>
                            <a href="{% url 'case:workflow_stage_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>{% trans "Clear" %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Workflow Stages -->
    <div class="workflow-card animate__animated animate__fadeIn" style="animation-delay: 0.2s">
        <div class="workflow-header">
            <h5 class="workflow-title">
                <i class="bi bi-list-check me-2"></i> {% trans "Workflow Stages List" %}
            </h5>
        </div>
        <div class="workflow-body">
            {% if workflow_stages %}
                <div class="table-responsive">
                    <table class="table table-hover workflow-table">
                        <thead>
                            <tr>
                                <th style="width: 8%;">{% trans "Order" %}</th>
                                <th style="width: 25%;">{% trans "Stage Name" %}</th>
                                <th style="width: 20%;">{% trans "Department" %}</th>
                                <th style="width: 15%;">{% trans "Duration" %}</th>
                                <th style="width: 12%;">{% trans "Critical" %}</th>
                                <th style="width: 20%;">{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stage in workflow_stages %}
                            <tr class="{% if stage.is_critical %}critical-stage{% endif %} animate__animated animate__fadeIn" style="animation-delay: {{ forloop.counter|add:2|divisibleby:10 }}0ms">
                                <td>
                                    <div class="stage-order">{{ stage.order }}</div>
                                </td>
                                <td>
                                    <div class="stage-name">{{ stage.name }}</div>
                                    {% if stage.description %}
                                    <div class="stage-description">{{ stage.description|truncatechars:50 }}</div>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="department-badge">
                                        <i class="bi bi-building me-1"></i> {{ stage.department }}
                                    </span>
                                </td>
                                <td>
                                    <span class="duration-badge">
                                        <i class="bi bi-clock"></i> {{ stage.estimated_duration }}
                                    </span>
                                </td>
                                <td>
                                    {% if stage.is_critical %}
                                    <span class="critical-indicator">
                                        <i class="bi bi-exclamation-triangle-fill me-1"></i> {% trans "Critical" %}
                                    </span>
                                    {% else %}
                                    <span class="text-muted">{% trans "No" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex justify-content-center">
                                        <!-- Single dropdown for all actions -->
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                    type="button"
                                                    data-bs-toggle="dropdown"
                                                    aria-expanded="false">
                                                <i class="bi bi-gear me-1"></i>
                                                {% trans "Actions" %}
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-start">
                                                <!-- View action -->
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'case:workflow_stage_detail' stage.pk %}">
                                                        <i class="bi bi-eye me-2 text-primary"></i>
                                                        {% trans "View Details" %}
                                                    </a>
                                                </li>

                                                <!-- Edit action -->
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'case:workflow_stage_update' stage.pk %}">
                                                        <i class="bi bi-pencil me-2 text-secondary"></i>
                                                        {% trans "Edit Stage" %}
                                                    </a>
                                                </li>

                                                <li><hr class="dropdown-divider"></li>

                                                <!-- Delete action -->
                                                <li>
                                                    <a class="dropdown-item text-danger" href="{% url 'case:workflow_stage_delete' stage.pk %}">
                                                        <i class="bi bi-trash me-2"></i>
                                                        {% trans "Delete Stage" %}
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="bi bi-diagram-3"></i>
                    <h4>{% trans "No Workflow Stages Found" %}</h4>
                    <p>{% trans "You haven't created any workflow stages yet. Workflow stages help you organize your dental lab processes into sequential steps." %}</p>
                    <a href="{% url 'case:workflow_stage_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-lg"></i> {% trans "Create Your First Stage" %}
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                boundary: document.body
            });
        });

        // Add hover effect to table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(13, 110, 253, 0.05)';
            });
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });

        // Fix dropdown positioning
        document.querySelectorAll('.dropdown').forEach(dropdown => {
            dropdown.addEventListener('show.bs.dropdown', function () {
                const dropdownMenu = this.querySelector('.dropdown-menu');
                const tableContainer = document.querySelector('.table-responsive');

                // Reset any previous styles
                dropdownMenu.style.maxHeight = '';

                // After the dropdown is shown, check if it's visible
                setTimeout(() => {
                    const rect = dropdownMenu.getBoundingClientRect();
                    const tableRect = tableContainer.getBoundingClientRect();

                    // Check if dropdown extends beyond table bottom
                    if (rect.bottom > tableRect.bottom) {
                        const newMaxHeight = tableRect.bottom - rect.top - 10;
                        dropdownMenu.style.maxHeight = newMaxHeight + 'px';
                        dropdownMenu.style.overflowY = 'auto';
                    }

                    // Check if dropdown extends beyond right edge
                    if (rect.right > tableRect.right) {
                        dropdownMenu.classList.remove('dropdown-menu-start');
                        dropdownMenu.classList.add('dropdown-menu-end');
                    }

                    // Check if dropdown extends beyond left edge
                    if (rect.left < tableRect.left) {
                        dropdownMenu.classList.remove('dropdown-menu-end');
                        dropdownMenu.classList.add('dropdown-menu-start');
                    }
                }, 0);
            });
        });

        // Add animation to filter collapse
        const filterCollapse = document.getElementById('filterCollapse');
        if (filterCollapse) {
            filterCollapse.addEventListener('show.bs.collapse', function () {
                const chevron = document.querySelector('[data-bs-target="#filterCollapse"] i');
                if (chevron) {
                    chevron.classList.remove('bi-chevron-down');
                    chevron.classList.add('bi-chevron-up');
                }
            });

            filterCollapse.addEventListener('hide.bs.collapse', function () {
                const chevron = document.querySelector('[data-bs-target="#filterCollapse"] i');
                if (chevron) {
                    chevron.classList.remove('bi-chevron-up');
                    chevron.classList.add('bi-chevron-down');
                }
            });
        }
    });
</script>
{% endblock %}