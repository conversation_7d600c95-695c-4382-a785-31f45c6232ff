INFO 2025-05-24 18:04:25,853 autoreload 19352 8840 Watching for file changes with StatReloader
ERROR 2025-05-24 18:05:46,927 exceptions 8924 1500 Exception occurred: ['Invalid data']
NoneType: None
ERROR 2025-05-24 18:05:46,928 exceptions 8924 1500 Exception occurred: Unknown error
NoneType: None
ERROR 2025-05-24 18:05:47,052 validators 8924 1500 Validation error for case: '<' not supported between instances of 'str' and 'int'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\validators.py", line 382, in validate_model_data
    field_errors = CentralizedValidationService._validate_fields(model_class, data)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\validators.py", line 420, in _validate_fields
    validator(value)
    ~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\validators.py", line 395, in __call__
    if self.compare(cleaned, limit_value):
       ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\validators.py", line 429, in compare
    return a < b
           ^^^^^
TypeError: '<' not supported between instances of 'str' and 'int'
ERROR 2025-05-24 18:06:48,572 exceptions 3996 1568 Exception occurred: ['Invalid data']
NoneType: None
ERROR 2025-05-24 18:06:48,573 exceptions 3996 1568 Exception occurred: Unknown error
NoneType: None
ERROR 2025-05-24 18:06:48,695 validators 3996 1568 Validation error for case: '__proxy__' object has no attribute 'extend'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\validators.py", line 411, in validate_model_data
    errors.setdefault(field, []).extend(field_error_list)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: '__proxy__' object has no attribute 'extend'
INFO 2025-05-24 18:10:18,073 basehttp 19352 20992 "GET / HTTP/1.1" 200 140233
INFO 2025-05-24 18:11:31,024 autoreload 19352 8840 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:11:32,415 autoreload 5728 11864 Watching for file changes with StatReloader
INFO 2025-05-24 18:11:35,217 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:37,843 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:40,222 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:42,260 basehttp 5728 20692 "GET / HTTP/1.1" 200 140233
INFO 2025-05-24 18:11:44,811 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:49,311 basehttp 5728 20692 "GET /case/case/15/ HTTP/1.1" 200 166812
INFO 2025-05-24 18:11:55,852 autoreload 5728 11864 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:11:57,286 autoreload 21180 6188 Watching for file changes with StatReloader
INFO 2025-05-24 18:12:12,302 basehttp 21180 20996 "GET /case/case/15/update/ HTTP/1.1" 200 221456
INFO 2025-05-24 18:12:20,222 autoreload 21180 6188 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:12:21,765 autoreload 4020 5876 Watching for file changes with StatReloader
INFO 2025-05-24 18:12:27,931 basehttp 4020 19144 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:12:29,845 basehttp 4020 19144 "GET /case/create/ HTTP/1.1" 200 222948
INFO 2025-05-24 18:12:42,809 autoreload 4020 5876 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:12:44,195 autoreload 21292 9656 Watching for file changes with StatReloader
INFO 2025-05-24 18:13:02,112 autoreload 21292 9656 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:13:03,638 autoreload 17420 3632 Watching for file changes with StatReloader
INFO 2025-05-24 18:13:48,176 basehttp 17420 5992 "POST /case/create/ HTTP/1.1" 302 0
INFO 2025-05-24 18:13:48,524 basehttp 17420 5992 "GET /case/case/16/ HTTP/1.1" 200 163413
INFO 2025-05-24 18:13:51,531 basehttp 17420 5992 "GET /case/list/ HTTP/1.1" 200 138335
ERROR 2025-05-24 18:16:34,629 status_synchronization 3192 22128 Error validating status consistency: No module named 'schedule'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\status_synchronization.py", line 320, in validate_status_consistency
    from schedule.models import Schedule
ModuleNotFoundError: No module named 'schedule'
ERROR 2025-05-24 18:16:34,635 dependency_management 3192 22128 Error getting task dependency chain: 'list' object has no attribute 'exclude'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\dependency_management.py", line 189, in get_task_dependency_chain
    incomplete_deps = direct_deps.exclude(status='completed')
                      ^^^^^^^^^^^^^^^^^^^
AttributeError: 'list' object has no attribute 'exclude'
ERROR 2025-05-24 18:16:34,641 exceptions 3192 22128 Exception occurred: Test error
NoneType: None
INFO 2025-05-24 18:20:42,907 autoreload 17420 3632 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\signals.py changed, reloading.
INFO 2025-05-24 18:20:44,745 autoreload 9624 21880 Watching for file changes with StatReloader
INFO 2025-05-24 18:20:47,848 basehttp 9624 7296 "GET /case/list/ HTTP/1.1" 200 138335
INFO 2025-05-24 18:20:50,055 basehttp 9624 7296 "GET /case/list/ HTTP/1.1" 200 138335
INFO 2025-05-24 18:20:54,905 basehttp 9624 7296 "GET /case/create/ HTTP/1.1" 200 232709
INFO 2025-05-24 18:21:24,082 basehttp 9624 7296 "GET /case/list/ HTTP/1.1" 200 138335
INFO 2025-05-24 18:21:26,521 basehttp 9624 7296 "GET /case/case/16/ HTTP/1.1" 302 0
INFO 2025-05-24 18:21:26,640 basehttp 9624 7296 "GET /case/list/ HTTP/1.1" 200 138698
INFO 2025-05-24 18:21:29,733 basehttp 9624 7296 "GET /case/case/16/ HTTP/1.1" 302 0
INFO 2025-05-24 18:21:29,847 basehttp 9624 7296 "GET /case/list/ HTTP/1.1" 200 138698
INFO 2025-05-24 18:22:43,476 autoreload 22444 16588 Watching for file changes with StatReloader
INFO 2025-05-24 18:22:47,133 basehttp 22444 11404 "GET /case/list/ HTTP/1.1" 200 138335
INFO 2025-05-24 18:22:49,785 basehttp 22444 11404 "GET /case/case/16/ HTTP/1.1" 200 194356
INFO 2025-05-24 18:23:14,983 basehttp 22444 11404 "GET /case/create/ HTTP/1.1" 200 232709
INFO 2025-05-24 18:23:55,936 basehttp 22444 11404 "GET /case/list/ HTTP/1.1" 200 138335
INFO 2025-05-24 18:23:58,050 autoreload 22444 16588 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:24:00,222 autoreload 20744 22220 Watching for file changes with StatReloader
INFO 2025-05-24 18:24:03,398 basehttp 20744 4928 "GET /case/gantt/ HTTP/1.1" 200 91688
INFO 2025-05-24 18:24:04,041 basehttp 20744 4928 "GET /static/js/production-timeline/utils.js HTTP/1.1" 200 5319
INFO 2025-05-24 18:24:04,070 basehttp 20744 15416 "GET /static/js/production-timeline/app.js HTTP/1.1" 200 5916
INFO 2025-05-24 18:24:04,124 basehttp 20744 15216 "GET /static/js/production-timeline/pdf-export.js HTTP/1.1" 200 8663
INFO 2025-05-24 18:24:04,131 basehttp 20744 8480 "GET /static/js/production-timeline/toast.js HTTP/1.1" 200 3997
INFO 2025-05-24 18:24:04,132 basehttp 20744 8904 "GET /static/js/production-timeline/case-management.js HTTP/1.1" 200 12228
INFO 2025-05-24 18:24:04,133 basehttp 20744 20708 "GET /static/js/production-timeline/gantt-chart-improved.js HTTP/1.1" 200 12348
INFO 2025-05-24 18:24:15,306 basehttp 20744 8904 "GET /case/case/calendar/ HTTP/1.1" 200 63594
INFO 2025-05-24 18:24:15,912 basehttp 20744 8904 "GET /case/case/calendar/ HTTP/1.1" 200 4244
INFO 2025-05-24 18:24:22,912 basehttp 20744 8904 "GET /case/dhtmlx-gantt/ HTTP/1.1" 200 90505
INFO 2025-05-24 18:24:31,597 basehttp 20744 8904 "GET / HTTP/1.1" 200 128546
INFO 2025-05-24 18:24:38,125 basehttp 20744 8904 "GET /?range=365 HTTP/1.1" 200 140244
ERROR 2025-05-24 18:26:01,687 scheduling_engine 6936 7584 Error analyzing department capacity: Field 'id' expected a number but got <__main__.test_capacity_planning_service.<locals>.MockDepartment object at 0x00000164654C8830>.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
TypeError: int() argument must be a string, a bytes-like object or a real number, not 'MockDepartment'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 624, in analyze_department_capacity
    items = ScheduleItem.objects.filter(
        department=department,
        start_time__date__gte=start_date,
        start_time__date__lte=end_date
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
TypeError: Field 'id' expected a number but got <__main__.test_capacity_planning_service.<locals>.MockDepartment object at 0x00000164654C8830>.
ERROR 2025-05-24 18:26:01,711 scheduling_engine 6936 7584 Error predicting capacity needs: Field 'id' expected a number but got <__main__.test_capacity_planning_service.<locals>.MockDepartment object at 0x00000164654C8830>.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
TypeError: int() argument must be a string, a bytes-like object or a real number, not 'MockDepartment'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 874, in predict_future_capacity_needs
    historical_items = ScheduleItem.objects.filter(
        department=department,
        start_time__date__gte=start_date,
        start_time__date__lte=end_date
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
TypeError: Field 'id' expected a number but got <__main__.test_capacity_planning_service.<locals>.MockDepartment object at 0x00000164654C8830>.
INFO 2025-05-24 18:26:01,962 scheduling_engine 6936 7584 Starting advanced scheduling for case #TEST001
ERROR 2025-05-24 18:26:01,963 scheduling_engine 6936 7584 Scheduling failed for case #TEST001: min() iterable argument is empty
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 96, in schedule_case
    schedule = self._create_optimized_schedule(case, allocation)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 497, in _create_optimized_schedule
    start_time = min(alloc.start_time for alloc in allocations)
ValueError: min() iterable argument is empty
INFO 2025-05-24 18:26:01,964 scheduling_engine 6936 7584 Starting advanced scheduling for case #TEST001
ERROR 2025-05-24 18:26:01,964 scheduling_engine 6936 7584 Scheduling failed for case #TEST001: min() iterable argument is empty
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 96, in schedule_case
    schedule = self._create_optimized_schedule(case, allocation)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 497, in _create_optimized_schedule
    start_time = min(alloc.start_time for alloc in allocations)
ValueError: min() iterable argument is empty
INFO 2025-05-24 18:26:01,967 scheduling_engine 6936 7584 Starting advanced scheduling for case #TEST001
ERROR 2025-05-24 18:26:01,968 scheduling_engine 6936 7584 Scheduling failed for case #TEST001: min() iterable argument is empty
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 96, in schedule_case
    schedule = self._create_optimized_schedule(case, allocation)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 497, in _create_optimized_schedule
    start_time = min(alloc.start_time for alloc in allocations)
ValueError: min() iterable argument is empty
INFO 2025-05-24 18:26:01,971 scheduling_engine 6936 7584 Starting advanced scheduling for case #TEST001
ERROR 2025-05-24 18:26:01,971 scheduling_engine 6936 7584 Scheduling failed for case #TEST001: min() iterable argument is empty
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 96, in schedule_case
    schedule = self._create_optimized_schedule(case, allocation)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 497, in _create_optimized_schedule
    start_time = min(alloc.start_time for alloc in allocations)
ValueError: min() iterable argument is empty
INFO 2025-05-24 18:26:01,998 services 6936 7584 Conflict detection completed: 0 conflicts found
ERROR 2025-05-24 18:26:02,005 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "3D Printing": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "3D Printing": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,010 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "CAD/CAM Design": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "CAD/CAM Design": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,015 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Casting": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Casting": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,023 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Ceramics": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Ceramics": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,029 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Finishing": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Finishing": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,037 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Implants": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Implants": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,044 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Milling": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Milling": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,052 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Quality Control": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Quality Control": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,062 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Reception": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Reception": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,070 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Shipping": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Shipping": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,077 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Test Department": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Test Department": Must be "UserDepartment" instance.
INFO 2025-05-24 18:26:02,082 services 6936 7584 Workload balance analysis completed
INFO 2025-05-24 18:26:15,035 scheduling_engine 14236 19472 Starting advanced scheduling for case #16
ERROR 2025-05-24 18:26:15,036 scheduling_engine 14236 19472 Scheduling failed for case #16: 'Case' object has no attribute 'id'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 88, in schedule_case
    constraints = self._generate_constraints(case, requirements)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 178, in _generate_constraints
    entity_id=case.id,
              ^^^^^^^
AttributeError: 'Case' object has no attribute 'id'
WARNING 2025-05-24 18:26:15,039 services 14236 19472 Failed to create optimized schedule for case #16: Scheduling failed: 'Case' object has no attribute 'id'
