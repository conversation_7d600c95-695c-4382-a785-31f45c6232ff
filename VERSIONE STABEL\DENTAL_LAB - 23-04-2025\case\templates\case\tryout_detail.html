{% extends 'base.html' %}

{% block extra_css %}
<style>
    .detail-container {
        max-width: 1000px;
        margin: 0 auto;
    }

    .status-badge {
        padding: 0.375rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .status-scheduled {
        background-color: #dbeafe;
        color: #1e40af;
    }

    .status-completed {
        background-color: #dcfce7;
        color: #166534;
    }

    .status-cancelled {
        background-color: #fee2e2;
        color: #991b1b;
    }

    .detail-section {
        background: #f8fafc;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .attachments-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .attachment-card {
        border: 1px solid #e2e8f0;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .attachment-preview {
        height: 200px;
        background-color: #f8fafc;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .attachment-preview img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .attachment-info {
        padding: 0.75rem;
        background: white;
    }

    .file-icon {
        font-size: 3rem;
        color: #64748b;
    }
</style>
{% endblock %}

{% block content %}
<div class="detail-container mt-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">Tryout Details</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{% url 'case:tryout_list' %}">Tryouts</a></li>
                    <li class="breadcrumb-item active">Detail</li>
                </ol>
            </nav>
        </div>
        <div class="d-flex gap-2">
            <a href="{% url 'case:tryout_update' tryout.id %}" class="btn btn-primary">
                <i class="bi bi-pencil-square me-2"></i>Edit
            </a>
            <form action="{% url 'scheduling:tryout_schedule_integration' %}" method="post" class="d-inline">
                {% csrf_token %}
                <input type="hidden" name="tryout_id" value="{{ tryout.id }}">
                <button type="submit" class="btn btn-success">
                    <i class="bi bi-calendar-plus me-2"></i>Add to Schedule
                </button>
            </form>
            <a href="{% url 'case:tryout_list' %}" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>Back
            </a>
        </div>
    </div>

    <!-- Basic Information -->
    <div class="detail-section">
        <h5 class="mb-4"><i class="bi bi-info-circle me-2"></i>Basic Information</h5>
        <div class="row g-3">
            <div class="col-md-6">
                <div class="d-flex align-items-center mb-3">
                    <i class="bi bi-folder me-2 text-primary"></i>
                    <div>
                        <div class="text-muted small">Case Number</div>
                        <div class="fw-medium">#{{ tryout.case.case_number }}</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex align-items-center mb-3">
                    <i class="bi bi-geo-alt me-2 text-primary"></i>
                    <div>
                        <div class="text-muted small">Location</div>
                        <div class="fw-medium">{{ tryout.location.name }}</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex align-items-center mb-3">
                    <i class="bi bi-calendar-event me-2 text-primary"></i>
                    <div>
                        <div class="text-muted small">Scheduled For</div>
                        <div class="fw-medium">{{ tryout.date_time|date:"d M Y H:i" }}</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex align-items-center mb-3">
                    <i class="bi bi-clock me-2 text-primary"></i>
                    <div>
                        <div class="text-muted small">Duration</div>
                        <div class="fw-medium">{{ tryout.duration|default:"Not specified" }}</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <i class="bi bi-check-circle me-2 text-primary"></i>
                    <div>
                        <div class="text-muted small">Status</div>
                        <span class="status-badge {% if tryout.status == 'completed' %}status-completed
                                  {% elif tryout.status == 'scheduled' %}status-scheduled
                                  {% else %}status-cancelled{% endif %}">
                            {{ tryout.get_status_display }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <i class="bi bi-shield-check me-2 text-primary"></i>
                    <div>
                        <div class="text-muted small">Confirmation</div>
                        <div class="fw-medium">
                            {% if tryout.confirmation_status %}
                                <span class="text-success">Confirmed</span>
                            {% else %}
                                <span class="text-warning">Not Confirmed</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notes Section -->
    {% if tryout.notes %}
    <div class="detail-section">
        <h5 class="mb-4"><i class="bi bi-journal-text me-2"></i>Notes</h5>
        <div class="p-3 bg-white rounded">
            {{ tryout.notes|linebreaks }}
        </div>
    </div>
    {% endif %}

    <!-- Feedback Section -->
    {% if tryout.feedback %}
    <div class="detail-section">
        <h5 class="mb-4"><i class="bi bi-chat-dots me-2"></i>Feedback</h5>
        <div class="row g-3">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <i class="bi bi-star-fill me-2 text-warning"></i>
                    <div>
                        <div class="text-muted small">Rating</div>
                        <div class="fw-medium">{{ tryout.feedback.rating }}/5</div>
                    </div>
                </div>
            </div>
            {% if tryout.feedback.comments %}
            <div class="col-12">
                <div class="p-3 bg-white rounded">
                    {{ tryout.feedback.comments|linebreaks }}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Attachments Section -->
    {% if tryout.tryout_attachments.exists %}
    <div class="detail-section">
        <h5 class="mb-4"><i class="bi bi-paperclip me-2"></i>Attachments</h5>
        <div class="attachments-grid">
            {% for attachment in tryout.tryout_attachments.all %}
            <div class="attachment-card">
                <div class="attachment-preview">
                    {% if attachment.is_image %}
                        <img src="{{ attachment.attachment.url }}" alt="Attachment Preview">
                    {% else %}
                        <i class="bi bi-file-earmark-text file-icon"></i>
                    {% endif %}
                </div>
                <div class="attachment-info">
                    <div class="text-truncate mb-2">{{ attachment.attachment.name|slice:"15:" }}</div>
                    <a href="{{ attachment.attachment.url }}"
                       class="btn btn-sm btn-outline-primary w-100"
                       target="_blank">
                        {% if attachment.is_image %}
                            <i class="bi bi-eye me-1"></i>View
                        {% else %}
                            <i class="bi bi-download me-1"></i>Download
                        {% endif %}
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}