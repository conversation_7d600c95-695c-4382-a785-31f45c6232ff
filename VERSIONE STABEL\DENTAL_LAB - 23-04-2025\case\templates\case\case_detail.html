{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Case Detail: {{ case.case_number }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
    /* Color Variables */
    :root {
        --primary: #4f46e5;
        --primary-light: #6366f1;
        --primary-dark: #4338ca;
        --secondary: #64748b;
        --success: #22c55e;
        --success-light: #86efac;
        --danger: #ef4444;
        --danger-light: #fca5a5;
        --warning: #f59e0b;
        --warning-light: #fcd34d;
        --info: #3b82f6;
        --info-light: #93c5fd;
        --gray-50: #f8fafc;
        --gray-100: #f1f5f9;
        --gray-200: #e2e8f0;
        --gray-300: #cbd5e1;
        --gray-400: #94a3b8;
        --gray-500: #64748b;
        --gray-600: #475569;
        --gray-700: #334155;
        --gray-800: #1e293b;
        --gray-900: #0f172a;
    }

    /* Main Container */
    .case-detail-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 1.5rem;
    }

    /* Page Header */
    .page-header {
        margin-bottom: 2rem;
        position: relative;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-800);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .page-subtitle {
        color: var(--gray-600);
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    /* Card Styles */
    .card {
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: none;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .card-header {
        background: linear-gradient(to right, var(--primary-dark), var(--primary));
        color: white;
        padding: 1.25rem 1.5rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header h4 {
        margin: 0;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .card-header h4 i {
        font-size: 1.5rem;
    }

    .card-header .card-actions {
        display: flex;
        gap: 0.5rem;
    }

    .card-body {
        background-color: white;
        padding: 1.5rem;
        flex: 1;
    }

    .card-title {
        color: var(--gray-800);
        font-weight: 600;
        margin-bottom: 1.25rem;
        font-size: 1.1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--gray-200);
    }

    /* Info Item Styles */
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .info-item {
        padding: 0.75rem;
        border-radius: 0.5rem;
        background-color: var(--gray-50);
        transition: all 0.2s ease;
        display: flex;
        flex-direction: column;
    }

    .info-item:hover {
        background-color: var(--gray-100);
        transform: translateY(-2px);
    }

    .info-label {
        font-size: 0.875rem;
        color: var(--gray-500);
        margin-bottom: 0.25rem;
    }

    .info-value {
        font-weight: 600;
        color: var(--gray-800);
        font-size: 1rem;
    }

    /* Status Badge Styles */
    .status-badge {
        padding: 0.35rem 0.75rem;
        border-radius: 9999px;
        font-weight: 600;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
    }

    .status-pending {
        background-color: var(--primary-light);
        color: white;
    }

    .status-in_progress {
        background-color: var(--warning);
        color: var(--gray-900);
    }

    .status-completed {
        background-color: var(--success);
        color: white;
    }

    .status-cancelled {
        background-color: var(--danger);
        color: white;
    }

    /* Priority Indicator */
    .priority-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.35rem 0.75rem;
        border-radius: 9999px;
        font-weight: 600;
        font-size: 0.75rem;
    }

    .priority-1 { /* Low */
        background-color: var(--success-light);
        color: var(--success);
    }

    .priority-2 { /* Medium */
        background-color: var(--info-light);
        color: var(--info);
    }

    .priority-3 { /* High */
        background-color: var(--warning-light);
        color: var(--warning);
    }

    .priority-4 { /* Urgent */
        background-color: var(--danger-light);
        color: var(--danger);
    }

    /* Table Styles */
    .table-container {
        overflow-x: auto;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .custom-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .custom-table thead th {
        background-color: var(--gray-100);
        color: var(--gray-700);
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
        padding: 0.75rem 1rem;
        border-bottom: 2px solid var(--gray-200);
    }

    .custom-table tbody tr {
        transition: all 0.2s ease;
    }

    .custom-table tbody tr:hover {
        background-color: var(--gray-50);
    }

    .custom-table tbody td {
        padding: 1rem;
        border-bottom: 1px solid var(--gray-200);
        color: var(--gray-700);
        vertical-align: middle;
    }

    .custom-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        justify-content: center;
        margin: 2rem 0;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-lg {
        padding: 0.875rem 1.75rem;
        font-size: 1.1rem;
    }

    .btn-primary {
        background-color: var(--primary);
        border-color: var(--primary);
        color: white;
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .btn-success {
        background-color: var(--success);
        border-color: var(--success);
        color: white;
    }

    .btn-success:hover {
        background-color: #1da750;
        border-color: #1da750;
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .btn-warning {
        background-color: var(--warning);
        border-color: var(--warning);
        color: white;
    }

    .btn-warning:hover {
        background-color: #e08e00;
        border-color: #e08e00;
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .btn-secondary {
        background-color: var(--gray-500);
        border-color: var(--gray-500);
        color: white;
    }

    .btn-secondary:hover {
        background-color: var(--gray-600);
        border-color: var(--gray-600);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    /* Progress Bar */
    .progress {
        height: 0.75rem;
        border-radius: 9999px;
        background-color: var(--gray-200);
        overflow: hidden;
    }

    .progress-bar {
        background: linear-gradient(to right, var(--primary), var(--primary-light));
        height: 100%;
        border-radius: 9999px;
        transition: width 1s ease;
    }

    /* Teeth Selection */
    .teeth-selection-container {
        background-color: white;
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .selected-teeth-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    /* Alert Styles */
    .alert {
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .alert-success {
        background-color: rgba(34, 197, 94, 0.1);
        border: 1px solid var(--success-light);
        color: var(--success);
    }

    .alert-warning {
        background-color: rgba(245, 158, 11, 0.1);
        border: 1px solid var(--warning-light);
        color: var(--warning);
    }

    .alert i {
        font-size: 1.25rem;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .info-grid {
            grid-template-columns: 1fr;
        }

        .action-buttons {
            flex-direction: column;
        }

        .btn {
            width: 100%;
        }
    }

    /* Animation Classes */
    .animate-fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    .animate-slide-up {
        animation: slideUp 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideUp {
        from { transform: translateY(20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    /* Tab Navigation */
    .nav-tabs {
        border-bottom: 1px solid var(--gray-200);
        margin-bottom: 1.5rem;
        display: flex;
        overflow-x: auto;
        scrollbar-width: none; /* Firefox */
        background-color: var(--gray-50);
        border-radius: 0.5rem 0.5rem 0 0;
        padding: 0.5rem 0.5rem 0 0.5rem;
    }

    .nav-tabs::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
    }

    .nav-tabs .nav-item {
        margin-bottom: -1px;
        margin-right: 0.25rem;
    }

    .nav-tabs .nav-link {
        border: 1px solid var(--gray-200);
        border-bottom: 3px solid transparent;
        border-radius: 0.5rem 0.5rem 0 0;
        padding: 0.75rem 1.25rem;
        font-weight: 600;
        color: var(--gray-700);
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background-color: var(--gray-100);
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.02);
    }

    .nav-tabs .nav-link:hover {
        color: var(--primary);
        border-color: var(--primary-light);
        background-color: white;
        transform: translateY(-2px);
    }

    .nav-tabs .nav-link.active {
        color: var(--primary);
        border-color: var(--primary);
        border-bottom-color: white;
        background-color: white;
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }

    .nav-tabs .nav-link i {
        font-size: 1.25rem;
        color: var(--primary);
    }

    .tab-text {
        font-weight: 600;
        margin: 0 0.25rem;
        display: inline-block;
    }

    .tab-content {
        background-color: white;
        border: 1px solid var(--gray-200);
        border-top: none;
        border-radius: 0 0 0.5rem 0.5rem;
        padding: 1rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    /* Tooltip Styles */
    .tooltip-icon {
        color: var(--gray-400);
        cursor: help;
        margin-left: 0.25rem;
        font-size: 0.875rem;
    }

    /* Dental Chart Styles */
    .dental-chart-container {
        background-color: white;
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .dental-chart-container svg {
        max-width: 100%;
        height: auto;
    }

    /* Timeline Styles */
    .timeline {
        position: relative;
        padding-left: 2rem;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: var(--gray-200);
    }

    .timeline-item {
        position: relative;
        padding-bottom: 1.5rem;
    }

    .timeline-item::before {
        content: '';
        position: absolute;
        left: -2rem;
        top: 0.25rem;
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        background-color: var(--primary);
        border: 2px solid white;
    }

    .timeline-item:last-child {
        padding-bottom: 0;
    }

    .timeline-date {
        font-size: 0.875rem;
        color: var(--gray-500);
        margin-bottom: 0.25rem;
    }

    .timeline-content {
        background-color: var(--gray-50);
        border-radius: 0.5rem;
        padding: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="case-detail-container animate-fade-in">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}" class="text-decoration-none"><i class="bi bi-house-door"></i> Home</a></li>
            <li class="breadcrumb-item"><a href="{% url 'case:case_list' %}" class="text-decoration-none"><i class="bi bi-folder2"></i> Cases</a></li>
            <li class="breadcrumb-item active" aria-current="page"><i class="bi bi-folder2-open"></i> Case #{{ case.case_number }}</li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="page-header d-flex justify-content-between align-items-center flex-wrap">
        <div>
            <h1 class="page-title">
                <i class="bi bi-folder2-open text-primary"></i>
                Case #{{ case.case_number }}
                <span class="status-badge status-{{ case.status }} ms-2">{{ case.get_status_display }}</span>
            </h1>
            <p class="page-subtitle">
                <i class="bi bi-person-fill me-1"></i> Patient: {{ case.patient.get_full_name }}
                <span class="mx-2">|</span>
                <i class="bi bi-person-badge me-1"></i> Dentist: {{ case.dentist.get_full_name }}
            </p>
        </div>
        <div class="d-flex gap-2 mt-2 mt-md-0">
            <a href="{% url 'case:case_update' case.case_number %}" class="btn btn-outline-primary">
                <i class="bi bi-pencil-square"></i> Edit Case
            </a>
            <div class="dropdown">
                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="caseActionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i> Actions
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="caseActionsDropdown">
                    <li><a class="dropdown-item" href="{% url 'case:task_create' case.case_number %}"><i class="bi bi-plus-circle me-2"></i> Add Task</a></li>
                    <li><a class="dropdown-item" href="{% url 'case:tryout_create' case.case_number %}"><i class="bi bi-plus-circle me-2"></i> Add Tryout</a></li>
                    {% if not case.invoice %}
                    <li><a class="dropdown-item" href="{% url 'billing:invoice_create' case_id=case.case_number %}"><i class="bi bi-file-earmark-plus me-2"></i> Create Invoice</a></li>
                    {% endif %}
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" id="printCaseBtn"><i class="bi bi-printer me-2"></i> Print Case</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Tab Navigation -->
    <ul class="nav nav-tabs" id="caseDetailTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                <i class="bi bi-info-circle"></i>
                <span class="tab-text">Overview</span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="items-tab" data-bs-toggle="tab" data-bs-target="#items" type="button" role="tab" aria-controls="items" aria-selected="false">
                <i class="bi bi-box"></i>
                <span class="tab-text">Items</span>
                <span class="badge rounded-pill bg-primary ms-1">{{ item_stats.total }}</span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab" aria-controls="tasks" aria-selected="false">
                <i class="bi bi-list-task"></i>
                <span class="tab-text">Tasks</span>
                <span class="badge rounded-pill bg-primary ms-1">{{ task_stats.total }}</span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="tryouts-tab" data-bs-toggle="tab" data-bs-target="#tryouts" type="button" role="tab" aria-controls="tryouts" aria-selected="false">
                <i class="bi bi-check2-circle"></i>
                <span class="tab-text">Tryouts</span>
                <span class="badge rounded-pill bg-primary ms-1">{{ tryouts|length }}</span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="invoice-tab" data-bs-toggle="tab" data-bs-target="#invoice" type="button" role="tab" aria-controls="invoice" aria-selected="false">
                <i class="bi bi-receipt"></i>
                <span class="tab-text">Invoice</span>
                {% if case.invoice %}
                <span class="badge rounded-pill bg-success ms-1">1</span>
                {% else %}
                <span class="badge rounded-pill bg-secondary ms-1">0</span>
                {% endif %}
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="caseDetailTabContent">
        <!-- Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
            <div class="row mt-4">
                <!-- Case Detail Card -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="bi bi-info-circle"></i> Case Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="info-grid">
                                <div class="info-item">
                                    <div class="info-label">Status</div>
                                    <div class="info-value">
                                        <span class="status-badge status-{{ case.status }}">
                                            <i class="bi bi-circle-fill me-1"></i>
                                            {{ case.get_status_display }}
                                        </span>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Priority</div>
                                    <div class="info-value">
                                        <span class="priority-indicator priority-{{ case.priority }}">
                                            {% if case.priority == 4 %}
                                                <i class="bi bi-exclamation-triangle-fill"></i>
                                            {% elif case.priority == 3 %}
                                                <i class="bi bi-arrow-up-circle-fill"></i>
                                            {% elif case.priority == 2 %}
                                                <i class="bi bi-dash-circle-fill"></i>
                                            {% else %}
                                                <i class="bi bi-arrow-down-circle-fill"></i>
                                            {% endif %}
                                            {{ case.get_priority_display }}
                                        </span>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Received Date</div>
                                    <div class="info-value">
                                        <i class="bi bi-calendar-check me-1 text-success"></i>
                                        {{ case.received_date_time|date:"Y-m-d H:i"|default:"Not set" }}
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Deadline</div>
                                    <div class="info-value">
                                        <i class="bi bi-calendar-x me-1 text-danger"></i>
                                        {{ case.deadline|date:"Y-m-d H:i"|default:"Not set" }}
                                        {% if case.deadline %}
                                            <small class="text-muted d-block">
                                                ({{ case.deadline|timeuntil }} remaining)
                                            </small>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Finished Date</div>
                                    <div class="info-value">
                                        <i class="bi bi-calendar-check-fill me-1 text-primary"></i>
                                        {{ case.finished_date_time|date:"Y-m-d H:i"|default:"Not completed" }}
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Ship Date</div>
                                    <div class="info-value">
                                        <i class="bi bi-truck me-1 text-info"></i>
                                        {{ case.ship_date_time|date:"Y-m-d H:i"|default:"Not shipped" }}
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Current Stage</div>
                                    <div class="info-value">
                                        <i class="bi bi-diagram-3 me-1 text-primary"></i>
                                        {{ case.current_stage.name|default:"Not set" }}
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Next Stage</div>
                                    <div class="info-value">
                                        <i class="bi bi-arrow-right-circle me-1 text-success"></i>
                                        {{ case.next_stage.name|default:"Not set" }}
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Department</div>
                                    <div class="info-value">
                                        <i class="bi bi-building me-1 text-secondary"></i>
                                        {{ case.responsible_department.name|default:"Not assigned" }}
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Estimated Duration</div>
                                    <div class="info-value">
                                        <i class="bi bi-hourglass-split me-1 text-warning"></i>
                                        {{ case.estimated_duration|default:"Not estimated" }}
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Work Duration</div>
                                    <div class="info-value">
                                        <i class="bi bi-clock-history me-1 text-info"></i>
                                        {% if case.received_date_time and case.ship_date_time %}
                                            {{ case.received_date_time|timesince:case.ship_date_time }}
                                        {% else %}
                                            Not completed
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Teeth Color</div>
                                    <div class="info-value">
                                        <i class="bi bi-palette me-1 text-primary"></i>
                                        {{ case.teethcolor|default:"Not specified" }}
                                    </div>
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            <div class="mt-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-muted">Progress</span>
                                    <span class="badge bg-primary">{{ progress|floatformat:1 }}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" role="progressbar" style="width: {{ progress }}%;"
                                         aria-valuenow="{{ progress }}" aria-valuemin="0" aria-valuemax="100">
                                    </div>
                                </div>
                            </div>

                            <!-- Notes Section -->
                            <div class="mt-4">
                                <h6 class="mb-2"><i class="bi bi-journal-text me-1 text-primary"></i> Notes</h6>
                                <div class="p-3 bg-light rounded">
                                    {% if case.notes %}
                                        {{ case.notes|linebreaks }}
                                    {% else %}
                                        <p class="text-muted mb-0"><em>No notes provided</em></p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Teeth Selection Visualization (Read-Only) -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="bi bi-grid-3x3"></i> Teeth Selection</h4>
                        </div>
                        <div class="card-body">
                            <div class="dental-chart-container">
                                <script>
                                    // Make selected teeth available to the select_tooth.html template
                                    var selectedTeethStr = "{{ selected_teeth_str }}";
                                    var selectedTeethNumbers = {{ selected_teeth_numbers|safe }};
                                    console.log("Selected teeth in detail view:", selectedTeethNumbers);
                                </script>
                                {% include 'case/select_tooth.html' %}
                            </div>

                            <!-- Display selected teeth information -->
                            <div class="mt-4">
                                <h6 class="mb-2"><i class="bi bi-list-check me-1 text-primary"></i> Selected Teeth</h6>
                                <div class="selected-teeth-list">
                                    {% if selected_teeth_numbers %}
                                        {% for tooth_number in selected_teeth_numbers %}
                                            <span class="badge bg-primary">{{ tooth_number }}</span>
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">No teeth selected</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Items Tab -->
        <div class="tab-pane fade" id="items" role="tabpanel" aria-labelledby="items-tab">
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4><i class="bi bi-box"></i> Case Items</h4>
                            <a href="{% url 'case:case_update' case.case_number %}#items" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-plus-lg"></i> Add Item
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="custom-table">
                                    <thead>
                                        <tr>
                                            <th>Item</th>
                                            <th>Quantity</th>
                                            <th>Unit</th>
                                            <th>Estimated Time</th>
                                            <th>Actual Time</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for case_item in case.case_items.all %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="item-icon me-2 bg-light rounded-circle p-2">
                                                        <i class="bi bi-box text-primary"></i>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">{{ case_item.item.name }}</div>
                                                        <small class="text-muted">ID: #{{ case_item.id }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="text-center fw-bold">{{ case_item.quantity }}</td>
                                            <td>{{ case_item.unit }}</td>
                                            <td>
                                                <i class="bi bi-hourglass-split text-warning me-1"></i>
                                                {{ case_item.estimated_production_time|default:"Not set" }}
                                            </td>
                                            <td>
                                                <i class="bi bi-hourglass text-success me-1"></i>
                                                {{ case_item.actual_production_time|default_if_none:"Not completed" }}
                                            </td>
                                            <td>
                                                <span class="status-badge status-{{ case_item.status }}">
                                                    <i class="bi bi-circle-fill me-1"></i>
                                                    {{ case_item.get_status_display }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex gap-1">
                                                    <a href="{% url 'case:case_update' case.case_number %}#item-{{ case_item.id }}" class="btn btn-sm btn-outline-primary" title="Edit Item">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <a href="{% url 'case:task_create' case.case_number %}?case_item={{ case_item.id }}" class="btn btn-sm btn-outline-success" title="Create Task">
                                                        <i class="bi bi-list-task"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="empty-state">
                                                    <i class="bi bi-box text-muted fs-1 mb-3"></i>
                                                    <p class="text-muted">No items found for this case.</p>
                                                    <a href="{% url 'case:case_update' case.case_number %}#items" class="btn btn-sm btn-primary mt-2">
                                                        <i class="bi bi-plus-lg"></i> Add First Item
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- Summary Section -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title"><i class="bi bi-clock-history text-primary me-2"></i>Time Summary</h6>
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span>Total Estimated Time:</span>
                                                <span class="badge bg-primary">
                                                    {% with total_time=case.case_items.all|dictsort:"estimated_production_time"|last %}
                                                        {{ total_time.estimated_production_time|default:"N/A" }}
                                                    {% endwith %}
                                                </span>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span>Total Actual Time:</span>
                                                <span class="badge bg-success">
                                                    {% with total_actual=case.case_items.all|dictsort:"actual_production_time"|last %}
                                                        {{ total_actual.actual_production_time|default:"N/A" }}
                                                    {% endwith %}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title"><i class="bi bi-graph-up text-primary me-2"></i>Status Summary</h6>
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span>Total Items:</span>
                                                <span class="badge bg-primary">{{ item_stats.total }}</span>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span>Completed Items:</span>
                                                <span class="badge bg-success">
                                                    {{ item_stats.completed }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Tab -->
        <div class="tab-pane fade" id="invoice" role="tabpanel" aria-labelledby="invoice-tab">
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="bi bi-receipt"></i> Invoice Information</h4>
                        </div>
                        <div class="card-body">
                            {% if case.invoice %}
                                <div class="alert alert-success">
                                    <i class="bi bi-check-circle-fill me-2 fs-4"></i>
                                    <div>
                                        <strong>Invoiced!</strong> This case has been invoiced successfully.
                                        <div class="text-success">Invoice #{{ case.invoice.id }} was created on {{ case.invoice.date|date:"F j, Y" }}.</div>
                                    </div>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <div class="card bg-light h-100">
                                            <div class="card-body">
                                                <h6 class="card-title"><i class="bi bi-info-circle text-primary me-2"></i>Invoice Details</h6>

                                                <div class="info-grid mt-3">
                                                    <div class="info-item">
                                                        <div class="info-label">Invoice Number</div>
                                                        <div class="info-value">#{{ case.invoice.id }}</div>
                                                    </div>

                                                    <div class="info-item">
                                                        <div class="info-label">Date</div>
                                                        <div class="info-value">
                                                            <i class="bi bi-calendar-date me-1 text-primary"></i>
                                                            {{ case.invoice.date|date:"F j, Y" }}
                                                        </div>
                                                    </div>

                                                    <div class="info-item">
                                                        <div class="info-label">Status</div>
                                                        <div class="info-value">
                                                            <span class="status-badge status-{{ case.invoice.status }}">
                                                                {% if case.invoice.status == 'paid' %}
                                                                    <i class="bi bi-check-circle-fill me-1"></i>
                                                                {% elif case.invoice.status == 'partial' %}
                                                                    <i class="bi bi-dash-circle-fill me-1"></i>
                                                                {% elif case.invoice.status == 'unpaid' %}
                                                                    <i class="bi bi-x-circle-fill me-1"></i>
                                                                {% else %}
                                                                    <i class="bi bi-question-circle-fill me-1"></i>
                                                                {% endif %}
                                                                {{ case.invoice.get_status_display }}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div class="info-item">
                                                        <div class="info-label">Due Date</div>
                                                        <div class="info-value">
                                                            <i class="bi bi-calendar-x me-1 text-danger"></i>
                                                            {{ case.invoice.due_date|date:"F j, Y"|default:"Not set" }}
                                                            {% if case.invoice.due_date %}
                                                                <small class="text-muted d-block">
                                                                    {% if case.invoice.due_date|date:'Y-m-d' < now|date:'Y-m-d' and case.invoice.status != 'paid' %}
                                                                        <span class="text-danger">({{ case.invoice.due_date|timesince }} overdue)</span>
                                                                    {% else %}
                                                                        ({{ case.invoice.due_date|timeuntil }} remaining)
                                                                    {% endif %}
                                                                </small>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="card bg-light h-100">
                                            <div class="card-body">
                                                <h6 class="card-title"><i class="bi bi-cash-coin text-primary me-2"></i>Financial Information</h6>

                                                <div class="info-grid mt-3">
                                                    <div class="info-item">
                                                        <div class="info-label">Total Amount</div>
                                                        <div class="info-value fw-bold fs-5 text-success">
                                                            {{ case.invoice.total_amount }} {{ case.invoice.currency.code }}
                                                        </div>
                                                    </div>

                                                    <div class="info-item">
                                                        <div class="info-label">Amount Paid</div>
                                                        <div class="info-value">
                                                            {% if case.invoice.payments.exists %}
                                                                {{ case.invoice.get_total_paid }} {{ case.invoice.currency.code }}
                                                            {% else %}
                                                                0.00 {{ case.invoice.currency.code }}
                                                            {% endif %}
                                                        </div>
                                                    </div>

                                                    <div class="info-item">
                                                        <div class="info-label">Balance Due</div>
                                                        <div class="info-value {% if case.invoice.status != 'paid' %}text-danger fw-bold{% endif %}">
                                                            {% if case.invoice.status == 'paid' %}
                                                                0.00 {{ case.invoice.currency.code }}
                                                            {% else %}
                                                                {{ case.invoice.get_balance_due }} {{ case.invoice.currency.code }}
                                                            {% endif %}
                                                        </div>
                                                    </div>

                                                    <div class="info-item">
                                                        <div class="info-label">Payment Method</div>
                                                        <div class="info-value">
                                                            {% if case.invoice.payments.exists %}
                                                                {% with last_payment=case.invoice.payments.last %}
                                                                    <i class="bi bi-credit-card me-1 text-primary"></i>
                                                                    {{ last_payment.payment_method }}
                                                                {% endwith %}
                                                            {% else %}
                                                                <span class="text-muted">No payments yet</span>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mt-4">
                                    <a href="{% url 'billing:invoice_detail' case.invoice.id %}" class="btn btn-primary">
                                        <i class="bi bi-file-earmark-text me-1"></i> View Full Invoice
                                    </a>
                                    {% if case.invoice.status != 'paid' %}
                                        <a href="{% url 'finance:invoice_payment_create' invoice_id=case.invoice.id %}" class="btn btn-success ms-2">
                                            <i class="bi bi-cash me-1"></i> Record Payment
                                        </a>
                                    {% endif %}
                                    <a href="{% url 'billing:download_invoice_pdf' case.invoice.id %}" class="btn btn-outline-secondary ms-2" target="_blank">
                                        <i class="bi bi-file-pdf me-1"></i> Download PDF
                                    </a>
                                </div>
                            {% else %}
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle-fill me-2 fs-4"></i>
                                    <div>
                                        <strong>Not Invoiced!</strong> This case has not been invoiced yet.
                                        <div>Create an invoice to bill the client for the services provided.</div>
                                    </div>
                                </div>

                                <div class="empty-state text-center py-5">
                                    <i class="bi bi-receipt text-muted fs-1 mb-3"></i>
                                    <h5>No Invoice Created</h5>
                                    <p class="text-muted mb-4">This case doesn't have an invoice yet. Create one to bill the client.</p>
                                    <a href="{% url 'billing:invoice_create' case_id=case.case_number %}" class="btn btn-success">
                                        <i class="bi bi-file-earmark-plus me-1"></i> Create Invoice
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tryouts Tab -->
        <div class="tab-pane fade" id="tryouts" role="tabpanel" aria-labelledby="tryouts-tab">
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4><i class="bi bi-check2-circle"></i> Tryouts</h4>
                            <a href="{% url 'case:tryout_create' case.case_number %}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-plus-lg"></i> Add Tryout
                            </a>
                        </div>
                        <div class="card-body">
                            {% if tryouts %}
                                <div class="table-container">
                                    <table class="custom-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Date & Time</th>
                                                <th>Status</th>
                                                <th>Location</th>
                                                <th>Notes</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for tryout in tryouts %}
                                                <tr>
                                                    <td>#{{ tryout.id }}</td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="date-icon me-2 bg-light rounded-circle p-2">
                                                                <i class="bi bi-calendar-event text-primary"></i>
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold">{{ tryout.date_time|date:"F j, Y" }}</div>
                                                                <small class="text-muted">{{ tryout.date_time|date:"H:i" }}</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="status-badge status-{{ tryout.status }}">
                                                            <i class="bi bi-circle-fill me-1"></i>
                                                            {{ tryout.get_status_display }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <i class="bi bi-geo-alt me-1 text-danger"></i>
                                                        {{ tryout.location.name }}
                                                    </td>
                                                    <td>
                                                        {% if tryout.notes %}
                                                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ tryout.notes }}">
                                                                {{ tryout.notes }}
                                                            </span>
                                                        {% else %}
                                                            <span class="text-muted">No notes</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <div class="d-flex gap-1">
                                                            <a href="{% url 'case:tryout_detail' tryout.id %}" class="btn btn-sm btn-outline-primary" title="View Tryout">
                                                                <i class="bi bi-eye"></i>
                                                            </a>
                                                            <a href="{% url 'case:tryout_update' tryout.id %}" class="btn btn-sm btn-outline-secondary" title="Edit Tryout">
                                                                <i class="bi bi-pencil"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="empty-state text-center py-5">
                                    <i class="bi bi-check2-circle text-muted fs-1 mb-3"></i>
                                    <h5>No Tryouts</h5>
                                    <p class="text-muted mb-4">This case doesn't have any tryouts scheduled yet.</p>
                                    <a href="{% url 'case:tryout_create' case.case_number %}" class="btn btn-primary">
                                        <i class="bi bi-plus-lg me-1"></i> Schedule First Tryout
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tasks Tab -->
        <div class="tab-pane fade" id="tasks" role="tabpanel" aria-labelledby="tasks-tab">
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4><i class="bi bi-list-task"></i> Tasks</h4>
                            <a href="{% url 'case:task_create' case.case_number %}" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-plus-lg"></i> Add Task
                            </a>
                        </div>
                        <div class="card-body">
                            {% if tasks %}
                                <div class="table-container">
                                    <table class="custom-table">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Title</th>
                                                <th>Assigned To</th>
                                                <th>Due Date</th>
                                                <th>Priority</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for task in tasks %}
                                                <tr>
                                                    <td>#{{ task.id }}</td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="task-icon me-2 bg-light rounded-circle p-2">
                                                                <i class="bi bi-clipboard-check text-primary"></i>
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold">{{ task.title }}</div>
                                                                <small class="text-muted text-truncate d-inline-block" style="max-width: 200px;" title="{{ task.description }}">
                                                                    {{ task.description|truncatechars:50 }}
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar-circle me-2">
                                                                <i class="bi bi-person-fill"></i>
                                                            </div>
                                                            <span>{{ task.assigned_to.get_full_name }}</span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        {% if task.due_date %}
                                                            <i class="bi bi-calendar-date me-1 text-danger"></i>
                                                            {{ task.due_date|date:"M d, Y" }}
                                                            <small class="text-muted d-block">
                                                                {% if task.due_date|date:'Y-m-d' < now|date:'Y-m-d' and task.status != 'completed' %}
                                                                    <span class="text-danger">({{ task.due_date|timesince }} overdue)</span>
                                                                {% else %}
                                                                    ({{ task.due_date|timeuntil }} remaining)
                                                                {% endif %}
                                                            </small>
                                                        {% else %}
                                                            <span class="text-muted">Not set</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <span class="priority-indicator priority-{{ task.priority }}">
                                                            {% if task.priority == 4 %}
                                                                <i class="bi bi-exclamation-triangle-fill"></i>
                                                            {% elif task.priority == 3 %}
                                                                <i class="bi bi-arrow-up-circle-fill"></i>
                                                            {% elif task.priority == 2 %}
                                                                <i class="bi bi-dash-circle-fill"></i>
                                                            {% else %}
                                                                <i class="bi bi-arrow-down-circle-fill"></i>
                                                            {% endif %}
                                                            {{ task.get_priority_display }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="status-badge status-{{ task.status }}">
                                                            <i class="bi bi-circle-fill me-1"></i>
                                                            {{ task.get_status_display }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex gap-1">
                                                            <a href="{% url 'case:task_detail' task.id %}" class="btn btn-sm btn-outline-primary" title="View Task">
                                                                <i class="bi bi-eye"></i>
                                                            </a>
                                                            <a href="{% url 'case:task_update' task.id %}" class="btn btn-sm btn-outline-secondary" title="Edit Task">
                                                                <i class="bi bi-pencil"></i>
                                                            </a>
                                                            {% if task.status == 'pending' %}
                                                            <a href="{% url 'case:task_start' task.id %}" class="btn btn-sm btn-outline-success" title="Start Task">
                                                                <i class="bi bi-play-fill"></i>
                                                            </a>
                                                            {% elif task.status == 'in_progress' %}
                                                            <a href="{% url 'case:task_complete' task.id %}" class="btn btn-sm btn-outline-success" title="Complete Task">
                                                                <i class="bi bi-check-lg"></i>
                                                            </a>
                                                            {% endif %}
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% empty %}
                                                <tr>
                                                    <td colspan="7" class="text-center py-4">
                                                        <div class="empty-state">
                                                            <i class="bi bi-list-task text-muted fs-1 mb-3"></i>
                                                            <p class="text-muted">No tasks found for this case.</p>
                                                            <a href="{% url 'case:task_create' case.case_number %}" class="btn btn-sm btn-primary mt-2">
                                                                <i class="bi bi-plus-lg"></i> Create First Task
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Task Summary -->
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6 class="card-title"><i class="bi bi-graph-up text-primary me-2"></i>Task Status</h6>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>Total Tasks:</span>
                                                    <span class="badge bg-primary">{{ task_stats.total }}</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>Completed:</span>
                                                    <span class="badge bg-success">{{ task_stats.completed }}</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>In Progress:</span>
                                                    <span class="badge bg-warning">{{ task_stats.in_progress }}</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span>Pending:</span>
                                                    <span class="badge bg-secondary">{{ task_stats.pending }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card bg-light">
                                            <div class="card-body">
                                                <h6 class="card-title"><i class="bi bi-exclamation-triangle text-primary me-2"></i>Priority Distribution</h6>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>Urgent:</span>
                                                    <span class="badge bg-danger">{{ task_stats.priority.urgent }}</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>High:</span>
                                                    <span class="badge bg-warning">{{ task_stats.priority.high }}</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span>Medium:</span>
                                                    <span class="badge bg-info">{{ task_stats.priority.medium }}</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span>Low:</span>
                                                    <span class="badge bg-success">{{ task_stats.priority.low }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="empty-state text-center py-5">
                                    <i class="bi bi-list-task text-muted fs-1 mb-3"></i>
                                    <h5>No Tasks</h5>
                                    <p class="text-muted mb-4">This case doesn't have any tasks assigned yet.</p>
                                    <a href="{% url 'case:task_create' case.case_number %}" class="btn btn-primary">
                                        <i class="bi bi-plus-lg me-1"></i> Create First Task
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{% url 'case:case_list' %}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back to List
        </a>
        <a href="{% url 'case:case_update' case.case_number %}" class="btn btn-warning">
            <i class="bi bi-pencil-square"></i> Edit Case
        </a>
        <a href="{% url 'case:task_create' case.case_number %}" class="btn btn-primary">
            <i class="bi bi-list-task"></i> Add Task
        </a>
        <a href="{% url 'case:tryout_create' case.case_number %}" class="btn btn-primary">
            <i class="bi bi-check2-circle"></i> Add Tryout
        </a>
        {% if not case.invoice %}
        <a href="{% url 'billing:invoice_create' case_id=case.case_number %}" class="btn btn-success">
            <i class="bi bi-receipt"></i> Create Invoice
        </a>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get the list of selected teeth from the context
        var selectedTeethNumbers = {{ selected_teeth_numbers|safe }};

        // Function to highlight a tooth as selected
        function highlightSelectedTooth(toothNumber) {
            try {
                var toothElement = document.querySelector(`.tooth-${toothNumber}-parent`);
                if (toothElement) {
                    toothElement.style.fill = 'red';
                }
            } catch (e) {
                console.error('Error highlighting tooth:', e);
            }
        }

        // Highlight all selected teeth
        if (selectedTeethNumbers && selectedTeethNumbers.length > 0) {
            selectedTeethNumbers.forEach(function(toothNumber) {
                highlightSelectedTooth(toothNumber);
            });
        }

        // Handle tab navigation from URL hash
        function activateTabFromHash() {
            const hash = window.location.hash;
            if (hash) {
                const tabId = hash.substring(1) + '-tab';
                const tab = document.getElementById(tabId);
                if (tab) {
                    tab.click();
                }
            }
        }

        // Initial activation based on URL hash
        activateTabFromHash();

        // Update hash when tab is changed
        const tabs = document.querySelectorAll('[data-bs-toggle="tab"]');
        tabs.forEach(tab => {
            tab.addEventListener('shown.bs.tab', function (event) {
                const targetId = event.target.getAttribute('data-bs-target').substring(1);
                window.location.hash = targetId;
            });
        });

        // Print functionality
        document.getElementById('printCaseBtn').addEventListener('click', function(e) {
            e.preventDefault();
            window.print();
        });

        // Animate progress bar on load
        const progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = '0%';
            setTimeout(() => {
                progressBar.style.width = progressBar.getAttribute('aria-valuenow') + '%';
            }, 300);
        }
    });
</script>
{% endblock %}