# Generated by Django 5.0.4 on 2025-04-23 01:54

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("case", "0002_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="task",
            options={
                "ordering": ["-priority", "created_at"],
                "verbose_name": "Task",
                "verbose_name_plural": "Tasks",
            },
        ),
        migrations.RemoveIndex(
            model_name="task",
            name="case_task_schedul_ccd25f_idx",
        ),
        migrations.RemoveField(
            model_name="task",
            name="scheduled_end_time",
        ),
        migrations.RemoveField(
            model_name="task",
            name="scheduled_start_time",
        ),
        migrations.AddIndex(
            model_name="task",
            index=models.Index(
                fields=["created_at"], name="case_task_created_ff37dd_idx"
            ),
        ),
    ]
