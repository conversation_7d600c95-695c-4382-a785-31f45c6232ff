"""
Enhanced State Machine Engine for Workflow Management
"""

from enum import Enum
from typing import Dict, List, Optional, Callable, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError

from .exceptions import WorkflowError, BusinessRuleViolation, DataIntegrityError
from .validators import CentralizedValidationService

logger = logging.getLogger(__name__)


class StateTransitionResult(Enum):
    """Results of state transition attempts"""
    SUCCESS = "success"
    BLOCKED = "blocked"
    INVALID = "invalid"
    ERROR = "error"
    PENDING_APPROVAL = "pending_approval"


@dataclass
class TransitionContext:
    """Context information for state transitions"""
    user: Optional[Any] = None
    reason: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    force: bool = False
    validate_dependencies: bool = True
    auto_create_tasks: bool = True
    notify_stakeholders: bool = True


@dataclass
class StateTransition:
    """Represents a state transition"""
    from_state: str
    to_state: str
    condition: Optional[Callable] = None
    action: Optional[Callable] = None
    requires_approval: bool = False
    auto_trigger: bool = False
    priority: int = 0


class WorkflowStateMachine:
    """
    Enhanced state machine for workflow management
    """
    
    def __init__(self, entity_type: str):
        self.entity_type = entity_type
        self.transitions: Dict[str, List[StateTransition]] = {}
        self.state_validators: Dict[str, List[Callable]] = {}
        self.state_actions: Dict[str, List[Callable]] = {}
        self.global_validators: List[Callable] = []
        
    def add_transition(self, transition: StateTransition):
        """Add a state transition"""
        if transition.from_state not in self.transitions:
            self.transitions[transition.from_state] = []
        self.transitions[transition.from_state].append(transition)
        
    def add_state_validator(self, state: str, validator: Callable):
        """Add a validator for a specific state"""
        if state not in self.state_validators:
            self.state_validators[state] = []
        self.state_validators[state].append(validator)
        
    def add_state_action(self, state: str, action: Callable):
        """Add an action to execute when entering a state"""
        if state not in self.state_actions:
            self.state_actions[state] = []
        self.state_actions[state].append(action)
        
    def add_global_validator(self, validator: Callable):
        """Add a global validator that runs for all transitions"""
        self.global_validators.append(validator)
        
    def get_valid_transitions(self, entity, current_state: str) -> List[StateTransition]:
        """Get all valid transitions from the current state"""
        if current_state not in self.transitions:
            return []
            
        valid_transitions = []
        for transition in self.transitions[current_state]:
            if self._can_transition(entity, transition):
                valid_transitions.append(transition)
                
        return valid_transitions
        
    def can_transition_to(self, entity, current_state: str, target_state: str) -> bool:
        """Check if entity can transition to target state"""
        valid_transitions = self.get_valid_transitions(entity, current_state)
        return any(t.to_state == target_state for t in valid_transitions)
        
    def transition_to(self, entity, target_state: str, context: TransitionContext) -> Tuple[StateTransitionResult, str]:
        """
        Attempt to transition entity to target state
        Returns (result, message)
        """
        current_state = self._get_current_state(entity)
        
        try:
            # Find the transition
            transition = self._find_transition(current_state, target_state)
            if not transition:
                return StateTransitionResult.INVALID, f"No transition from {current_state} to {target_state}"
            
            # Check if transition is allowed
            if not context.force and not self._can_transition(entity, transition):
                return StateTransitionResult.BLOCKED, "Transition conditions not met"
            
            # Run global validators
            for validator in self.global_validators:
                try:
                    validator(entity, current_state, target_state, context)
                except Exception as e:
                    return StateTransitionResult.ERROR, f"Global validation failed: {str(e)}"
            
            # Run state-specific validators
            if target_state in self.state_validators:
                for validator in self.state_validators[target_state]:
                    try:
                        validator(entity, context)
                    except Exception as e:
                        return StateTransitionResult.ERROR, f"State validation failed: {str(e)}"
            
            # Check if approval is required
            if transition.requires_approval and not context.force:
                return self._handle_approval_required(entity, transition, context)
            
            # Execute the transition
            return self._execute_transition(entity, transition, context)
            
        except Exception as e:
            logger.error(f"State transition error: {e}", exc_info=True)
            return StateTransitionResult.ERROR, f"Transition failed: {str(e)}"
    
    def auto_progress(self, entity) -> List[Tuple[StateTransitionResult, str]]:
        """
        Automatically progress through states where auto_trigger is enabled
        """
        results = []
        current_state = self._get_current_state(entity)
        max_iterations = 10  # Prevent infinite loops
        
        for _ in range(max_iterations):
            auto_transitions = [
                t for t in self.get_valid_transitions(entity, current_state)
                if t.auto_trigger
            ]
            
            if not auto_transitions:
                break
                
            # Sort by priority and take the highest priority transition
            auto_transitions.sort(key=lambda t: t.priority, reverse=True)
            transition = auto_transitions[0]
            
            context = TransitionContext(
                user=None,
                reason="Auto-progression",
                auto_create_tasks=True,
                notify_stakeholders=True
            )
            
            result, message = self.transition_to(entity, transition.to_state, context)
            results.append((result, message))
            
            if result != StateTransitionResult.SUCCESS:
                break
                
            current_state = transition.to_state
            
        return results
    
    def _get_current_state(self, entity) -> str:
        """Get the current state of the entity"""
        if hasattr(entity, 'status'):
            return entity.status
        elif hasattr(entity, 'state'):
            return entity.state
        else:
            raise WorkflowError("Entity does not have a status or state field")
    
    def _set_current_state(self, entity, state: str):
        """Set the current state of the entity"""
        if hasattr(entity, 'status'):
            entity.status = state
        elif hasattr(entity, 'state'):
            entity.state = state
        else:
            raise WorkflowError("Entity does not have a status or state field")
    
    def _find_transition(self, from_state: str, to_state: str) -> Optional[StateTransition]:
        """Find a transition between two states"""
        if from_state not in self.transitions:
            return None
            
        for transition in self.transitions[from_state]:
            if transition.to_state == to_state:
                return transition
                
        return None
    
    def _can_transition(self, entity, transition: StateTransition) -> bool:
        """Check if a transition can be executed"""
        if transition.condition:
            try:
                return transition.condition(entity)
            except Exception as e:
                logger.warning(f"Transition condition check failed: {e}")
                return False
        return True
    
    def _handle_approval_required(self, entity, transition: StateTransition, context: TransitionContext) -> Tuple[StateTransitionResult, str]:
        """Handle transitions that require approval"""
        # Create approval request
        # This would integrate with a notification/approval system
        logger.info(f"Approval required for transition to {transition.to_state}")
        return StateTransitionResult.PENDING_APPROVAL, "Transition requires approval"
    
    def _execute_transition(self, entity, transition: StateTransition, context: TransitionContext) -> Tuple[StateTransitionResult, str]:
        """Execute the actual state transition"""
        old_state = self._get_current_state(entity)
        
        try:
            with transaction.atomic():
                # Set new state
                self._set_current_state(entity, transition.to_state)
                
                # Execute transition action
                if transition.action:
                    transition.action(entity, context)
                
                # Execute state entry actions
                if transition.to_state in self.state_actions:
                    for action in self.state_actions[transition.to_state]:
                        action(entity, context)
                
                # Save the entity
                entity.save()
                
                # Log the transition
                self._log_transition(entity, old_state, transition.to_state, context)
                
            return StateTransitionResult.SUCCESS, f"Transitioned from {old_state} to {transition.to_state}"
            
        except Exception as e:
            logger.error(f"Transition execution failed: {e}", exc_info=True)
            return StateTransitionResult.ERROR, f"Execution failed: {str(e)}"
    
    def _log_transition(self, entity, from_state: str, to_state: str, context: TransitionContext):
        """Log the state transition"""
        logger.info(
            f"{self.entity_type} {entity.pk} transitioned from {from_state} to {to_state}",
            extra={
                'entity_type': self.entity_type,
                'entity_id': entity.pk,
                'from_state': from_state,
                'to_state': to_state,
                'user': context.user.id if context.user else None,
                'reason': context.reason,
                'metadata': context.metadata
            }
        )
