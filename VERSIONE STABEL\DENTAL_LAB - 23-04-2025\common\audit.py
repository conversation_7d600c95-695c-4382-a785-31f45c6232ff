"""
Financial audit and logging services
"""

import logging
from decimal import Decimal
from django.db import models, transaction
from django.utils import timezone
from django.contrib.auth import get_user_model
from typing import Dict, Any, Optional
from datetime import timedelta
import json

logger = logging.getLogger(__name__)
User = get_user_model()

class FinancialAuditLog(models.Model):
    """
    Model for storing financial audit logs
    """
    OPERATION_TYPES = [
        ('payment_created', 'Payment Created'),
        ('payment_updated', 'Payment Updated'),
        ('payment_deleted', 'Payment Deleted'),
        ('payment_allocated', 'Payment Allocated to Invoice'),
        ('allocation_reversed', 'Payment Allocation Reversed'),
        ('invoice_created', 'Invoice Created'),
        ('invoice_updated', 'Invoice Updated'),
        ('invoice_status_changed', 'Invoice Status Changed'),
        ('currency_conversion', 'Currency Conversion'),
        ('exchange_rate_updated', 'Exchange Rate Updated'),
        ('account_balance_adjusted', 'Account Balance Adjusted'),
        ('security_event', 'Security Event'),
        ('user_login', 'User Login'),
        ('user_logout', 'User Logout'),
        ('login_failed', 'Login Failed'),
        ('permission_denied', 'Permission Denied'),
        ('data_access', 'Data Access'),
        ('data_modification', 'Data Modification'),
    ]

    operation_type = models.CharField(max_length=50, choices=OPERATION_TYPES, db_index=True)
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="User who performed the operation"
    )
    timestamp = models.DateTimeField(default=timezone.now, db_index=True)

    # Object references
    object_type = models.CharField(max_length=50, help_text="Type of object affected")
    object_id = models.PositiveIntegerField(help_text="ID of the affected object")

    # Financial details
    amount = models.DecimalField(
        max_digits=12, decimal_places=2,
        null=True, blank=True,
        help_text="Amount involved in the operation"
    )
    currency_code = models.CharField(
        max_length=3, null=True, blank=True,
        help_text="Currency code for the amount"
    )

    # Additional data
    details = models.JSONField(
        default=dict,
        help_text="Additional details about the operation"
    )

    # Before/after values for updates
    old_values = models.JSONField(
        default=dict, blank=True,
        help_text="Values before the operation"
    )
    new_values = models.JSONField(
        default=dict, blank=True,
        help_text="Values after the operation"
    )

    # IP address and session info
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    session_key = models.CharField(max_length=40, null=True, blank=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = "Financial Audit Log"
        verbose_name_plural = "Financial Audit Logs"
        indexes = [
            models.Index(fields=['operation_type', 'timestamp']),
            models.Index(fields=['object_type', 'object_id']),
            models.Index(fields=['user', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.operation_type} - {self.object_type} #{self.object_id} at {self.timestamp}"

class FinancialAuditService:
    """
    Service for financial audit logging and compliance
    """

    @staticmethod
    def log_payment_operation(operation_type: str, payment, user: User = None,
                            request=None, **kwargs):
        """Log payment-related operations"""
        details = {
            'payment_method': payment.payment_method,
            'dentist_id': payment.dentist.id,
            'dentist_name': str(payment.dentist),
            'account_id': payment.account.id,
            'account_name': payment.account.name,
            'date': payment.date.isoformat(),
        }
        details.update(kwargs)

        FinancialAuditService._create_audit_log(
            operation_type=operation_type,
            user=user,
            object_type='Payment',
            object_id=payment.id,
            amount=payment.amount,
            currency_code=payment.currency.code,
            details=details,
            request=request
        )

    @staticmethod
    def log_invoice_operation(operation_type: str, invoice, user: User = None,
                            request=None, **kwargs):
        """Log invoice-related operations"""
        details = {
            'case_id': invoice.case.id if invoice.case else None,
            'case_number': invoice.case.case_number if invoice.case else None,
            'dentist_id': invoice.dentist.id,
            'dentist_name': str(invoice.dentist),
            'status': invoice.status,
            'due_date': invoice.due_date.isoformat() if invoice.due_date else None,
        }
        details.update(kwargs)

        FinancialAuditService._create_audit_log(
            operation_type=operation_type,
            user=user,
            object_type='Invoice',
            object_id=invoice.id,
            amount=invoice.total_amount,
            currency_code=invoice.currency.code,
            details=details,
            request=request
        )

    @staticmethod
    def log_payment_allocation(allocation, user: User = None, request=None, **kwargs):
        """Log payment allocation operations"""
        details = {
            'payment_id': allocation.payment.id,
            'invoice_id': allocation.invoice.id,
            'original_amount': str(allocation.original_amount) if allocation.original_amount else None,
            'exchange_rate': str(allocation.exchange_rate) if allocation.exchange_rate else None,
            'is_converted': allocation.is_converted,
        }
        details.update(kwargs)

        FinancialAuditService._create_audit_log(
            operation_type='payment_allocated',
            user=user,
            object_type='InvoicePayment',
            object_id=allocation.id,
            amount=allocation.amount,
            currency_code=allocation.invoice.currency.code,
            details=details,
            request=request
        )

    @staticmethod
    def log_currency_conversion(conversion_result: Dict[str, Any], user: User = None,
                              request=None):
        """Log currency conversion operations"""
        if conversion_result['success']:
            FinancialAuditService._create_audit_log(
                operation_type='currency_conversion',
                user=user,
                object_type='CurrencyConversion',
                object_id=0,  # No specific object
                amount=conversion_result['original_amount'],
                currency_code=conversion_result['from_currency'],
                details={
                    'to_currency': conversion_result['to_currency'],
                    'converted_amount': str(conversion_result['converted_amount']),
                    'exchange_rate': str(conversion_result['exchange_rate']),
                    'conversion_date': conversion_result['date'].isoformat(),
                },
                request=request
            )

    @staticmethod
    def log_account_balance_change(account, old_balance: Decimal, new_balance: Decimal,
                                 operation: str, user: User = None, request=None):
        """Log account balance changes"""
        change_amount = new_balance - old_balance

        FinancialAuditService._create_audit_log(
            operation_type='account_balance_adjusted',
            user=user,
            object_type='Account',
            object_id=account.id,
            amount=abs(change_amount),
            currency_code=account.currency.code,
            details={
                'account_name': account.name,
                'operation': operation,
                'change_amount': str(change_amount),
            },
            old_values={'balance': str(old_balance)},
            new_values={'balance': str(new_balance)},
            request=request
        )

    @staticmethod
    def _create_audit_log(operation_type: str, user: User = None, object_type: str = None,
                         object_id: int = None, amount: Decimal = None,
                         currency_code: str = None, details: Dict = None,
                         old_values: Dict = None, new_values: Dict = None,
                         request=None):
        """Create audit log entry"""
        try:
            # Extract request information
            ip_address = None
            session_key = None

            if request:
                ip_address = FinancialAuditService._get_client_ip(request)
                session_key = request.session.session_key

            # Create audit log
            FinancialAuditLog.objects.create(
                operation_type=operation_type,
                user=user,
                object_type=object_type or '',
                object_id=object_id or 0,
                amount=amount,
                currency_code=currency_code,
                details=details or {},
                old_values=old_values or {},
                new_values=new_values or {},
                ip_address=ip_address,
                session_key=session_key
            )

        except Exception as e:
            logger.error(f"Failed to create audit log: {e}")

    @staticmethod
    def _get_client_ip(request):
        """Extract client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    @staticmethod
    def get_audit_trail(object_type: str = None, object_id: int = None,
                       user: User = None, operation_type: str = None,
                       start_date=None, end_date=None, limit: int = 100):
        """Get audit trail with filters"""
        queryset = FinancialAuditLog.objects.all()

        if object_type:
            queryset = queryset.filter(object_type=object_type)

        if object_id:
            queryset = queryset.filter(object_id=object_id)

        if user:
            queryset = queryset.filter(user=user)

        if operation_type:
            queryset = queryset.filter(operation_type=operation_type)

        if start_date:
            queryset = queryset.filter(timestamp__gte=start_date)

        if end_date:
            queryset = queryset.filter(timestamp__lte=end_date)

        return queryset.order_by('-timestamp')[:limit]

    @staticmethod
    def log_security_event(event_type: str, user: User = None, request=None, **kwargs):
        """Log security-related events"""
        details = {
            'event_type': event_type,
            'user_agent': request.META.get('HTTP_USER_AGENT', '') if request else '',
        }
        details.update(kwargs)

        FinancialAuditService._create_audit_log(
            operation_type='security_event',
            user=user,
            object_type='SecurityEvent',
            object_id=0,
            details=details,
            request=request
        )

    @staticmethod
    def log_permission_denied(user: User, resource: str, action: str, request=None):
        """Log permission denied events"""
        FinancialAuditService._create_audit_log(
            operation_type='permission_denied',
            user=user,
            object_type='PermissionDenied',
            object_id=0,
            details={
                'resource': resource,
                'action': action,
                'user_groups': [g.name for g in user.groups.all()] if user else []
            },
            request=request
        )

    @staticmethod
    def log_data_access(user: User, object_type: str, object_id: int,
                       action: str = 'view', request=None):
        """Log data access events"""
        FinancialAuditService._create_audit_log(
            operation_type='data_access',
            user=user,
            object_type=object_type,
            object_id=object_id,
            details={
                'action': action,
                'timestamp': timezone.now().isoformat()
            },
            request=request
        )

    @staticmethod
    def log_data_modification(user: User, object_type: str, object_id: int,
                            action: str, old_values: Dict = None,
                            new_values: Dict = None, request=None):
        """Log data modification events"""
        FinancialAuditService._create_audit_log(
            operation_type='data_modification',
            user=user,
            object_type=object_type,
            object_id=object_id,
            details={
                'action': action,
                'timestamp': timezone.now().isoformat()
            },
            old_values=old_values or {},
            new_values=new_values or {},
            request=request
        )

    @staticmethod
    def generate_compliance_report(start_date, end_date) -> Dict[str, Any]:
        """Generate compliance report for a date range"""
        logs = FinancialAuditLog.objects.filter(
            timestamp__range=[start_date, end_date]
        )

        # Group by operation type
        operation_counts = {}
        total_amounts = {}

        for log in logs:
            op_type = log.operation_type
            operation_counts[op_type] = operation_counts.get(op_type, 0) + 1

            if log.amount and log.currency_code:
                currency_key = f"{op_type}_{log.currency_code}"
                if currency_key not in total_amounts:
                    total_amounts[currency_key] = Decimal('0.00')
                total_amounts[currency_key] += log.amount

        # User activity
        user_activity = logs.values('user__username').annotate(
            count=models.Count('id')
        ).order_by('-count')

        # Security events
        security_events = logs.filter(operation_type='security_event').count()
        failed_logins = logs.filter(operation_type='login_failed').count()
        permission_denials = logs.filter(operation_type='permission_denied').count()

        return {
            'period': {
                'start_date': start_date,
                'end_date': end_date
            },
            'total_operations': logs.count(),
            'operation_counts': operation_counts,
            'total_amounts': {k: str(v) for k, v in total_amounts.items()},
            'user_activity': list(user_activity),
            'security_summary': {
                'security_events': security_events,
                'failed_logins': failed_logins,
                'permission_denials': permission_denials
            },
            'generated_at': timezone.now()
        }

    @staticmethod
    def get_user_activity_summary(user: User, days: int = 30) -> Dict[str, Any]:
        """Get activity summary for a specific user"""
        start_date = timezone.now() - timedelta(days=days)

        logs = FinancialAuditLog.objects.filter(
            user=user,
            timestamp__gte=start_date
        )

        # Group by operation type
        activity_by_type = {}
        for log in logs:
            op_type = log.operation_type
            if op_type not in activity_by_type:
                activity_by_type[op_type] = []
            activity_by_type[op_type].append({
                'timestamp': log.timestamp,
                'object_type': log.object_type,
                'object_id': log.object_id
            })

        # Recent activity (last 10 actions)
        recent_activity = logs.order_by('-timestamp')[:10]

        return {
            'user': user.get_full_name(),
            'period_days': days,
            'total_actions': logs.count(),
            'activity_by_type': activity_by_type,
            'recent_activity': [
                {
                    'operation': log.operation_type,
                    'timestamp': log.timestamp,
                    'object': f"{log.object_type} #{log.object_id}"
                }
                for log in recent_activity
            ]
        }
