"""
Resource Management Service for Real-time Availability and Optimization
"""

from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging
from django.utils import timezone
from django.db import transaction
from django.db.models import Q, Count, Sum, Avg
from django.contrib.auth import get_user_model

from .exceptions import ResourceNotFoundError, SchedulingError
from .scheduling_engine import SchedulingConstraint, ResourceAllocation

logger = logging.getLogger(__name__)
User = get_user_model()


@dataclass
class ResourceAvailability:
    """Represents resource availability information"""
    resource_id: int
    resource_type: str  # 'user', 'equipment', 'department'
    available_from: datetime
    available_until: datetime
    capacity_percentage: float
    current_workload: float
    skills: List[str] = None
    conflicts: List[str] = None


@dataclass
class ResourceConflict:
    """Represents a resource scheduling conflict"""
    resource_id: int
    conflict_type: str  # 'overbooked', 'unavailable', 'skill_mismatch'
    start_time: datetime
    end_time: datetime
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    suggested_resolution: str = ""


class ResourceManagementService:
    """
    Service for managing resources and real-time availability
    """
    
    @staticmethod
    def check_real_time_availability(resource_type='user', department_id=None, 
                                   start_time=None, end_time=None) -> List[ResourceAvailability]:
        """
        Check real-time availability of resources
        
        Args:
            resource_type: Type of resource to check ('user', 'equipment', 'department')
            department_id: Filter by department
            start_time: Start of availability window
            end_time: End of availability window
            
        Returns:
            List of ResourceAvailability objects
        """
        try:
            if start_time is None:
                start_time = timezone.now()
            if end_time is None:
                end_time = start_time + timedelta(hours=8)  # Default 8-hour window
            
            availabilities = []
            
            if resource_type == 'user':
                availabilities = ResourceManagementService._check_user_availability(
                    department_id, start_time, end_time
                )
            elif resource_type == 'department':
                availabilities = ResourceManagementService._check_department_availability(
                    department_id, start_time, end_time
                )
            elif resource_type == 'equipment':
                availabilities = ResourceManagementService._check_equipment_availability(
                    department_id, start_time, end_time
                )
            
            return availabilities
            
        except Exception as e:
            logger.error(f"Error checking resource availability: {e}", exc_info=True)
            return []
    
    @staticmethod
    def _check_user_availability(department_id, start_time, end_time) -> List[ResourceAvailability]:
        """Check user availability"""
        availabilities = []
        
        try:
            # Get users in department or all active users
            if department_id:
                from case.models import Department
                department = Department.objects.get(id=department_id)
                users = User.objects.filter(departments=department, is_active=True)
            else:
                users = User.objects.filter(is_active=True)
            
            for user in users:
                availability = ResourceManagementService._calculate_user_availability(
                    user, start_time, end_time
                )
                if availability:
                    availabilities.append(availability)
            
        except Exception as e:
            logger.error(f"Error checking user availability: {e}")
        
        return availabilities
    
    @staticmethod
    def _calculate_user_availability(user, start_time, end_time) -> Optional[ResourceAvailability]:
        """Calculate availability for a specific user"""
        try:
            # Check scheduled tasks
            scheduled_hours = 0
            conflicts = []
            
            try:
                from scheduling.models import ScheduleItem
                
                user_items = ScheduleItem.objects.filter(
                    assigned_to=user,
                    start_time__lt=end_time,
                    end_time__gt=start_time,
                    status__in=['pending', 'in_progress']
                )
                
                scheduled_hours = sum(
                    (item.end_time - item.start_time).total_seconds() / 3600
                    for item in user_items
                )
                
                if user_items.exists():
                    conflicts.append(f"{user_items.count()} scheduled tasks")
                    
            except ImportError:
                # Scheduling models not available
                pass
            
            # Check resource availability records
            try:
                from scheduling.models import ResourceAvailability as AvailabilityRecord
                
                unavailable_periods = AvailabilityRecord.objects.filter(
                    user=user,
                    date__gte=start_time.date(),
                    date__lte=end_time.date(),
                    is_available=False
                )
                
                if unavailable_periods.exists():
                    conflicts.append(f"{unavailable_periods.count()} unavailable periods")
                    
            except ImportError:
                pass
            
            # Calculate capacity and workload
            total_hours = (end_time - start_time).total_seconds() / 3600
            workload_percentage = (scheduled_hours / total_hours * 100) if total_hours > 0 else 0
            capacity_percentage = max(0, 100 - workload_percentage)
            
            # Get user skills
            user_skills = []
            if hasattr(user, 'skills'):
                user_skills = getattr(user, 'skills', [])
            elif hasattr(user, 'profile') and hasattr(user.profile, 'skills'):
                user_skills = getattr(user.profile, 'skills', [])
            
            return ResourceAvailability(
                resource_id=user.id,
                resource_type='user',
                available_from=start_time,
                available_until=end_time,
                capacity_percentage=capacity_percentage,
                current_workload=workload_percentage,
                skills=user_skills,
                conflicts=conflicts
            )
            
        except Exception as e:
            logger.error(f"Error calculating user availability for {user}: {e}")
            return None
    
    @staticmethod
    def _check_department_availability(department_id, start_time, end_time) -> List[ResourceAvailability]:
        """Check department availability"""
        availabilities = []
        
        try:
            from case.models import Department
            
            if department_id:
                departments = [Department.objects.get(id=department_id)]
            else:
                departments = Department.objects.filter(is_active=True)
            
            for department in departments:
                availability = ResourceManagementService._calculate_department_availability(
                    department, start_time, end_time
                )
                if availability:
                    availabilities.append(availability)
                    
        except Exception as e:
            logger.error(f"Error checking department availability: {e}")
        
        return availabilities
    
    @staticmethod
    def _calculate_department_availability(department, start_time, end_time) -> Optional[ResourceAvailability]:
        """Calculate availability for a specific department"""
        try:
            # Calculate department utilization
            total_hours = (end_time - start_time).total_seconds() / 3600
            department_capacity = getattr(department, 'capacity', 1)
            available_capacity_hours = total_hours * department_capacity
            
            # Check scheduled items
            scheduled_hours = 0
            conflicts = []
            
            try:
                from scheduling.models import ScheduleItem
                
                dept_items = ScheduleItem.objects.filter(
                    department=department,
                    start_time__lt=end_time,
                    end_time__gt=start_time,
                    status__in=['pending', 'in_progress']
                )
                
                scheduled_hours = sum(
                    (item.end_time - item.start_time).total_seconds() / 3600
                    for item in dept_items
                )
                
                if scheduled_hours > available_capacity_hours:
                    conflicts.append("Department over capacity")
                    
            except ImportError:
                pass
            
            # Calculate utilization
            utilization = (scheduled_hours / available_capacity_hours * 100) if available_capacity_hours > 0 else 0
            capacity_percentage = max(0, 100 - utilization)
            
            return ResourceAvailability(
                resource_id=department.id,
                resource_type='department',
                available_from=start_time,
                available_until=end_time,
                capacity_percentage=capacity_percentage,
                current_workload=utilization,
                skills=[],
                conflicts=conflicts
            )
            
        except Exception as e:
            logger.error(f"Error calculating department availability: {e}")
            return None
    
    @staticmethod
    def _check_equipment_availability(department_id, start_time, end_time) -> List[ResourceAvailability]:
        """Check equipment availability (placeholder for future implementation)"""
        # This would integrate with an equipment management system
        return []
    
    @staticmethod
    def detect_resource_conflicts(start_date, end_date) -> List[ResourceConflict]:
        """
        Detect resource conflicts in the given time period
        
        Returns:
            List of ResourceConflict objects
        """
        conflicts = []
        
        try:
            # Check user conflicts
            user_conflicts = ResourceManagementService._detect_user_conflicts(start_date, end_date)
            conflicts.extend(user_conflicts)
            
            # Check department conflicts
            dept_conflicts = ResourceManagementService._detect_department_conflicts(start_date, end_date)
            conflicts.extend(dept_conflicts)
            
            # Sort by severity
            severity_order = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3}
            conflicts.sort(key=lambda x: severity_order.get(x.severity, 4))
            
        except Exception as e:
            logger.error(f"Error detecting resource conflicts: {e}", exc_info=True)
        
        return conflicts
    
    @staticmethod
    def _detect_user_conflicts(start_date, end_date) -> List[ResourceConflict]:
        """Detect user scheduling conflicts"""
        conflicts = []
        
        try:
            from scheduling.models import ScheduleItem
            
            # Find users with overlapping schedule items
            users = User.objects.filter(
                scheduleitem__start_time__date__gte=start_date,
                scheduleitem__start_time__date__lte=end_date,
                is_active=True
            ).distinct()
            
            for user in users:
                user_items = ScheduleItem.objects.filter(
                    assigned_to=user,
                    start_time__date__gte=start_date,
                    start_time__date__lte=end_date,
                    status__in=['pending', 'in_progress']
                ).order_by('start_time')
                
                # Check for overlapping items
                for i, item1 in enumerate(user_items):
                    for item2 in user_items[i+1:]:
                        if (item1.start_time < item2.end_time and 
                            item1.end_time > item2.start_time):
                            
                            conflicts.append(ResourceConflict(
                                resource_id=user.id,
                                conflict_type='overbooked',
                                start_time=max(item1.start_time, item2.start_time),
                                end_time=min(item1.end_time, item2.end_time),
                                severity='high',
                                description=f"User {user.get_full_name()} has overlapping tasks",
                                suggested_resolution="Reschedule one of the conflicting tasks"
                            ))
                
                # Check for excessive workload
                daily_hours = {}
                for item in user_items:
                    date = item.start_time.date()
                    hours = (item.end_time - item.start_time).total_seconds() / 3600
                    daily_hours[date] = daily_hours.get(date, 0) + hours
                
                for date, hours in daily_hours.items():
                    if hours > 10:  # More than 10 hours per day
                        conflicts.append(ResourceConflict(
                            resource_id=user.id,
                            conflict_type='overbooked',
                            start_time=datetime.combine(date, datetime.min.time()),
                            end_time=datetime.combine(date, datetime.max.time()),
                            severity='medium',
                            description=f"User {user.get_full_name()} scheduled for {hours:.1f} hours on {date}",
                            suggested_resolution="Redistribute workload or schedule overtime"
                        ))
                        
        except ImportError:
            # Scheduling models not available
            pass
        except Exception as e:
            logger.error(f"Error detecting user conflicts: {e}")
        
        return conflicts
    
    @staticmethod
    def _detect_department_conflicts(start_date, end_date) -> List[ResourceConflict]:
        """Detect department capacity conflicts"""
        conflicts = []
        
        try:
            from case.models import Department
            from scheduling.models import ScheduleItem
            
            departments = Department.objects.filter(is_active=True)
            
            for department in departments:
                dept_items = ScheduleItem.objects.filter(
                    department=department,
                    start_time__date__gte=start_date,
                    start_time__date__lte=end_date,
                    status__in=['pending', 'in_progress']
                )
                
                # Check daily capacity
                daily_hours = {}
                for item in dept_items:
                    date = item.start_time.date()
                    hours = (item.end_time - item.start_time).total_seconds() / 3600
                    daily_hours[date] = daily_hours.get(date, 0) + hours
                
                department_capacity = getattr(department, 'capacity', 1)
                daily_capacity = department_capacity * 8  # 8 hours per day
                
                for date, hours in daily_hours.items():
                    if hours > daily_capacity:
                        over_capacity = hours - daily_capacity
                        severity = 'critical' if over_capacity > daily_capacity * 0.5 else 'high'
                        
                        conflicts.append(ResourceConflict(
                            resource_id=department.id,
                            conflict_type='overbooked',
                            start_time=datetime.combine(date, datetime.min.time()),
                            end_time=datetime.combine(date, datetime.max.time()),
                            severity=severity,
                            description=f"Department {department.name} over capacity by {over_capacity:.1f} hours on {date}",
                            suggested_resolution="Reschedule tasks or increase department capacity"
                        ))
                        
        except ImportError:
            pass
        except Exception as e:
            logger.error(f"Error detecting department conflicts: {e}")
        
        return conflicts
    
    @staticmethod
    def optimize_resource_allocation(schedule_items, optimization_goal='balanced') -> Dict[str, Any]:
        """
        Optimize resource allocation for a set of schedule items
        
        Args:
            schedule_items: List of schedule items to optimize
            optimization_goal: 'speed', 'quality', 'balanced', 'cost'
            
        Returns:
            Dictionary with optimization results
        """
        try:
            optimization_results = {
                'original_allocation': [],
                'optimized_allocation': [],
                'improvements': {},
                'conflicts_resolved': 0,
                'optimization_score': 0.0
            }
            
            # Analyze current allocation
            current_conflicts = ResourceManagementService._analyze_allocation_conflicts(schedule_items)
            optimization_results['original_allocation'] = current_conflicts
            
            # Apply optimization based on goal
            if optimization_goal == 'speed':
                optimized_items = ResourceManagementService._optimize_for_speed(schedule_items)
            elif optimization_goal == 'quality':
                optimized_items = ResourceManagementService._optimize_for_quality(schedule_items)
            elif optimization_goal == 'cost':
                optimized_items = ResourceManagementService._optimize_for_cost(schedule_items)
            else:  # balanced
                optimized_items = ResourceManagementService._optimize_balanced(schedule_items)
            
            # Analyze optimized allocation
            optimized_conflicts = ResourceManagementService._analyze_allocation_conflicts(optimized_items)
            optimization_results['optimized_allocation'] = optimized_conflicts
            
            # Calculate improvements
            conflicts_resolved = len(current_conflicts) - len(optimized_conflicts)
            optimization_results['conflicts_resolved'] = conflicts_resolved
            
            # Calculate optimization score
            if len(current_conflicts) > 0:
                improvement_ratio = conflicts_resolved / len(current_conflicts)
                optimization_results['optimization_score'] = improvement_ratio * 100
            else:
                optimization_results['optimization_score'] = 100.0
            
            optimization_results['improvements'] = {
                'conflicts_before': len(current_conflicts),
                'conflicts_after': len(optimized_conflicts),
                'improvement_percentage': optimization_results['optimization_score']
            }
            
            return optimization_results
            
        except Exception as e:
            logger.error(f"Error optimizing resource allocation: {e}", exc_info=True)
            return {'error': str(e)}
    
    @staticmethod
    def _analyze_allocation_conflicts(schedule_items) -> List[str]:
        """Analyze conflicts in current allocation"""
        conflicts = []
        
        # Group items by resource
        resource_items = {}
        for item in schedule_items:
            if hasattr(item, 'assigned_to') and item.assigned_to:
                resource_id = item.assigned_to.id
                if resource_id not in resource_items:
                    resource_items[resource_id] = []
                resource_items[resource_id].append(item)
        
        # Check for overlaps
        for resource_id, items in resource_items.items():
            sorted_items = sorted(items, key=lambda x: x.start_time)
            
            for i in range(len(sorted_items) - 1):
                current = sorted_items[i]
                next_item = sorted_items[i + 1]
                
                if current.end_time > next_item.start_time:
                    conflicts.append(f"Resource {resource_id} has overlapping tasks")
        
        return conflicts
    
    @staticmethod
    def _optimize_for_speed(schedule_items):
        """Optimize allocation for speed"""
        # Prioritize parallel execution and minimal setup times
        return schedule_items  # Placeholder implementation
    
    @staticmethod
    def _optimize_for_quality(schedule_items):
        """Optimize allocation for quality"""
        # Prioritize skill matching and adequate time allocation
        return schedule_items  # Placeholder implementation
    
    @staticmethod
    def _optimize_for_cost(schedule_items):
        """Optimize allocation for cost efficiency"""
        # Prioritize resource utilization and minimal overtime
        return schedule_items  # Placeholder implementation
    
    @staticmethod
    def _optimize_balanced(schedule_items):
        """Optimize allocation with balanced approach"""
        # Balance speed, quality, and cost considerations
        return schedule_items  # Placeholder implementation
    
    @staticmethod
    def get_resource_utilization_report(start_date, end_date) -> Dict[str, Any]:
        """
        Generate comprehensive resource utilization report
        """
        try:
            report = {
                'report_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'user_utilization': [],
                'department_utilization': [],
                'overall_metrics': {},
                'recommendations': []
            }
            
            # Analyze user utilization
            users = User.objects.filter(is_active=True)
            total_user_utilization = 0
            
            for user in users:
                availability = ResourceManagementService._calculate_user_availability(
                    user, 
                    datetime.combine(start_date, datetime.min.time()),
                    datetime.combine(end_date, datetime.max.time())
                )
                
                if availability:
                    user_data = {
                        'user_id': user.id,
                        'user_name': user.get_full_name(),
                        'utilization': availability.current_workload,
                        'capacity': availability.capacity_percentage,
                        'conflicts': len(availability.conflicts or [])
                    }
                    report['user_utilization'].append(user_data)
                    total_user_utilization += availability.current_workload
            
            # Analyze department utilization
            try:
                from case.models import Department
                
                departments = Department.objects.filter(is_active=True)
                total_dept_utilization = 0
                
                for department in departments:
                    availability = ResourceManagementService._calculate_department_availability(
                        department,
                        datetime.combine(start_date, datetime.min.time()),
                        datetime.combine(end_date, datetime.max.time())
                    )
                    
                    if availability:
                        dept_data = {
                            'department_id': department.id,
                            'department_name': department.name,
                            'utilization': availability.current_workload,
                            'capacity': availability.capacity_percentage,
                            'conflicts': len(availability.conflicts or [])
                        }
                        report['department_utilization'].append(dept_data)
                        total_dept_utilization += availability.current_workload
                        
            except ImportError:
                pass
            
            # Calculate overall metrics
            avg_user_utilization = total_user_utilization / len(users) if users else 0
            avg_dept_utilization = total_dept_utilization / len(departments) if departments else 0
            
            report['overall_metrics'] = {
                'average_user_utilization': round(avg_user_utilization, 2),
                'average_department_utilization': round(avg_dept_utilization, 2),
                'total_users_analyzed': len(users),
                'total_departments_analyzed': len(departments) if departments else 0
            }
            
            # Generate recommendations
            if avg_user_utilization > 85:
                report['recommendations'].append("High user utilization detected. Consider hiring additional staff.")
            elif avg_user_utilization < 50:
                report['recommendations'].append("Low user utilization. Consider workload redistribution.")
            
            if avg_dept_utilization > 90:
                report['recommendations'].append("Department capacity constraints detected. Plan for expansion.")
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating utilization report: {e}", exc_info=True)
            return {'error': str(e)}


# Global instance
resource_manager = ResourceManagementService()
