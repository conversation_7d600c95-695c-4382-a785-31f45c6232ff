"""
Dependency Management Service
Handles task dependencies and prerequisites
"""

from typing import Dict, List, Optional, Set, Tuple
from datetime import datetime, timedelta
import logging
from django.utils import timezone
from django.db import transaction
from django.db.models import Q

from .exceptions import WorkflowError, BusinessRuleViolation
from .status_synchronization import status_sync

logger = logging.getLogger(__name__)


class DependencyManagementService:
    """
    Service for managing task dependencies and prerequisites
    """
    
    @staticmethod
    def add_task_dependency(task, dependency_task, dependency_type='finish_to_start'):
        """
        Add a dependency between two tasks
        
        Args:
            task: The dependent task
            dependency_task: The task that must be completed first
            dependency_type: Type of dependency ('finish_to_start', 'start_to_start', etc.)
            
        Returns:
            (success, message) tuple
        """
        try:
            # Validate dependency
            validation_result = DependencyManagementService._validate_dependency(
                task, dependency_task, dependency_type
            )
            
            if not validation_result[0]:
                return validation_result
            
            # Add the dependency
            if hasattr(task, 'dependencies'):
                task.dependencies.add(dependency_task)
            
            # Update task status if needed
            if dependency_task.status != 'completed':
                task.status = 'blocked'
                task.blocking_issues = f"Waiting for task #{dependency_task.id} to complete"
                task.save(update_fields=['status', 'blocking_issues'])
            
            logger.info(f"Added dependency: Task #{task.id} depends on Task #{dependency_task.id}")
            
            return True, "Dependency added successfully"
            
        except Exception as e:
            logger.error(f"Error adding task dependency: {e}", exc_info=True)
            return False, f"Failed to add dependency: {str(e)}"
    
    @staticmethod
    def remove_task_dependency(task, dependency_task):
        """
        Remove a dependency between two tasks
        
        Args:
            task: The dependent task
            dependency_task: The dependency to remove
            
        Returns:
            (success, message) tuple
        """
        try:
            if hasattr(task, 'dependencies'):
                task.dependencies.remove(dependency_task)
            
            # Check if task can be unblocked
            remaining_deps = task.dependencies.exclude(status='completed')
            if not remaining_deps.exists() and task.status == 'blocked':
                task.status = 'pending'
                task.blocking_issues = ""
                task.save(update_fields=['status', 'blocking_issues'])
            
            logger.info(f"Removed dependency: Task #{task.id} no longer depends on Task #{dependency_task.id}")
            
            return True, "Dependency removed successfully"
            
        except Exception as e:
            logger.error(f"Error removing task dependency: {e}", exc_info=True)
            return False, f"Failed to remove dependency: {str(e)}"
    
    @staticmethod
    def _validate_dependency(task, dependency_task, dependency_type):
        """
        Validate a task dependency
        
        Returns:
            (is_valid, message) tuple
        """
        # Check for circular dependencies
        if DependencyManagementService._has_circular_dependency(task, dependency_task):
            return False, "Circular dependency detected"
        
        # Check if tasks are in the same case
        if hasattr(task, 'case') and hasattr(dependency_task, 'case'):
            if task.case != dependency_task.case:
                return False, "Dependencies can only be created between tasks in the same case"
        
        # Check if dependency already exists
        if hasattr(task, 'dependencies') and dependency_task in task.dependencies.all():
            return False, "Dependency already exists"
        
        # Check if task is trying to depend on itself
        if task.id == dependency_task.id:
            return False, "Task cannot depend on itself"
        
        return True, "Dependency is valid"
    
    @staticmethod
    def _has_circular_dependency(task, dependency_task, visited=None):
        """
        Check for circular dependencies using depth-first search
        
        Args:
            task: The task to check
            dependency_task: The potential dependency
            visited: Set of visited task IDs
            
        Returns:
            True if circular dependency exists
        """
        if visited is None:
            visited = set()
        
        if dependency_task.id in visited:
            return True
        
        visited.add(dependency_task.id)
        
        # Check dependencies of the dependency_task
        if hasattr(dependency_task, 'dependencies'):
            for dep in dependency_task.dependencies.all():
                if dep.id == task.id:
                    return True
                if DependencyManagementService._has_circular_dependency(task, dep, visited.copy()):
                    return True
        
        return False
    
    @staticmethod
    def get_task_dependency_chain(task):
        """
        Get the complete dependency chain for a task
        
        Args:
            task: The task to analyze
            
        Returns:
            Dictionary with dependency information
        """
        try:
            result = {
                'task_id': task.id,
                'direct_dependencies': [],
                'all_dependencies': [],
                'dependent_tasks': [],
                'is_blocked': task.status == 'blocked',
                'blocking_issues': getattr(task, 'blocking_issues', ''),
                'can_start': True
            }
            
            # Get direct dependencies
            if hasattr(task, 'dependencies'):
                direct_deps = task.dependencies.all()
                result['direct_dependencies'] = [
                    {
                        'id': dep.id,
                        'title': getattr(dep, 'title', f'Task {dep.id}'),
                        'status': dep.status,
                        'completed': dep.status == 'completed'
                    }
                    for dep in direct_deps
                ]
                
                # Check if task can start
                incomplete_deps = direct_deps.exclude(status='completed')
                result['can_start'] = not incomplete_deps.exists()
            
            # Get all dependencies (recursive)
            all_deps = DependencyManagementService._get_all_dependencies(task)
            result['all_dependencies'] = [
                {
                    'id': dep.id,
                    'title': getattr(dep, 'title', f'Task {dep.id}'),
                    'status': dep.status,
                    'level': level
                }
                for dep, level in all_deps
            ]
            
            # Get dependent tasks (tasks that depend on this task)
            if hasattr(task, 'dependent_tasks'):
                dependent_tasks = task.dependent_tasks.all()
                result['dependent_tasks'] = [
                    {
                        'id': dep_task.id,
                        'title': getattr(dep_task, 'title', f'Task {dep_task.id}'),
                        'status': dep_task.status,
                        'blocked_by_this': dep_task.status == 'blocked'
                    }
                    for dep_task in dependent_tasks
                ]
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting task dependency chain: {e}", exc_info=True)
            return {'error': str(e)}
    
    @staticmethod
    def _get_all_dependencies(task, level=0, visited=None):
        """
        Recursively get all dependencies for a task
        
        Returns:
            List of (task, level) tuples
        """
        if visited is None:
            visited = set()
        
        if task.id in visited:
            return []
        
        visited.add(task.id)
        dependencies = []
        
        if hasattr(task, 'dependencies'):
            for dep in task.dependencies.all():
                dependencies.append((dep, level))
                # Recursively get dependencies of dependencies
                sub_deps = DependencyManagementService._get_all_dependencies(dep, level + 1, visited.copy())
                dependencies.extend(sub_deps)
        
        return dependencies
    
    @staticmethod
    def update_dependent_tasks(completed_task):
        """
        Update tasks that depend on a completed task
        
        Args:
            completed_task: The task that was just completed
            
        Returns:
            List of updated tasks
        """
        updated_tasks = []
        
        try:
            # Find tasks that depend on this task
            if hasattr(completed_task, 'dependent_tasks'):
                dependent_tasks = completed_task.dependent_tasks.filter(status='blocked')
                
                for dep_task in dependent_tasks:
                    # Check if all dependencies are now complete
                    changed, new_status, message = status_sync.sync_task_dependencies(dep_task)
                    
                    if changed:
                        updated_tasks.append({
                            'task': dep_task,
                            'old_status': 'blocked',
                            'new_status': new_status,
                            'message': message
                        })
                        
                        logger.info(f"Task #{dep_task.id} unblocked after Task #{completed_task.id} completion")
            
        except Exception as e:
            logger.error(f"Error updating dependent tasks: {e}", exc_info=True)
        
        return updated_tasks
    
    @staticmethod
    def get_critical_path(case):
        """
        Calculate the critical path for a case's tasks
        
        Args:
            case: The case to analyze
            
        Returns:
            Dictionary with critical path information
        """
        try:
            tasks = case.tasks.all()
            if not tasks.exists():
                return {'critical_path': [], 'total_duration': 0, 'error': 'No tasks found'}
            
            # Build dependency graph
            task_graph = {}
            for task in tasks:
                task_graph[task.id] = {
                    'task': task,
                    'dependencies': list(task.dependencies.all()) if hasattr(task, 'dependencies') else [],
                    'duration': getattr(task, 'estimated_duration', timedelta(hours=1)).total_seconds() / 3600,
                    'earliest_start': 0,
                    'latest_start': 0,
                    'slack': 0
                }
            
            # Calculate earliest start times (forward pass)
            DependencyManagementService._calculate_earliest_start_times(task_graph)
            
            # Calculate latest start times (backward pass)
            DependencyManagementService._calculate_latest_start_times(task_graph)
            
            # Find critical path (tasks with zero slack)
            critical_tasks = [
                task_info['task'] for task_info in task_graph.values()
                if task_info['slack'] == 0
            ]
            
            total_duration = max(
                task_info['earliest_start'] + task_info['duration']
                for task_info in task_graph.values()
            )
            
            return {
                'critical_path': [
                    {
                        'id': task.id,
                        'title': getattr(task, 'title', f'Task {task.id}'),
                        'duration': task_graph[task.id]['duration'],
                        'earliest_start': task_graph[task.id]['earliest_start'],
                        'latest_start': task_graph[task.id]['latest_start'],
                        'slack': task_graph[task.id]['slack']
                    }
                    for task in critical_tasks
                ],
                'total_duration': total_duration,
                'all_tasks': [
                    {
                        'id': task_id,
                        'title': getattr(task_info['task'], 'title', f'Task {task_id}'),
                        'duration': task_info['duration'],
                        'earliest_start': task_info['earliest_start'],
                        'latest_start': task_info['latest_start'],
                        'slack': task_info['slack'],
                        'is_critical': task_info['slack'] == 0
                    }
                    for task_id, task_info in task_graph.items()
                ]
            }
            
        except Exception as e:
            logger.error(f"Error calculating critical path: {e}", exc_info=True)
            return {'error': str(e)}
    
    @staticmethod
    def _calculate_earliest_start_times(task_graph):
        """Calculate earliest start times for all tasks"""
        # Topological sort to process tasks in dependency order
        visited = set()
        
        def visit(task_id):
            if task_id in visited:
                return
            
            visited.add(task_id)
            task_info = task_graph[task_id]
            
            # Process dependencies first
            max_dependency_finish = 0
            for dep_task in task_info['dependencies']:
                if dep_task.id in task_graph:
                    visit(dep_task.id)
                    dep_info = task_graph[dep_task.id]
                    dep_finish = dep_info['earliest_start'] + dep_info['duration']
                    max_dependency_finish = max(max_dependency_finish, dep_finish)
            
            task_info['earliest_start'] = max_dependency_finish
        
        for task_id in task_graph:
            visit(task_id)
    
    @staticmethod
    def _calculate_latest_start_times(task_graph):
        """Calculate latest start times for all tasks"""
        # Find project end time
        project_end = max(
            task_info['earliest_start'] + task_info['duration']
            for task_info in task_graph.values()
        )
        
        # Initialize latest start times
        for task_info in task_graph.values():
            task_info['latest_start'] = project_end - task_info['duration']
        
        # Backward pass
        visited = set()
        
        def visit_backward(task_id):
            if task_id in visited:
                return
            
            visited.add(task_id)
            task_info = task_graph[task_id]
            
            # Find tasks that depend on this task
            min_successor_start = project_end
            for other_id, other_info in task_graph.items():
                if task_info['task'] in other_info['dependencies']:
                    visit_backward(other_id)
                    min_successor_start = min(min_successor_start, other_info['latest_start'])
            
            if min_successor_start < project_end:
                task_info['latest_start'] = min_successor_start - task_info['duration']
            
            # Calculate slack
            task_info['slack'] = task_info['latest_start'] - task_info['earliest_start']
        
        for task_id in task_graph:
            visit_backward(task_id)


# Global instance
dependency_manager = DependencyManagementService()
