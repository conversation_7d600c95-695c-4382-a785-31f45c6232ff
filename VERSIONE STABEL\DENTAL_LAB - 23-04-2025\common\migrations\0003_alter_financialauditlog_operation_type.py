# Generated by Django 5.2.1 on 2025-05-24 15:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('common', '0002_rename_common_fina_operati_b8e8a5_idx_common_fina_operati_9ad83f_idx_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='financialauditlog',
            name='operation_type',
            field=models.CharField(choices=[('payment_created', 'Payment Created'), ('payment_updated', 'Payment Updated'), ('payment_deleted', 'Payment Deleted'), ('payment_allocated', 'Payment Allocated to Invoice'), ('allocation_reversed', 'Payment Allocation Reversed'), ('invoice_created', 'Invoice Created'), ('invoice_updated', 'Invoice Updated'), ('invoice_status_changed', 'Invoice Status Changed'), ('currency_conversion', 'Currency Conversion'), ('exchange_rate_updated', 'Exchange Rate Updated'), ('account_balance_adjusted', 'Account Balance Adjusted'), ('security_event', 'Security Event'), ('user_login', 'User Login'), ('user_logout', 'User Logout'), ('login_failed', 'Login Failed'), ('permission_denied', 'Permission Denied'), ('data_access', 'Data Access'), ('data_modification', 'Data Modification')], db_index=True, max_length=50),
        ),
    ]
