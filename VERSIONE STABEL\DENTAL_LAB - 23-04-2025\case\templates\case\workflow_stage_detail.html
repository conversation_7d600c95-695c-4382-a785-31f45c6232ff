{% extends 'base.html' %}
{% load i18n %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
<style>
    :root {
        --primary-color: #4361ee;
        --primary-light: rgba(67, 97, 238, 0.1);
        --secondary-color: #3f37c9;
        --success-color: #4cc9f0;
        --info-color: #4895ef;
        --warning-color: #f72585;
        --danger-color: #e63946;
        --light-color: #f8f9fa;
        --dark-color: #212529;

        --border-radius: 0.375rem;
        --border-radius-lg: 0.5rem;
        --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* Page Layout */
    .page-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 1.5rem;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding: 1.5rem;
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
    }

    .page-title {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .page-title i {
        font-size: 1.5rem;
        color: var(--primary-color);
        background-color: var(--primary-light);
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    /* Stage Detail Card */
    .stage-detail-card {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .stage-detail-header {
        padding: 1.5rem;
        background-color: var(--primary-light);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        position: relative;
    }

    .stage-detail-title {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .stage-detail-title .stage-order {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        font-weight: 600;
        font-size: 1.25rem;
    }

    .stage-detail-body {
        padding: 1.5rem;
    }

    .stage-detail-section {
        margin-bottom: 2rem;
    }

    .stage-detail-section:last-child {
        margin-bottom: 0;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--dark-color);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: var(--primary-color);
    }

    /* Info List */
    .info-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .info-list li {
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
    }

    .info-list li:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: var(--dark-color);
        width: 180px;
        flex-shrink: 0;
    }

    .info-value {
        color: #6c757d;
    }

    /* Badges */
    .department-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.35em 0.65em;
        font-size: 0.875rem;
        font-weight: 600;
        border-radius: 50rem;
        background-color: var(--light-color);
        color: var(--dark-color);
    }

    .department-badge i {
        margin-right: 0.25rem;
    }

    .duration-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.35em 0.65em;
        font-size: 0.875rem;
        font-weight: 600;
        border-radius: 50rem;
        background-color: var(--info-color);
        color: white;
    }

    .duration-badge i {
        margin-right: 0.25rem;
    }

    .critical-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.35em 0.65em;
        font-size: 0.875rem;
        font-weight: 600;
        border-radius: 50rem;
        background-color: var(--warning-color);
        color: white;
    }

    .critical-badge i {
        margin-right: 0.25rem;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 0.75rem;
        margin-top: 1.5rem;
    }

    .action-buttons .btn {
        padding: 0.5rem 1rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        border-radius: var(--border-radius);
        transition: all 0.2s ease;
        font-weight: 500;
    }

    .action-buttons .btn i {
        font-size: 1rem;
    }

    .action-buttons .btn-edit {
        background-color: var(--warning-color);
        border-color: var(--warning-color);
        color: white;
    }

    .action-buttons .btn-delete {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
        color: white;
    }

    .action-buttons .btn-back {
        background-color: var(--light-color);
        border-color: var(--light-color);
        color: var(--dark-color);
    }

    /* Dependencies Section */
    .dependencies-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .dependencies-list li {
        padding: 0.5rem 0.75rem;
        margin-bottom: 0.5rem;
        background-color: var(--light-color);
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .dependency-order {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        font-weight: 600;
        font-size: 0.75rem;
    }

    .dependency-name {
        font-weight: 500;
        color: var(--dark-color);
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .page-container {
            padding: 1rem;
        }

        .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .info-list li {
            flex-direction: column;
        }

        .info-label {
            width: 100%;
            margin-bottom: 0.25rem;
        }

        .action-buttons {
            flex-direction: column;
        }

        .action-buttons .btn {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <!-- Page Header -->
    <div class="page-header animate__animated animate__fadeIn">
        <div>
            <h1 class="page-title">
                <i class="bi bi-diagram-3"></i> {% trans "Workflow Stage Detail" %}
            </h1>
            <p class="text-muted mb-0">{% trans "View detailed information about this workflow stage" %}</p>
        </div>
        <div>
            <a href="{% url 'case:workflow_stage_list' %}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> {% trans "Back to List" %}
            </a>
        </div>
    </div>

    <!-- Stage Detail Card -->
    <div class="stage-detail-card animate__animated animate__fadeIn" style="animation-delay: 0.1s">
        <div class="stage-detail-header">
            <h2 class="stage-detail-title">
                <span class="stage-order">{{ workflow_stage.order }}</span>
                {{ workflow_stage.name }}
                {% if workflow_stage.is_critical %}
                <span class="critical-badge">
                    <i class="bi bi-exclamation-triangle-fill"></i> {% trans "Critical Stage" %}
                </span>
                {% endif %}
            </h2>
        </div>
        <div class="stage-detail-body">
            <!-- Basic Information -->
            <div class="stage-detail-section">
                <h3 class="section-title">
                    <i class="bi bi-info-circle"></i> {% trans "Basic Information" %}
                </h3>
                <ul class="info-list">
                    <li>
                        <div class="info-label">{% trans "Department" %}</div>
                        <div class="info-value">
                            <span class="department-badge">
                                <i class="bi bi-building"></i> {{ workflow_stage.department }}
                            </span>
                        </div>
                    </li>
                    <li>
                        <div class="info-label">{% trans "Workflow Template" %}</div>
                        <div class="info-value">{{ workflow_stage.workflow.name }}</div>
                    </li>
                    <li>
                        <div class="info-label">{% trans "Order in Sequence" %}</div>
                        <div class="info-value">{{ workflow_stage.order }}</div>
                    </li>
                    <li>
                        <div class="info-label">{% trans "Estimated Duration" %}</div>
                        <div class="info-value">
                            <span class="duration-badge">
                                <i class="bi bi-clock"></i> {{ workflow_stage.estimated_duration }}
                            </span>
                        </div>
                    </li>
                    <li>
                        <div class="info-label">{% trans "Complexity Level" %}</div>
                        <div class="info-value">{{ workflow_stage.get_complexity_display }}</div>
                    </li>
                    <li>
                        <div class="info-label">{% trans "Critical Stage" %}</div>
                        <div class="info-value">
                            {% if workflow_stage.is_critical %}
                            <span class="text-danger"><i class="bi bi-check-circle-fill"></i> {% trans "Yes" %}</span>
                            {% else %}
                            <span class="text-muted">{% trans "No" %}</span>
                            {% endif %}
                        </div>
                    </li>
                </ul>
            </div>

            <!-- Description -->
            {% if workflow_stage.description %}
            <div class="stage-detail-section">
                <h3 class="section-title">
                    <i class="bi bi-card-text"></i> {% trans "Description" %}
                </h3>
                <div class="p-3 bg-light rounded">
                    {{ workflow_stage.description|linebreaks }}
                </div>
            </div>
            {% endif %}

            <!-- Dependencies -->
            {% if workflow_stage.dependencies.exists %}
            <div class="stage-detail-section">
                <h3 class="section-title">
                    <i class="bi bi-link-45deg"></i> {% trans "Dependencies" %}
                </h3>
                <ul class="dependencies-list">
                    {% for dependency in workflow_stage.dependencies.all %}
                    <li>
                        <span class="dependency-order">{{ dependency.order }}</span>
                        <span class="dependency-name">{{ dependency.name }}</span>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}

            <!-- Required Skills -->
            {% if workflow_stage.required_skills %}
            <div class="stage-detail-section">
                <h3 class="section-title">
                    <i class="bi bi-person-badge"></i> {% trans "Required Skills" %}
                </h3>
                <div class="p-3 bg-light rounded">
                    <pre class="mb-0">{{ workflow_stage.required_skills|pprint }}</pre>
                </div>
            </div>
            {% endif %}

            <!-- Required Equipment -->
            {% if workflow_stage.required_equipment %}
            <div class="stage-detail-section">
                <h3 class="section-title">
                    <i class="bi bi-tools"></i> {% trans "Required Equipment" %}
                </h3>
                <div class="p-3 bg-light rounded">
                    <pre class="mb-0">{{ workflow_stage.required_equipment|pprint }}</pre>
                </div>
            </div>
            {% endif %}

            <!-- Quality Checklist -->
            {% if workflow_stage.quality_checklist %}
            <div class="stage-detail-section">
                <h3 class="section-title">
                    <i class="bi bi-check2-square"></i> {% trans "Quality Checklist" %}
                </h3>
                <div class="p-3 bg-light rounded">
                    <pre class="mb-0">{{ workflow_stage.quality_checklist|pprint }}</pre>
                </div>
            </div>
            {% endif %}

            <!-- Additional Notes -->
            {% if workflow_stage.notes %}
            <div class="stage-detail-section">
                <h3 class="section-title">
                    <i class="bi bi-journal-text"></i> {% trans "Additional Notes" %}
                </h3>
                <div class="p-3 bg-light rounded">
                    {{ workflow_stage.notes|linebreaks }}
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="action-buttons">
                <a href="{% url 'case:workflow_stage_update' workflow_stage.pk %}" class="btn btn-edit">
                    <i class="bi bi-pencil"></i> {% trans "Edit Stage" %}
                </a>
                <a href="{% url 'case:workflow_stage_delete' workflow_stage.pk %}" class="btn btn-delete">
                    <i class="bi bi-trash"></i> {% trans "Delete Stage" %}
                </a>
                <a href="{% url 'case:workflow_stage_list' %}" class="btn btn-back">
                    <i class="bi bi-arrow-left"></i> {% trans "Back to List" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}