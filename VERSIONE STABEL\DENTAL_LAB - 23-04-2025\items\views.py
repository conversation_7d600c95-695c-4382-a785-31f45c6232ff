# items views.py

from django.contrib import messages
from django.core.paginator import Paginator
from django.db import IntegrityError
from django.db.models import Q
from django.forms import formset_factory, inlineformset_factory
from django.http import HttpResponse, JsonResponse
import csv
import logging
from datetime import datetime
from django.shortcuts import render, redirect, get_object_or_404
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST
from django.utils import timezone

logger = logging.getLogger(__name__)

from .forms import (
    CategoryForm, ItemForm, ItemRawMaterialForm, RawMaterialForm,
    SupplierForm, UnitForm
)
from .models import (
    Category, Currency, Inventory, Item, ItemRawMaterial,
    RawMaterial, RawMaterialInventory, Supplier, Unit
)
from .services import (
    MaterialRequirementPlanningService, StockAlertService, CostTrackingService
)

# Category views
def category_list(request):
    """
    Display a list of all categories.
    """
    # Get all categories
    categories = Category.objects.all().order_by('name')

    # Filter by search query if provided
    search_query = request.GET.get('search', '')
    if search_query:
        categories = categories.filter(name__icontains=search_query)

    # Filter by active status if provided
    show_inactive = request.GET.get('show_inactive') == 'on'
    if not show_inactive:
        categories = categories.filter(is_active=True)

    # Paginate the results
    paginator = Paginator(categories, 30)  # Show 30 categories per page
    page_number = request.GET.get('page')
    categories = paginator.get_page(page_number)

    context = {
        'categories': categories,
        'search_query': search_query,
        'show_inactive': show_inactive
    }
    return render(request, 'items/category_list.html', context)

def category_create(request):
    """
    Create a new category.
    """
    if request.method == 'POST':
        form = CategoryForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, _('Category created successfully.'))
            return redirect('items:category_list')
    else:
        form = CategoryForm()

    return render(request, 'items/category_form.html', {'form': form, 'title': _('Create Category')})

def category_edit(request, pk):
    """
    Edit an existing category.
    """
    category = get_object_or_404(Category, pk=pk)
    if request.method == 'POST':
        form = CategoryForm(request.POST, instance=category)
        if form.is_valid():
            form.save()
            messages.success(request, _('Category updated successfully.'))
            return redirect('items:category_list')
    else:
        form = CategoryForm(instance=category)

    return render(request, 'items/category_form.html', {
        'form': form,
        'title': _('Edit Category'),
        'category': category
    })

def category_delete(request, pk):
    """
    Delete a category.
    """
    category = get_object_or_404(Category, pk=pk)

    # Check if category has any items or raw materials
    has_items = category.items.exists()
    has_raw_materials = category.raw_materials.exists()

    if request.method == 'POST':
        if has_items or has_raw_materials:
            # Instead of deleting, just mark as inactive
            category.is_active = False
            category.save()
            messages.success(request, _('Category marked as inactive because it has associated items or raw materials.'))
        else:
            category.delete()
            messages.success(request, _('Category deleted successfully.'))
        return redirect('items:category_list')

    return render(request, 'items/category_confirm_delete.html', {
        'category': category,
        'has_items': has_items,
        'has_raw_materials': has_raw_materials
    })

# Item views
def item_list(request):
    """
    Display a list of all items with filtering and search capabilities.
    """
    # Start with all items with optimized queries
    items = Item.objects.select_related(
        'category', 'unit', 'currency'
    ).prefetch_related(
        'itemrawmaterial_set__raw_material',
        'itemrawmaterial_set__unit'
    ).order_by('name')

    # Filter by search query if provided
    search_query = request.GET.get('search', '')
    if search_query:
        items = items.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Filter by category if provided
    category_id = request.GET.get('category')
    if category_id and category_id != 'all':
        items = items.filter(category_id=category_id)

    # Filter by active status if provided
    show_inactive = request.GET.get('show_inactive') == 'on'
    if not show_inactive:
        items = items.filter(is_active=True)

    # Paginate the results
    paginator = Paginator(items, 30)  # Show 30 items per page
    page_number = request.GET.get('page')
    items = paginator.get_page(page_number)

    # Get all categories for the filter dropdown
    categories = Category.objects.filter(is_active=True).order_by('name')

    context = {
        'items': items,
        'categories': categories,
        'search_query': search_query,
        'selected_category': category_id,
        'show_inactive': show_inactive
    }
    return render(request, 'items/item_list.html', context)



def item_create(request):
    """
    Create a new item with associated raw materials.
    """
    # Create a formset for raw materials
    ItemRawMaterialFormSet = formset_factory(ItemRawMaterialForm, extra=1)

    if request.method == 'POST':
        form = ItemForm(request.POST)
        formset = ItemRawMaterialFormSet(request.POST, prefix='item_raw_materials')

        if form.is_valid() and formset.is_valid():
            item = form.save()

            # Save raw materials
            for rmf in formset:
                if rmf.has_changed():  # Only save raw materials with valid data
                    item_raw_material = rmf.save(commit=False)
                    item_raw_material.item = item
                    item_raw_material.save()

            # Calculate cost and profit after saving
            cost = item.cost()
            profit = item.profit()
            profit_percentage = item.profit_percentage()

            # Check if selling price is less than cost and add a warning
            if item.selling_price <= cost:
                messages.warning(
                    request,
                    _('Warning: The selling price (%(price)s) is less than or equal to the cost (%(cost)s). '
                      'This item will not generate profit.') % {
                        'price': item.selling_price,
                        'cost': cost
                    }
                )

            messages.success(
                request,
                _('Item "%(name)s" created successfully. Cost: %(cost)s, Profit: %(profit)s (%(percentage)s%%)') % {
                    'name': item.name,
                    'cost': cost,
                    'profit': profit,
                    'percentage': profit_percentage
                }
            )

            # If the user clicked 'Save and Add Another', redirect to the create form
            if 'save_and_add_another' in request.POST:
                return redirect('items:item_create')
            # Otherwise, redirect to the list view
            return redirect('items:item_list')
    else:
        form = ItemForm()
        formset = ItemRawMaterialFormSet(prefix='item_raw_materials')

    # Get all categories for the dropdown
    categories = Category.objects.filter(is_active=True).order_by('name')
    # Get all currencies for the dropdown
    currencies = Currency.objects.all().order_by('code')
    # Get all units for the dropdown
    units = Unit.objects.all().order_by('name')
    # Get active raw materials for the dropdown
    raw_materials = RawMaterial.objects.filter(is_active=True).order_by('name')

    context = {
        'form': form,
        'formset': formset,
        'categories': categories,
        'currencies': currencies,
        'units': units,
        'raw_materials': raw_materials,
        'title': _('Create Item')
    }
    return render(request, 'items/item_form.html', context)








from django.forms import formset_factory

from django.forms import inlineformset_factory

from django.forms import modelformset_factory
from django.core.paginator import Paginator

# def item_edit(request, pk):
#     item = get_object_or_404(Item, id=pk)
#     ItemRawMaterialFormSet = formset_factory(ItemRawMaterialForm, extra=0, can_delete=True)

#     if request.method == 'POST':
#         form = ItemForm(request.POST, instance=item)
#         raw_material_forms = ItemRawMaterialFormSet(request.POST, prefix='rm')

#         if form.is_valid() and raw_material_forms.is_valid():
#             item = form.save()

#             # Print existing raw materials before deletion
#             print("Existing Raw Materials (Before Deletion):")
#             for irm in item.itemrawmaterial_set.all():
#                 print(f"{irm.raw_material.name} - {irm.raw_material.description}: {irm.quantity} {irm.unit}")

#             item.itemrawmaterial_set.all().delete()

#             for rmf in raw_material_forms:
#                 if rmf.cleaned_data and not rmf.cleaned_data.get('DELETE'):
#                     item_raw_material = rmf.save(commit=False)
#                     item_raw_material.item = item
#                     item_raw_material.save()

#             # Print existing raw materials after saving
#             print("Existing Raw Materials (After Saving):")
#             for irm in item.itemrawmaterial_set.all():
#                 print(f"{irm.raw_material.name} - {irm.raw_material.description}: {irm.quantity} {irm.unit}")

#             return redirect('items:item_list')
#         else:
#             print("Form is invalid:", form.errors)
#             print("Formset is invalid:", raw_material_forms.errors)
#     else:
#         form = ItemForm(instance=item)
#         raw_material_forms = ItemRawMaterialFormSet(
#             initial=[
#                 {'raw_material': irm.raw_material, 'quantity': irm.quantity, 'unit': irm.unit}
#                 for irm in item.itemrawmaterial_set.all()
#             ],
#             prefix='rm'
#         )

#     return render(request, 'items/item_edit.html', {'form': form, 'formset': raw_material_forms, 'id': pk})
from django.forms import inlineformset_factory
from django.shortcuts import get_object_or_404, redirect, render
from .models import Item, ItemRawMaterial
from .forms import ItemForm, ItemRawMaterialForm

def item_edit(request, pk):
    """
    Edit an existing item and its associated raw materials.
    """
    item = get_object_or_404(Item, pk=pk)

    # Setup the inline formset for the item's raw materials
    ItemRawMaterialFormSet = inlineformset_factory(
        Item, ItemRawMaterial,
        form=ItemRawMaterialForm,
        fields=('raw_material', 'quantity', 'unit'),
        extra=1, can_delete=True
    )

    if request.method == 'POST':
        form = ItemForm(request.POST, instance=item)
        formset = ItemRawMaterialFormSet(request.POST, instance=item)

        if form.is_valid() and formset.is_valid():
            item = form.save()
            formset.save()

            # Calculate cost and profit after saving
            cost = item.cost()
            profit = item.profit()
            profit_percentage = item.profit_percentage()

            # Check if selling price is less than cost and add a warning
            if item.selling_price <= cost:
                messages.warning(
                    request,
                    _('Warning: The selling price (%(price)s) is less than or equal to the cost (%(cost)s). '
                      'This item will not generate profit.') % {
                        'price': item.selling_price,
                        'cost': cost
                    }
                )

            messages.success(
                request,
                _('Item "%(name)s" updated successfully. Cost: %(cost)s, Profit: %(profit)s (%(percentage)s%%)') % {
                    'name': item.name,
                    'cost': cost,
                    'profit': profit,
                    'percentage': profit_percentage
                }
            )
            return redirect('items:item_list')
    else:
        form = ItemForm(instance=item)
        formset = ItemRawMaterialFormSet(instance=item)

    # Get all categories for the dropdown
    categories = Category.objects.filter(is_active=True).order_by('name')
    # Get all currencies for the dropdown
    currencies = Currency.objects.all().order_by('code')
    # Get all units for the dropdown
    units = Unit.objects.all().order_by('name')
    # Get active raw materials for the dropdown
    raw_materials = RawMaterial.objects.filter(is_active=True).order_by('name')

    # Calculate cost and profit for display
    cost = item.cost()
    profit = item.profit()
    profit_percentage = item.profit_percentage()

    context = {
        'form': form,
        'formset': formset,
        'item': item,
        'categories': categories,
        'currencies': currencies,
        'units': units,
        'raw_materials': raw_materials,
        'cost': cost,
        'profit': profit,
        'profit_percentage': profit_percentage,
        'title': _('Edit Item')
    }
    return render(request, 'items/item_form.html', context)




# # def item_edit(request, pk):
#     item = get_object_or_404(Item, id=pk)

#     ItemRawMaterialFormSet = formset_factory(ItemRawMaterialForm, extra=0, can_delete=True)

#     if request.method == 'POST':
#         form = ItemForm(request.POST, instance=item)
#         raw_material_forms = ItemRawMaterialFormSet(request.POST, prefix='rm')

#         if form.is_valid() and raw_material_forms.is_valid():
#             item = form.save()
#             item.itemrawmaterial_set.all().delete()

#             for rmf in raw_material_forms:
#                 if rmf.cleaned_data and not rmf.cleaned_data.get('DELETE'):
#                     item_raw_material = rmf.save(commit=False)
#                     item_raw_material.item = item
#                     item_raw_material.save()

#             return redirect('items:item_list')
#     else:
#         form = ItemForm(instance=item)
#         raw_material_forms = ItemRawMaterialFormSet(initial=[{'raw_material': irm.raw_material, 'quantity': irm.quantity, 'unit': irm.unit} for irm in item.itemrawmaterial_set.all()], prefix='rm')


#     return render(request, 'items/item_edit.html', {'form': form, 'formset': raw_material_forms, 'id': pk})





def item_detail(request, pk):
    """
    Display detailed information about an item.
    """
    item = get_object_or_404(Item, pk=pk)

    # Get raw materials for this item
    raw_materials = item.itemrawmaterial_set.all().select_related('raw_material', 'unit')

    # Calculate cost and profit
    cost = item.cost()
    profit = item.profit()
    profit_percentage = item.profit_percentage()

    context = {
        'item': item,
        'raw_materials': raw_materials,
        'cost': cost,
        'profit': profit,
        'profit_percentage': profit_percentage
    }
    return render(request, 'items/item_detail.html', context)







def item_delete(request, pk):
    """
    Delete an item or mark it as inactive if it's used in other modules.
    """
    item = get_object_or_404(Item, id=pk)

    # Check if item is used in any cases or invoices
    # This would require checking related models in other apps
    # For now, we'll just check if it has any raw materials
    has_raw_materials = item.itemrawmaterial_set.exists()

    if request.method == 'POST':
        if has_raw_materials:
            # Instead of deleting, just mark as inactive
            item.is_active = False
            item.save()
            messages.success(request, _('Item "%(name)s" marked as inactive because it has associated raw materials.') % {'name': item.name})
        else:
            item.delete()
            messages.success(request, _('Item "%(name)s" deleted successfully.') % {'name': item.name})
        return redirect('items:item_list')

    return render(request, 'items/item_confirm_delete.html', {
        'item': item,
        'has_raw_materials': has_raw_materials
    })

# RawMaterial views
def raw_material_list(request):
    """
    Display a list of all raw materials with filtering and search capabilities.
    """
    # Start with all raw materials
    raw_materials = RawMaterial.objects.all().order_by('name')

    # Filter by search query if provided
    search_query = request.GET.get('search', '')
    if search_query:
        raw_materials = raw_materials.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Filter by category if provided
    category_id = request.GET.get('category')
    if category_id and category_id != 'all':
        raw_materials = raw_materials.filter(category_id=category_id)

    # Filter by active status if provided
    show_inactive = request.GET.get('show_inactive') == 'on'
    if not show_inactive:
        raw_materials = raw_materials.filter(is_active=True)

    # Filter by reorder status if provided
    show_reorder = request.GET.get('show_reorder') == 'on'
    if show_reorder:
        # Get raw materials that need reordering
        reorder_needed = []
        for rm in raw_materials:
            if rm.needs_reorder():
                reorder_needed.append(rm.id)
        raw_materials = raw_materials.filter(id__in=reorder_needed)

    # Paginate the results
    paginator = Paginator(raw_materials, 30)  # Show 30 raw materials per page
    page_number = request.GET.get('page')
    raw_materials = paginator.get_page(page_number)

    # Get all categories for the filter dropdown
    categories = Category.objects.filter(is_active=True).order_by('name')

    context = {
        'raw_materials': raw_materials,
        'categories': categories,
        'search_query': search_query,
        'selected_category': category_id,
        'show_inactive': show_inactive,
        'show_reorder': show_reorder
    }
    return render(request, 'items/raw_material_list.html', context)

def raw_material_create(request):
    """
    Create a new raw material.
    """
    if request.method == 'POST':
        form = RawMaterialForm(request.POST)
        if form.is_valid():
            raw_material = form.save()
            messages.success(request, _('Raw material "%(name)s" created successfully.') % {'name': raw_material.name})

            # If the user clicked 'Save and Add Another', redirect to the create form
            if 'save_and_add_another' in request.POST:
                return redirect('items:raw_material_create')
            # Otherwise, redirect to the list view
            return redirect('items:raw_material_list')
    else:
        form = RawMaterialForm()

    # Get all categories for the dropdown
    categories = Category.objects.filter(is_active=True).order_by('name')

    context = {
        'form': form,
        'categories': categories,
        'title': _('Create Raw Material')
    }
    return render(request, 'items/raw_material_form.html', context)

def raw_material_edit(request, pk):
    """
    Edit an existing raw material.
    """
    raw_material = get_object_or_404(RawMaterial, id=pk)
    if request.method == 'POST':
        form = RawMaterialForm(request.POST, instance=raw_material)
        if form.is_valid():
            raw_material = form.save()
            messages.success(request, _('Raw material "%(name)s" updated successfully.') % {'name': raw_material.name})
            return redirect('items:raw_material_list')
    else:
        form = RawMaterialForm(instance=raw_material)

    # Get all categories for the dropdown
    categories = Category.objects.filter(is_active=True).order_by('name')

    context = {
        'form': form,
        'raw_material': raw_material,
        'categories': categories,
        'title': _('Edit Raw Material')
    }
    return render(request, 'items/raw_material_form.html', context)

def raw_material_delete(request, pk):
    """
    Delete a raw material or mark it as inactive if it's used in items.
    """
    raw_material = get_object_or_404(RawMaterial, id=pk)

    # Check if raw material is used in any items
    is_used = ItemRawMaterial.objects.filter(raw_material=raw_material).exists()

    if request.method == 'POST':
        if is_used:
            # Instead of deleting, just mark as inactive
            raw_material.is_active = False
            raw_material.save()
            messages.success(request, _('Raw material "%(name)s" marked as inactive because it is used in items.') % {'name': raw_material.name})
        else:
            raw_material.delete()
            messages.success(request, _('Raw material "%(name)s" deleted successfully.') % {'name': raw_material.name})
        return redirect('items:raw_material_list')

    return render(request, 'items/raw_material_confirm_delete.html', {
        'raw_material': raw_material,
        'is_used': is_used
    })





# Unit views

def unit_list(request):
    units = Unit.objects.all()
    return render(request, 'items/unit_list.html', {'units': units})

def unit_create(request):
    if request.method == 'POST':
        form = UnitForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect('items:unit_list')
    else:
        form = UnitForm()
    return render(request, 'items/unit_form.html', {'form': form})

def unit_edit(request, unit_id):
    unit = get_object_or_404(Unit, id=unit_id)
    if request.method == 'POST':
        form = UnitForm(request.POST, instance=unit)
        if form.is_valid():
            form.save()
            return redirect('items:unit_list')
    else:
        form = UnitForm(instance=unit)
    return render(request, 'items/unit_form.html', {'form': form})

from django.db import IntegrityError
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from .models import Unit

def unit_delete(request, unit_id):
    unit = get_object_or_404(Unit, id=unit_id)
    if request.method == 'POST':
        try:
            unit.delete()
            messages.success(request, "Unit deleted successfully.")
            return redirect('items:unit_list')
        except IntegrityError:
            messages.error(request, "This unit cannot be deleted because it is referenced by other records.")
            return redirect('items:unit_list')
    return render(request, 'items/unit_confirm_delete.html', {'unit': unit})

# Supplier views

def supplier_list(request):
    # Show only active suppliers by default
    show_inactive = request.GET.get('show_inactive', False)
    suppliers = Supplier.objects.order_by('name')

    if not show_inactive:
        suppliers = suppliers.filter(is_active=True)

    paginator = Paginator(suppliers, 30)
    page_number = request.GET.get('page')
    suppliers = paginator.get_page(page_number)

    context = {
        'suppliers': suppliers,
        'show_inactive': show_inactive
    }
    return render(request, 'items/supplier_list.html', context)

def supplier_create(request):
    if request.method == 'POST':
        form = SupplierForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect('items:supplier_list')
    else:
        form = SupplierForm()
    return render(request, 'items/supplier_create.html', {'form': form})

def supplier_edit(request, pk):  # Changed 'supplier_id' to 'pk'
    supplier = get_object_or_404(Supplier, pk=pk)  # Use 'pk' here
    if request.method == 'POST':
        form = SupplierForm(request.POST, instance=supplier)
        if form.is_valid():
            form.save()
            return redirect('items:supplier_list')
    else:
        form = SupplierForm(instance=supplier)
    return render(request, 'items/supplier_edit.html', {'form': form})


# views.py
def supplier_delete(request, pk):
    supplier = get_object_or_404(Supplier, pk=pk)
    if request.method == 'POST':
        # Instead of deleting, just mark as inactive
        supplier.is_active = False
        supplier.save()
        messages.success(request, f'Supplier "{supplier.name}" has been deactivated.')
        return redirect('items:supplier_list')
    return render(request, 'items/supplier_delete.html', {'supplier': supplier})





from .models import Inventory, RawMaterialInventory

def item_inventory(request):
    """Display item inventory with filtering capabilities."""
    # Get all inventory items with related objects
    inventories = Inventory.objects.all().select_related('item', 'item__category', 'unit')

    # Filter by search query if provided
    search_query = request.GET.get('search', '')
    if search_query:
        inventories = inventories.filter(
            Q(item__name__icontains=search_query) |
            Q(item__category__name__icontains=search_query) |
            Q(location__icontains=search_query)
        )

    # Filter by location if provided
    location = request.GET.get('location', '')
    if location:
        inventories = inventories.filter(location=location)

    # Filter by stock status if provided
    stock_status = request.GET.get('stock_status', '')
    if stock_status == 'made_to_order':
        # Filter for made-to-order items
        made_to_order_items = []
        for inv in inventories:
            if inv.item.minimum_stock_level == 0 and inv.item.maximum_stock_level == 9999:
                made_to_order_items.append(inv.id)
        inventories = inventories.filter(id__in=made_to_order_items)
    elif stock_status == 'low':
        # Filter for items with quantity <= minimum_stock_level (excluding made-to-order items)
        low_stock_items = []
        for inv in inventories:
            if (inv.item.minimum_stock_level > 0 and
                inv.quantity <= inv.item.minimum_stock_level):
                low_stock_items.append(inv.id)
        inventories = inventories.filter(id__in=low_stock_items)
    elif stock_status == 'normal':
        # Filter for items with quantity between minimum and maximum (excluding made-to-order items)
        normal_stock_items = []
        for inv in inventories:
            if (inv.item.minimum_stock_level > 0 and
                inv.item.maximum_stock_level < 9999 and
                inv.quantity > inv.item.minimum_stock_level and
                inv.quantity < inv.item.maximum_stock_level):
                normal_stock_items.append(inv.id)
        inventories = inventories.filter(id__in=normal_stock_items)
    elif stock_status == 'high':
        # Filter for items with quantity >= maximum_stock_level (excluding made-to-order items)
        high_stock_items = []
        for inv in inventories:
            if (inv.item.maximum_stock_level < 9999 and
                inv.quantity >= inv.item.maximum_stock_level):
                high_stock_items.append(inv.id)
        inventories = inventories.filter(id__in=high_stock_items)

    context = {
        'inventories': inventories,
        'search_query': search_query,
        'selected_location': location,
        'selected_stock_status': stock_status
    }
    return render(request, 'items/item_inventory.html', context)

def raw_material_inventory(request):
    """Display raw material inventory with filtering capabilities."""
    # Get all raw material inventory items with related objects
    raw_material_inventories = RawMaterialInventory.objects.all().select_related(
        'raw_material', 'raw_material__category', 'unit'
    )

    # Filter by search query if provided
    search_query = request.GET.get('search', '')
    if search_query:
        raw_material_inventories = raw_material_inventories.filter(
            Q(raw_material__name__icontains=search_query) |
            Q(raw_material__category__name__icontains=search_query) |
            Q(location__icontains=search_query)
        )

    # Filter by location if provided
    location = request.GET.get('location', '')
    if location:
        raw_material_inventories = raw_material_inventories.filter(location=location)

    # Filter by stock status if provided
    stock_status = request.GET.get('stock_status', '')
    if stock_status == 'low':
        # Filter for materials with quantity <= reorder_point
        low_stock_materials = []
        for inv in raw_material_inventories:
            if inv.quantity <= inv.raw_material.reorder_point:
                low_stock_materials.append(inv.id)
        raw_material_inventories = raw_material_inventories.filter(id__in=low_stock_materials)
    elif stock_status == 'normal':
        # Filter for materials with quantity > reorder_point
        normal_stock_materials = []
        for inv in raw_material_inventories:
            if inv.quantity > inv.raw_material.reorder_point:
                normal_stock_materials.append(inv.id)
        raw_material_inventories = raw_material_inventories.filter(id__in=normal_stock_materials)

    context = {
        'raw_material_inventories': raw_material_inventories,
        'search_query': search_query,
        'selected_location': location,
        'selected_stock_status': stock_status
    }
    return render(request, 'items/raw_material_inventory.html', context)

def export_item_inventory_csv(request):
    """Export item inventory to CSV file."""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="item_inventory_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'

    # Get all inventory items with related objects
    inventories = Inventory.objects.all().select_related('item', 'item__category', 'unit')

    # Apply filters from request if they exist
    search_query = request.GET.get('search', '')
    if search_query:
        inventories = inventories.filter(
            Q(item__name__icontains=search_query) |
            Q(item__category__name__icontains=search_query) |
            Q(location__icontains=search_query)
        )

    location = request.GET.get('location', '')
    if location:
        inventories = inventories.filter(location=location)

    stock_status = request.GET.get('stock_status', '')
    if stock_status == 'made_to_order':
        # Filter for made-to-order items
        made_to_order_items = []
        for inv in inventories:
            if inv.item.minimum_stock_level == 0 and inv.item.maximum_stock_level == 9999:
                made_to_order_items.append(inv.id)
        inventories = inventories.filter(id__in=made_to_order_items)
    elif stock_status == 'low':
        low_stock_items = []
        for inv in inventories:
            if (inv.item.minimum_stock_level > 0 and
                inv.quantity <= inv.item.minimum_stock_level):
                low_stock_items.append(inv.id)
        inventories = inventories.filter(id__in=low_stock_items)
    elif stock_status == 'normal':
        normal_stock_items = []
        for inv in inventories:
            if (inv.item.minimum_stock_level > 0 and
                inv.item.maximum_stock_level < 9999 and
                inv.quantity > inv.item.minimum_stock_level and
                inv.quantity < inv.item.maximum_stock_level):
                normal_stock_items.append(inv.id)
        inventories = inventories.filter(id__in=normal_stock_items)
    elif stock_status == 'high':
        high_stock_items = []
        for inv in inventories:
            if (inv.item.maximum_stock_level < 9999 and
                inv.quantity >= inv.item.maximum_stock_level):
                high_stock_items.append(inv.id)
        inventories = inventories.filter(id__in=high_stock_items)

    writer = csv.writer(response)
    writer.writerow([
        'Item', 'Category', 'Quantity', 'Unit', 'Min Level', 'Max Level',
        'Status', 'Location', 'Last Updated'
    ])

    for inventory in inventories:
        item = inventory.item

        # Determine status
        if item.minimum_stock_level == 0 and item.maximum_stock_level == 9999:
            status = 'Made-to-Order'
        elif inventory.quantity <= item.minimum_stock_level:
            status = 'Low Stock'
        elif inventory.quantity >= item.maximum_stock_level:
            status = 'Overstocked'
        else:
            status = 'Normal'

        writer.writerow([
            item.name,
            item.category.name if item.category else '',
            inventory.quantity,
            inventory.unit.abbreviation,
            item.minimum_stock_level,
            item.maximum_stock_level,
            status,
            inventory.location or '',
            inventory.last_updated.strftime('%Y-%m-%d %H:%M')
        ])

    return response

def export_raw_material_inventory_csv(request):
    """Export raw material inventory to CSV file."""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="raw_material_inventory_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'

    # Get all raw material inventory items with related objects
    raw_material_inventories = RawMaterialInventory.objects.all().select_related(
        'raw_material', 'raw_material__category', 'unit'
    )

    # Apply filters from request if they exist
    search_query = request.GET.get('search', '')
    if search_query:
        raw_material_inventories = raw_material_inventories.filter(
            Q(raw_material__name__icontains=search_query) |
            Q(raw_material__category__name__icontains=search_query) |
            Q(location__icontains=search_query)
        )

    location = request.GET.get('location', '')
    if location:
        raw_material_inventories = raw_material_inventories.filter(location=location)

    stock_status = request.GET.get('stock_status', '')
    if stock_status == 'low':
        low_stock_materials = []
        for inv in raw_material_inventories:
            if inv.quantity <= inv.raw_material.reorder_point:
                low_stock_materials.append(inv.id)
        raw_material_inventories = raw_material_inventories.filter(id__in=low_stock_materials)
    elif stock_status == 'normal':
        normal_stock_materials = []
        for inv in raw_material_inventories:
            if inv.quantity > inv.raw_material.reorder_point:
                normal_stock_materials.append(inv.id)
        raw_material_inventories = raw_material_inventories.filter(id__in=normal_stock_materials)

    writer = csv.writer(response)
    writer.writerow([
        'Raw Material', 'Category', 'Quantity', 'Unit', 'Reorder Point',
        'Status', 'Location', 'Last Updated'
    ])

    for inventory in raw_material_inventories:
        raw_material = inventory.raw_material

        # Determine status
        if inventory.quantity <= raw_material.reorder_point:
            status = 'Needs Reorder'
        else:
            status = 'Normal'

        writer.writerow([
            raw_material.name,
            raw_material.category.name if raw_material.category else '',
            inventory.quantity,
            inventory.unit.abbreviation,
            raw_material.reorder_point,
            status,
            inventory.location or '',
            inventory.last_updated.strftime('%Y-%m-%d %H:%M')
        ])

    return response

# Export functionality
def export_items_csv(request):
    """
    Export items to CSV file.
    """
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="items_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'

    # Apply filters from request if they exist
    items = Item.objects.all().order_by('name')

    # Filter by search query if provided
    search_query = request.GET.get('search', '')
    if search_query:
        items = items.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Filter by category if provided
    category_id = request.GET.get('category')
    if category_id and category_id != 'all':
        items = items.filter(category_id=category_id)

    # Filter by active status if provided
    show_inactive = request.GET.get('show_inactive') == 'on'
    if not show_inactive:
        items = items.filter(is_active=True)

    writer = csv.writer(response)
    writer.writerow([
        'ID', 'Name', 'Description', 'Category', 'Unit', 'Selling Price',
        'Currency', 'Cost', 'Profit', 'Profit %', 'Active'
    ])

    for item in items:
        cost = item.cost()
        profit = item.profit()
        profit_percentage = item.profit_percentage()

        writer.writerow([
            item.id,
            item.name,
            item.description or '',
            item.category.name if item.category else '',
            item.unit.name,
            item.selling_price,
            item.currency.code,
            cost,
            profit,
            f"{profit_percentage}%",
            'Yes' if item.is_active else 'No'
        ])

    return response

def export_raw_materials_csv(request):
    """
    Export raw materials to CSV file.
    """
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="raw_materials_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'

    # Apply filters from request if they exist
    raw_materials = RawMaterial.objects.all().order_by('name')

    # Filter by search query if provided
    search_query = request.GET.get('search', '')
    if search_query:
        raw_materials = raw_materials.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Filter by category if provided
    category_id = request.GET.get('category')
    if category_id and category_id != 'all':
        raw_materials = raw_materials.filter(category_id=category_id)

    # Filter by active status if provided
    show_inactive = request.GET.get('show_inactive') == 'on'
    if not show_inactive:
        raw_materials = raw_materials.filter(is_active=True)

    writer = csv.writer(response)
    writer.writerow([
        'ID', 'Name', 'Description', 'Category', 'Unit', 'Price Per Unit',
        'Currency', 'Current Stock', 'Reorder Point', 'Needs Reorder', 'Active'
    ])

    for rm in raw_materials:
        current_stock = rm.get_current_stock()
        needs_reorder = rm.needs_reorder()

        writer.writerow([
            rm.id,
            rm.name,
            rm.description or '',
            rm.category.name if rm.category else '',
            rm.unit.name,
            rm.price_per_unit,
            rm.currency.code,
            current_stock,
            rm.reorder_point,
            'Yes' if needs_reorder else 'No',
            'Yes' if rm.is_active else 'No'
        ])

    return response


# ===== NEW INVENTORY MANAGEMENT VIEWS =====

def material_requirements_planning(request):
    """
    Display Material Requirements Planning (MRP) dashboard
    """
    try:
        mrp_service = MaterialRequirementPlanningService()

        # Get cases for MRP analysis
        from case.models import Case

        # Get pending and in-progress cases
        cases = Case.objects.filter(
            status__in=['pending', 'in_progress']
        ).select_related('patient', 'dentist').order_by('deadline')

        # Calculate material requirements for each case
        case_plans = []
        total_material_requirements = {}

        for case in cases[:10]:  # Limit to first 10 cases for performance
            try:
                plan = mrp_service.calculate_case_material_requirements(case)
                case_plans.append(plan)

                # Aggregate material requirements
                for req in plan.material_requirements:
                    material_id = req.raw_material.id
                    if material_id in total_material_requirements:
                        total_material_requirements[material_id]['required_quantity'] += req.required_quantity
                        total_material_requirements[material_id]['shortage_quantity'] += req.shortage_quantity
                    else:
                        total_material_requirements[material_id] = {
                            'raw_material': req.raw_material,
                            'required_quantity': req.required_quantity,
                            'available_quantity': req.available_quantity,
                            'shortage_quantity': req.shortage_quantity,
                            'unit': req.unit
                        }
            except Exception as e:
                logger.error(f"Error calculating MRP for case {case.case_number}: {str(e)}")
                continue

        # Get purchase recommendations
        purchase_recommendations = mrp_service.generate_purchase_recommendations()

        context = {
            'case_plans': case_plans,
            'total_material_requirements': total_material_requirements.values(),
            'purchase_recommendations': purchase_recommendations[:20],  # Top 20 recommendations
            'total_cases_analyzed': len(case_plans),
            'total_shortage_cost': sum(
                req['shortage_quantity'] * req['raw_material'].price_per_unit
                for req in total_material_requirements.values()
            )
        }

        return render(request, 'items/material_requirements_planning.html', context)

    except Exception as e:
        logger.error(f"Error in MRP dashboard: {str(e)}")
        messages.error(request, _('Error loading Material Requirements Planning dashboard.'))
        return redirect('items:item_list')


def stock_alerts_dashboard(request):
    """
    Display stock alerts dashboard
    """
    try:
        alert_service = StockAlertService()

        # Get alert summary
        alert_summary = alert_service.generate_alert_summary()

        # Get detailed alerts
        alerts = alert_service.check_stock_levels()

        context = {
            'alert_summary': alert_summary,
            'critical_alerts': alerts['critical'],
            'low_alerts': alerts['low'],
            'reorder_alerts': alerts['reorder'],
            'overstocked_alerts': alerts['overstocked'],
            'total_alerts': alert_summary['total_alerts']
        }

        return render(request, 'items/stock_alerts_dashboard.html', context)

    except Exception as e:
        logger.error(f"Error in stock alerts dashboard: {str(e)}")
        messages.error(request, _('Error loading stock alerts dashboard.'))
        return redirect('items:item_list')


def purchase_recommendations(request):
    """
    Display purchase recommendations
    """
    try:
        mrp_service = MaterialRequirementPlanningService()

        # Get days ahead parameter
        days_ahead = int(request.GET.get('days_ahead', 30))

        # Generate recommendations
        recommendations = mrp_service.generate_purchase_recommendations(days_ahead)

        # Group by priority
        recommendations_by_priority = {
            'urgent': [r for r in recommendations if r.priority == 'urgent'],
            'high': [r for r in recommendations if r.priority == 'high'],
            'medium': [r for r in recommendations if r.priority == 'medium'],
            'low': [r for r in recommendations if r.priority == 'low']
        }

        # Calculate totals
        total_cost = sum(r.estimated_cost for r in recommendations)
        urgent_cost = sum(r.estimated_cost for r in recommendations_by_priority['urgent'])

        context = {
            'recommendations': recommendations,
            'recommendations_by_priority': recommendations_by_priority,
            'days_ahead': days_ahead,
            'total_recommendations': len(recommendations),
            'total_cost': total_cost,
            'urgent_cost': urgent_cost,
            'urgent_count': len(recommendations_by_priority['urgent'])
        }

        return render(request, 'items/purchase_recommendations.html', context)

    except Exception as e:
        logger.error(f"Error in purchase recommendations: {str(e)}")
        messages.error(request, _('Error loading purchase recommendations.'))
        return redirect('items:item_list')


def case_material_analysis(request, case_id):
    """
    Detailed material analysis for a specific case
    """
    try:
        from case.models import Case
        case = get_object_or_404(Case, id=case_id)

        mrp_service = MaterialRequirementPlanningService()
        cost_service = CostTrackingService()

        # Get material requirements
        production_plan = mrp_service.calculate_case_material_requirements(case)

        # Get cost variance analysis
        cost_variance = cost_service.calculate_case_cost_variance(case)

        context = {
            'case': case,
            'production_plan': production_plan,
            'cost_variance': cost_variance,
            'material_requirements': production_plan.material_requirements,
            'missing_materials': production_plan.missing_materials,
            'feasible': production_plan.feasible,
            'total_cost': production_plan.total_cost,
            'shortage_cost': production_plan.total_shortage_cost
        }

        return render(request, 'items/case_material_analysis.html', context)

    except Exception as e:
        logger.error(f"Error in case material analysis: {str(e)}")
        messages.error(request, _('Error loading case material analysis.'))
        return redirect('case:case_list')


@require_POST
def create_purchase_order_from_recommendation(request):
    """
    Create a purchase order from purchase recommendations
    """
    try:
        from billing.models import PurchaseOrder, PurchaseOrderItem

        # Get selected recommendations
        recommendation_ids = request.POST.getlist('recommendation_ids')

        if not recommendation_ids:
            messages.error(request, _('No recommendations selected.'))
            return redirect('items:purchase_recommendations')

        # Group recommendations by supplier
        mrp_service = MaterialRequirementPlanningService()
        recommendations = mrp_service.generate_purchase_recommendations()

        # Filter selected recommendations
        selected_recommendations = [
            r for r in recommendations
            if str(r.raw_material.id) in recommendation_ids
        ]

        # Group by supplier
        supplier_groups = {}
        for rec in selected_recommendations:
            supplier = rec.supplier
            if supplier not in supplier_groups:
                supplier_groups[supplier] = []
            supplier_groups[supplier].append(rec)

        # Create purchase orders
        created_orders = []

        for supplier, supplier_recommendations in supplier_groups.items():
            # Create purchase order
            po = PurchaseOrder.objects.create(
                supplier=supplier,
                order_date=timezone.now().date(),
                expected_delivery_date=max(r.delivery_date for r in supplier_recommendations).date(),
                status='draft',
                notes=f'Auto-generated from MRP recommendations'
            )

            # Create purchase order items
            total_amount = 0
            for rec in supplier_recommendations:
                po_item = PurchaseOrderItem.objects.create(
                    purchase_order=po,
                    raw_material=rec.raw_material,
                    description=f'{rec.raw_material.name} - {rec.reason}',
                    quantity=rec.recommended_quantity,
                    unit=rec.raw_material.unit,
                    price_per_unit=rec.estimated_cost / rec.recommended_quantity,
                    currency=rec.raw_material.currency
                )
                total_amount += rec.estimated_cost

            # Update total amount
            po.total_amount = total_amount
            po.save()

            created_orders.append(po)

        messages.success(
            request,
            _('Created %(count)d purchase order(s) from recommendations.') % {
                'count': len(created_orders)
            }
        )

        # Redirect to purchase orders list
        return redirect('billing:purchase_order_list')

    except Exception as e:
        logger.error(f"Error creating purchase orders from recommendations: {str(e)}")
        messages.error(request, _('Error creating purchase orders from recommendations.'))
        return redirect('items:purchase_recommendations')