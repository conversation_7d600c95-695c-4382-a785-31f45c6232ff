# views.py

from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse
from django.contrib import messages
from django.db import transaction
from django.forms import inlineformset_factory
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.decorators import login_required, permission_required
from django.urls import reverse, reverse_lazy
from django.views.generic import ListView, TemplateView, DetailView
from django.views.generic.edit import CreateView, DeleteView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.core.paginator import Paginator
from django.utils import timezone
from django.db.models import Q, Count, F, ExpressionWrapper, DurationField, CharField
from django.db.models.functions import TruncDay, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>run<PERSON><PERSON><PERSON>, Cast
from django.core.serializers.json import DjangoJSONEncoder
# Import Django's timezone with an alias to avoid conflicts
from django.utils import timezone as django_timezone
from datetime import datetime, timedelta
import json
from dateutil.relativedelta import relativedelta

from case.mixins import UserContextMixin, AuditLogMixin
from case.services import CaseService, TaskService
from case.forms import CaseForm, CaseItemForm, TryoutForm, DentistSimpleCaseForm, DentistSimpleCaseItemForm, CaseItemFormSet, CaseFilterForm
from case.models import Case, CaseItem, Tooth, WorkflowStage, CaseTeeth, Department
from Dentists.models import Dentist
from billing.models import Invoice

import logging
logger = logging.getLogger(__name__)


def case_calendar(request):
    """
    View for case calendar. Handles both initial page load and AJAX requests for events.
    """
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return _get_calendar_events()
    return render(request, 'case/case_calendar.html')
def _get_calendar_events():
    """
    Helper function to get all calendar events.
    Returns JsonResponse with event data.
    """
    try:
        cases = Case.objects.select_related(
            'dentist',
            'patient'
        ).prefetch_related(
            'items'
        ).all()

        calendar_events = [
            _create_case_event(case)
            for case in cases
            if case.received_date_time
        ]
        return JsonResponse(calendar_events, safe=False)

    except Exception as e:
        logger.exception("Error fetching calendar events")
        return JsonResponse({
            'error': 'Failed to load calendar events',
            'details': str(e)
        }, status=500)

def _create_case_event(case):
    """
    Creates a calendar event dictionary for a single case.
    """
    CASE_COLORS = {
        'pending_acceptance': '#FFB6C1',
        'on_hold': '#FFE4B5',
        'in_progress': '#98FB98',
        'quality_check': '#87CEEB',
        'revision_needed': '#FFA07A',
        'ready_to_ship': '#DDA0DD',
        'shipped': '#B0C4DE',
        'delivered': '#90EE90',
        'completed': '#D3D3D3',
        'cancelled': '#CD5C5C'
    }

    # Get items
    items_list = []
    for item in case.items.all():
        through_item = CaseItem.objects.filter(case=case, item=item).first()
        if through_item:
            items_list.append(f"{item.name} (Qty: {through_item.quantity})")

    event = {
        'id': str(case.case_number),
        'title': f"Case {case.case_number}",
        'start': case.received_date_time.isoformat() if case.received_date_time else None,
        'color': CASE_COLORS.get(case.status, '#808080'),
        'url': reverse('case:case_detail', args=[case.case_number]),
        'extendedProps': {
            'dentist': str(case.dentist) if case.dentist else "N/A",
        'patient': getattr(case.patient, 'name', 'N/A') if case.patient else "N/A",
            'status': case.get_status_display(),
            'priority': dict(Case.PRIORITY_CHOICES).get(case.priority, 'Normal'),
            'items_with_quantity': ", ".join(items_list) if items_list else "No items",
        }
    }

    if case.deadline:
        event['end'] = case.deadline.isoformat()
        event['extendedProps']['is_overdue'] = case.is_overdue()

    return event

from django.shortcuts import render
from django.http import JsonResponse
from .models import Case
from itertools import cycle
from itertools import cycle

# def case_gantt_data(request):
#     cases = Case.objects.all()
#     colors = cycle(['#ffadad', '#ffd6a5', '#fdffb6', '#caffbf', '#9bf6ff', '#a0c4ff', '#bdb2ff'])
#     case_list = []

#     for case in cases:
#         items_with_quantity = [
#             f"{str(case_item.item)} (Quantity: {case_item.quantity})"
#             for case_item in case.caseitem_set.all()
#         ]
#         items_string = ", ".join(items_with_quantity)
#         dentist = str(case.dentist)
#         patients = str(case.patient)
#         color = next(colors)

#         case_data = {
#             'id': str(case.pk),
#             'title': f"Case {case.case_number} - {dentist} - {patients} - {items_string}",
#             'start': case.received_date_time.isoformat() if case.received_date_time else None,
#             'end': case.ship_date_time.isoformat() if case.ship_date_time else None,
#             'color': color,
#         }

#         if case_data['start'] and case_data['end']:  # Ensure both dates are present
#             case_list.append(case_data)

#     context = {'case_list': case_list}
#     return render(request, 'case/case_gantt_chart.html', context)


from datetime import datetime, timedelta
from django.shortcuts import render
from django.utils import timezone
from django.core.cache import cache
from django.core.paginator import Paginator
from django.contrib import messages
from django.views.decorators.cache import cache_page
from django.core.serializers.json import DjangoJSONEncoder
from django.db.models import F, ExpressionWrapper, DurationField
from .models import Case, CaseItem, Task
from .forms import DateRangeForm

def sanitize_case_title(title):
    return str(title).strip().replace(' ', '_').replace('-', '_').replace('/', '_').replace('#', '')

def get_case_class(status):
    status_colors = {
        'pending_acceptance': 'crit',
        'in_progress': 'active',
        'completed': 'done',
        'on_hold': 'warning',
        'ready_to_ship': 'milestone',
        'cancelled': 'warning',
        'delivered': 'done'
    }
    return status_colors.get(status, '')

@cache_page(60 * 15)
def case_gantt_data(request):
    try:
        # Get filter parameters
        start_date_str = request.GET.get('start_date')
        department_id = request.GET.get('department')
        status = request.GET.get('status')
        priority = request.GET.get('priority')
        page_size = int(request.GET.get('page_size', 25))

        # Import Django's timezone explicitly to avoid conflicts
        from django.utils import timezone as django_timezone

        # Set date range
        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                start_date = django_timezone.now().date() - timedelta(days=30)
        else:
            start_date = django_timezone.now().date() - timedelta(days=30)

        end_date = start_date + timedelta(days=30)

        # Base query me fushat e sakta
        cases = Case.objects.select_related(
            'patient',
            'dentist',
            'current_stage',
            'responsible_department'
        ).prefetch_related(
            'tasks',
            'items',
            'selected_teeth',
            'stagehistory_set'
        )

        # Date range filter me fushat e sakta
        cases = cases.filter(
            Q(received_date_time__date__lte=end_date) &
            (Q(ship_date_time__date__gte=start_date) | Q(ship_date_time__isnull=True))
        )

        # Apliko filtrat
        if department_id:
            cases = cases.filter(responsible_department_id=department_id)

        if status:
            cases = cases.filter(status=status)

        if priority:
            cases = cases.filter(priority=priority)

        # Analytics me fushat e sakta
        current_date = django_timezone.now().date()
        analytics = {
            'total_cases': cases.count(),
            'completed_cases': cases.filter(status='completed').count(),
            'in_progress': cases.filter(status='in_progress').count(),
            'delayed_cases': cases.filter(
                Q(deadline__date__lt=current_date) |
                Q(deadline__lt=current_date) &
                ~Q(status__in=['completed', 'delivered', 'cancelled'])
            ).count()
        }

        # Ndërto Gantt Chart
        gantt_definition = [
            "gantt",
            "dateFormat YYYY-MM-DD",
            f"title Case Production Timeline ({start_date} to {end_date})",
            "excludes weekends",
            ""
        ]

        for case in cases:
            section_title = f"Case #{case.case_number}"
            if case.patient:
                section_title += f" - {case.patient}"

            gantt_definition.append(f"section {section_title}")

            # Add case timeline
            case_start = case.received_date_time.strftime('%Y-%m-%d') if case.received_date_time else start_date.strftime('%Y-%m-%d')
            case_end = case.ship_date_time.strftime('%Y-%m-%d') if case.ship_date_time else (django_timezone.now() + timedelta(days=5)).strftime('%Y-%m-%d')

            gantt_definition.append(
                f"    Case Timeline : case-{case.status}, {case_start}, {case_end}"
            )

            # Add stages
            for stage in case.stagehistory_set.all().order_by('start_date_time'):
                if stage.start_date_time:
                    stage_start = stage.start_date_time.strftime('%Y-%m-%d')
                    stage_end = stage.end_date_time.strftime('%Y-%m-%d') if stage.end_date_time else case_end
                    gantt_definition.append(
                        f"    {stage.stage.name if stage.stage else 'Unknown'} : active, {stage_start}, {stage_end}"
                    )

        # Context me të dhënat e sakta
        context = {
            'mermaid_gantt_definition': "\n".join(gantt_definition),
            'cases': cases,
            'analytics': analytics,
            'departments': Department.objects.filter(is_active=True).values('id', 'name'),
            'start_date': start_date,
            'end_date': end_date,
            'selected_department': department_id,
            'selected_status': status,
            'selected_priority': priority,
            'page_size': page_size,
            'case_statuses': Case.STATUS_CHOICES,
            'case_priorities': Case.PRIORITY_CHOICES
        }

        return render(request, 'case/gantt_view.html', context)

    except Exception as e:
        # Import Django's timezone explicitly to avoid conflicts
        from django.utils import timezone as django_timezone

        messages.error(request, f"An error occurred: {str(e)}")
        return render(request, 'case/case_gantt_chart.html', {
            'error': str(e),
            'start_date': django_timezone.now().date() - timedelta(days=30),
            'end_date': django_timezone.now().date(),
            'page_size': 25
        })

    # except Exception as e:
    #     messages.error(request, f"An error occurred: {str(e)}")
    #     return render(request, 'case/case_gantt_chart.html', {
    #         'error': str(e),
    #         'start_date': timezone.now().date() - timedelta(days=90),
    #         'end_date': timezone.now().date(),
    #         'page_size': 25
    #     })


# views.py
from datetime import datetime, timedelta
from django.shortcuts import render
from django.utils import timezone
from django.contrib import messages
from django.db.models import Count, Avg, F, ExpressionWrapper, DurationField, Q
from django.db.models.functions import TruncDate
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
import json

from .models import Case, Department, WorkflowStage, StageHistory
from accounts.models import CustomUser



def case_gantt_view(request):
    try:
        # Get filter parameters
        start_date_str = request.GET.get('start_date')
        department_id = request.GET.get('department')
        status = request.GET.get('status')
        priority = request.GET.get('priority')
        page_size = int(request.GET.get('page_size', 25))

        # Import Django's timezone explicitly to avoid conflicts
        from django.utils import timezone as django_timezone

        # Set date range
        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                start_date = django_timezone.now().date() - timedelta(days=30)
        else:
            start_date = django_timezone.now().date() - timedelta(days=30)

        end_date = start_date + timedelta(days=30)

        # Base query me fushat e sakta
        cases = Case.objects.select_related(
            'patient',
            'dentist',
            'current_stage',
            'responsible_department'
        ).prefetch_related(
            'tasks',
            'items',
            'selected_teeth',
            'stagehistory_set'
        )

        # Date range filter me fushat e sakta
        cases = cases.filter(
            Q(received_date_time__date__lte=end_date) &
            (Q(ship_date_time__date__gte=start_date) | Q(ship_date_time__isnull=True))
        )

        # Apliko filtrat
        if department_id:
            cases = cases.filter(responsible_department_id=department_id)

        if status:
            cases = cases.filter(status=status)

        if priority:
            cases = cases.filter(priority=priority)

        # Analytics me fushat e sakta
        analytics = {
            'total_cases': cases.count(),
            'completed_cases': cases.filter(status='completed').count(),
            'in_progress': cases.filter(status='in_progress').count(),
            'delayed_cases': cases.filter(
                Q(deadline__lt=django_timezone.now()) &
                ~Q(status__in=['completed', 'delivered', 'cancelled'])
            ).count()
        }

        # Ndërto Gantt Chart
        gantt_definition = [
            "gantt",
            "dateFormat YYYY-MM-DD",
            f"title Case Production Timeline ({start_date} to {end_date})",
            "excludes weekends",
            ""
        ]

        for case in cases:
            section_title = f"Case #{case.case_number}"
            if case.patient:
                section_title += f" - {case.patient}"

            gantt_definition.append(f"section {section_title}")

            # Add case timeline
            case_start = case.received_date_time.strftime('%Y-%m-%d') if case.received_date_time else start_date.strftime('%Y-%m-%d')
            case_end = case.ship_date_time.strftime('%Y-%m-%d') if case.ship_date_time else (django_timezone.now() + timedelta(days=5)).strftime('%Y-%m-%d')

            gantt_definition.append(
                f"    Case Timeline : case-{case.status}, {case_start}, {case_end}"
            )

            # Add stages
            for stage in case.stagehistory_set.all().order_by('start_date_time'):
                if stage.start_date_time:
                    stage_start = stage.start_date_time.strftime('%Y-%m-%d')
                    stage_end = stage.end_date_time.strftime('%Y-%m-%d') if stage.end_date_time else case_end
                    gantt_definition.append(
                        f"    {stage.stage.name if stage.stage else 'Unknown'} : active, {stage_start}, {stage_end}"
                    )

        # Context me të dhënat e sakta
        context = {
            'mermaid_gantt_definition': "\n".join(gantt_definition),
            'cases': cases,
            'analytics': analytics,
            'departments': Department.objects.filter(is_active=True).values('id', 'name'),
            'start_date': start_date,
            'end_date': end_date,
            'selected_department': department_id,
            'selected_status': status,
            'selected_priority': priority,
            'page_size': page_size,
            'case_statuses': Case.STATUS_CHOICES,
            'case_priorities': Case.PRIORITY_CHOICES
        }

        return render(request, 'case/gantt_view.html', context)

    except Exception as e:
        # Import Django's timezone explicitly to avoid conflicts
        from django.utils import timezone as django_timezone

        messages.error(request, f"An error occurred: {str(e)}")
        return render(request, 'case/gantt_view.html', {
            'error': str(e),
            'start_date': django_timezone.now().date() - timedelta(days=30),
            'end_date': django_timezone.now().date(),
        })

def dhtmlx_gantt_view(request):
    try:
        # Get filter parameters
        date_range = request.GET.get('date_range', '30')
        start_date_str = request.GET.get('start_date')
        department_id = request.GET.get('department')
        status = request.GET.get('status')
        priority = request.GET.get('priority')
        search_term = request.GET.get('search', '')
        page_size = int(request.GET.get('page_size', 25))

        # Import Django's timezone explicitly to avoid conflicts
        from django.utils import timezone as django_timezone
        import json

        # Set date range based on date_range parameter or custom date
        current_date = django_timezone.now().date()

        if date_range == 'custom' and start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                start_date = current_date - timedelta(days=30)
        else:
            # Convert date_range to integer and use as number of days
            try:
                days = int(date_range)
                start_date = current_date
                # If date range is in the past, subtract days from current date
                if days < 0:
                    start_date = current_date + timedelta(days=days)
            except ValueError:
                # Default to 30 days if conversion fails
                start_date = current_date - timedelta(days=30)

        # End date is always a certain number of days from start date
        # For positive date ranges, add days to current date
        # For negative date ranges, add 30 days to start date
        try:
            days = int(date_range)
            if days > 0:
                end_date = current_date + timedelta(days=days)
            else:
                end_date = start_date + timedelta(days=30)
        except ValueError:
            end_date = start_date + timedelta(days=30)

        # Base queryset
        cases = Case.objects.select_related(
            'patient',
            'dentist',
            'current_stage',
            'responsible_department'
        ).prefetch_related(
            'tasks',
            'items',
            'selected_teeth',
            'stagehistory_set'
        )

        # Date range filter
        cases = cases.filter(
            Q(received_date_time__date__lte=end_date) &
            (Q(ship_date_time__date__gte=start_date) | Q(ship_date_time__isnull=True))
        )

        # Apply filters
        if department_id:
            cases = cases.filter(responsible_department_id=department_id)

        if status:
            cases = cases.filter(status=status)

        if priority:
            cases = cases.filter(priority=priority)

        # Search functionality
        if search_term:
            cases = cases.filter(
                Q(case_number__icontains=search_term) |
                Q(patient__first_name__icontains=search_term) |
                Q(patient__last_name__icontains=search_term) |
                Q(dentist__first_name__icontains=search_term) |
                Q(dentist__last_name__icontains=search_term) |
                Q(dentist__clinic_name__icontains=search_term) |
                Q(responsible_department__name__icontains=search_term) |
                Q(items__item__name__icontains=search_term)
            ).distinct()

        # Analytics - calculate before applying the slice
        current_date = django_timezone.now().date()
        analytics = {
            'total_cases': cases.count(),
            'completed_cases': cases.filter(status='completed').count(),
            'in_progress': cases.filter(status='in_progress').count(),
            'delayed_cases': cases.filter(
                Q(deadline__date__lt=current_date) |
                Q(deadline__lt=current_date) &
                ~Q(status__in=['completed', 'delivered', 'cancelled'])
            ).count()
        }

        # Limit to page size - do this after calculating analytics
        cases = cases[:page_size]

        # Build data for dhtmlxGantt
        gantt_data = {
            'data': [],
            'links': []
        }

        link_id = 1

        for case in cases:
            # Determine case start and end dates
            case_start = case.received_date_time if case.received_date_time else django_timezone.now().replace(hour=8, minute=0, second=0, microsecond=0)
            case_end = case.ship_date_time if case.ship_date_time else (django_timezone.now() + timedelta(days=5))

            # Check if case is overdue
            is_overdue = False
            if case.deadline:
                # Convert deadline to date if it's a datetime
                deadline_date = case.deadline.date() if hasattr(case.deadline, 'date') else case.deadline
                current_date = django_timezone.now().date()

                if deadline_date < current_date and case.status not in ['completed', 'delivered', 'cancelled']:
                    is_overdue = True

            # Get status and priority display names
            status_display = dict(Case.STATUS_CHOICES).get(case.status, case.status)
            priority_display = dict(Case.PRIORITY_CHOICES).get(case.priority, case.priority)

            # Create case task object
            case_task = {
                'id': case.case_number,
                'text': f"Case #{case.case_number}",
                'start_date': case_start.strftime("%Y-%m-%d %H:%M"),
                'end_date': case_end.strftime("%Y-%m-%d %H:%M"),
                'progress': 1.0 if case.status == 'completed' else (0.5 if case.status == 'in_progress' else 0.0),
                'type': 'case',
                'case_number': case.case_number,
                'status': case.status,
                'status_display': status_display,
                'priority': case.priority,
                'priority_display': priority_display,
                'patient': str(case.patient) if case.patient else 'No Patient',
                'dentist': str(case.dentist) if case.dentist else 'No Dentist',
                'department': str(case.responsible_department) if case.responsible_department else 'No Department',
                'deadline': case.deadline.strftime('%Y-%m-%d') if case.deadline else 'Not set',
                'is_overdue': is_overdue,
                'open': True  # Expand by default
            }
            gantt_data['data'].append(case_task)

            # Add stages as subtasks
            for stage in case.stagehistory_set.all().order_by('start_date_time'):
                if stage.start_date_time:
                    stage_start = stage.start_date_time
                    stage_end = stage.end_date_time if stage.end_date_time else case_end

                    # Calculate progress for the stage
                    progress = 1.0 if stage.end_date_time else 0.5 if stage.start_date_time else 0.0

                    # Create stage task object
                    stage_task = {
                        'id': f"stage_{stage.id}",
                        'text': stage.stage.name if stage.stage else 'Unknown Stage',
                        'start_date': stage_start.strftime("%Y-%m-%d %H:%M"),
                        'end_date': stage_end.strftime("%Y-%m-%d %H:%M"),
                        'parent': case.case_number,
                        'progress': progress,
                        'type': 'stage',
                        'case_number': case.case_number,
                        'department': str(stage.stage.department) if stage.stage and stage.stage.department else 'No Department',
                    }
                    gantt_data['data'].append(stage_task)

                    # Add link from case to stage
                    gantt_data['links'].append({
                        'id': link_id,
                        'source': case.case_number,
                        'target': f"stage_{stage.id}",
                        'type': '1'  # Finish to start
                    })
                    link_id += 1

        # Calculate efficiency rate (completed cases / total cases)
        total_cases_count = analytics['total_cases']
        completed_cases_count = analytics['completed_cases']
        efficiency_rate = round((completed_cases_count / total_cases_count) * 100) if total_cases_count > 0 else 0

        context = {
            'gantt_data_json': json.dumps(gantt_data, cls=DjangoJSONEncoder),
            'cases': cases,
            'analytics': analytics,
            'efficiency_rate': efficiency_rate,
            'departments': Department.objects.filter(is_active=True).values('id', 'name'),
            'start_date': start_date,
            'end_date': end_date,
            'date_range': date_range,
            'selected_department': department_id,
            'selected_status': status,
            'selected_priority': priority,
            'search_term': search_term,
            'page_size': page_size,
            'case_statuses': Case.STATUS_CHOICES,
            'case_priorities': Case.PRIORITY_CHOICES
        }

        return render(request, 'case/dhtmlx_gantt.html', context)

    except Exception as e:
        from django.utils import timezone as django_timezone
        messages.error(request, f"An error occurred: {str(e)}")
        return render(request, 'case/dhtmlx_gantt.html', {
            'error': str(e),
            'start_date': django_timezone.now().date() - timedelta(days=30),
            'end_date': django_timezone.now().date(),
            'date_range': '30',
            'gantt_data_json': json.dumps({'data': [], 'links': []}, cls=DjangoJSONEncoder),
            'analytics': {'total_cases': 0, 'completed_cases': 0, 'in_progress': 0, 'delayed_cases': 0},
            'efficiency_rate': 0,
            'case_statuses': Case.STATUS_CHOICES,
            'case_priorities': Case.PRIORITY_CHOICES,
            'departments': Department.objects.filter(is_active=True).values('id', 'name')
        })

# API Views
@require_http_methods(["GET"])
def case_detail_api(request, case_number):
    try:
        case = Case.objects.select_related(
            'patient',
            'dentist',
            'current_stage',
            'responsible_department'
        ).get(case_number=case_number)

        data = {
            'case_number': case.case_number,
            'patient': str(case.patient) if case.patient else None,
            'dentist': str(case.dentist) if case.dentist else None,
            'status': case.get_status_display(),
            'received_date': case.received_date_time.strftime('%Y-%m-%d %H:%M') if case.received_date_time else None,
            'deadline': case.deadline.strftime('%Y-%m-%d %H:%M') if case.deadline else None,
            'current_stage': str(case.current_stage) if case.current_stage else None,
            'priority': case.get_priority_display(),
            'department': str(case.responsible_department) if case.responsible_department else None,
        }
        return JsonResponse(data)
    except Case.DoesNotExist:
        return JsonResponse({'error': 'Case not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@require_http_methods(["GET"])
def case_history_api(request, case_number):
    try:
        case = Case.objects.get(case_number=case_number)
        history = case.stagehistory_set.all().select_related('stage').order_by('-start_date_time')

        data = [{
            'stage': str(h.stage),
            'start_date': h.start_date_time.strftime('%Y-%m-%d %H:%M') if h.start_date_time else None,
            'end_date': h.end_date_time.strftime('%Y-%m-%d %H:%M') if h.end_date_time else None,
            'duration': str(h.end_date_time - h.start_date_time) if h.end_date_time and h.start_date_time else None,
        } for h in history]
        return JsonResponse(data, safe=False)
    except Case.DoesNotExist:
        return JsonResponse({'error': 'Case not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@require_http_methods(["PATCH"])
def case_status_update_api(request, case_number):
    try:
        data = json.loads(request.body)
        case = Case.objects.get(case_number=case_number)

        if 'status' in data and data['status'] in dict(Case.STATUS_CHOICES):
            case.status = data['status']
            case.save()

            return JsonResponse({
                'status': case.get_status_display(),
                'case_number': case.case_number
            })
        else:
            return JsonResponse({'error': 'Invalid status'}, status=400)

    except Case.DoesNotExist:
        return JsonResponse({'error': 'Case not found'}, status=404)
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

# views.py

from django.db.models import Count
from .models import Case

def case_status_data(request):
  data = Case.objects.values('status').annotate(Count('status'))
  return JsonResponse(list(data), safe=False)

from django.db.models import Count, Avg, F

from django.db.models import Count, Avg, F


from django.db.models import Count
from .models import Case

def case_status_data(request):
  data = Case.objects.values('status').annotate(Count('status'))
  return JsonResponse(list(data), safe=False)

from django.db.models import Count, Avg, F, ExpressionWrapper, fields


from django.utils import timezone

from django.db.models.functions import Coalesce
from django.core.serializers import serialize
from django.http import JsonResponse





from django.shortcuts import render
from django.db.models import Count
from django.db.models.functions import TruncDay
from django.core.serializers.json import DjangoJSONEncoder
from django.utils import timezone
import json

from .models import Case, Department
from Dentists.models import Dentist
from datetime import timedelta
from django.db.models import Count, CharField
from django.db.models.functions import Cast
from dateutil.relativedelta import relativedelta



def dashboard(request):
    # Import Django's timezone explicitly to avoid conflicts
    from django.utils import timezone as django_timezone

    today = django_timezone.now().date()
    start_week = today - timedelta(days=today.weekday())
    start_month = today.replace(day=1)
    one_month_ago = today - timedelta(days=30)
    start_last_week = start_week - timedelta(weeks=1)
    start_last_month = one_month_ago.replace(day=1)
    start_last_year = today - relativedelta(years=1)



    # Query for total cases
    total_cases = Case.objects.count()

    # Cases for the current day, week, and month
    cases_today = Case.objects.filter(received_date_time__date=today).count()
    cases_this_week = Case.objects.filter(received_date_time__date__gte=start_week).count()
    cases_this_month = Case.objects.filter(received_date_time__date__gte=start_month).count()

    # Cases for one month ago, last week, and last month
    cases_one_month_ago = Case.objects.filter(received_date_time__date=one_month_ago).count()
    cases_last_week = Case.objects.filter(
        received_date_time__date__gte=start_last_week,
        received_date_time__date__lt=start_week
    ).count()
    cases_last_month = Case.objects.filter(
        received_date_time__date__gte=start_last_month,
        received_date_time__date__lt=start_month
    ).count()

    # Other queries remain the same
    cases_by_status = Case.objects.values('status').annotate(total=Count('case_number')).order_by('status')
    cases_by_priority = Case.objects.values('priority').annotate(total=Count('case_number')).order_by('priority')
    latest_cases = Case.objects.order_by('-received_date_time', '-case_number')[:5]
    cases_per_department = Department.objects.annotate(total_cases=Count('responsible_cases')).order_by('name')

    top_dentists = Dentist.objects.annotate(total_cases=Count('dentist_cases')).order_by('-total_cases')[:5]

    # Get cases for the last year, grouped by day
    cases_last_year = Case.objects.filter(
        received_date_time__date__gte=start_last_year,
        received_date_time__date__lt=today
    ).annotate(
        day=TruncDay('received_date_time')
    )

    # Group by day and count cases for each day
    cases_mapped = cases_last_year.values(
        'day'
    ).annotate(
        case_count=Count('case_number')
    ).annotate(
        date=Cast('day', CharField())
    )

    try:
        # Convert queryset to JSON
        cases_last_year_json = json.dumps(list(cases_mapped), cls=DjangoJSONEncoder)
    except Exception as e:
        print("Error converting to JSON:", e)
        cases_last_year_json = None


    # Get cases for the last month, grouped by day
    cases_last_month = Case.objects.filter(
        received_date_time__date__gte=start_last_month,
        received_date_time__date__lt=start_month
    ).annotate(
        day=TruncDay('received_date_time')
    )

    # Group by day and count cases for each day
    cases_mapped_month = cases_last_month.values(
        'day'
    ).annotate(
        case_count=Count('case_number')
    ).annotate(
        date=Cast('day', CharField())
    )

    try:
        # Convert queryset to JSON
        cases_last_month_json = json.dumps(list(cases_mapped_month), cls=DjangoJSONEncoder)
    except Exception as e:
        print("Error converting to JSON:", e)
        cases_last_month_json = None


    context = {
        'total_cases': total_cases,
        'cases_today': cases_today,
        'cases_this_week': cases_this_week,
        'cases_this_month': cases_this_month,
        'cases_one_month_ago': cases_one_month_ago,
        'cases_last_week': cases_last_week,
        'cases_last_month': cases_last_month,
        'cases_by_status': list(cases_by_status),
        'cases_by_priority': list(cases_by_priority),
        'latest_cases': latest_cases,
        'top_dentists': top_dentists,
        'cases_per_department': cases_per_department,
        'cases_last_month_json': cases_last_month_json,
        'cases_last_year_json': cases_last_year_json


    }

    return render(request, 'case/dashboard.html', context)




from django.shortcuts import render
from django.http import JsonResponse
from django.db.models import Q
from django.template.loader import render_to_string

def search_cases(request):
    search_term = request.GET.get('q', '')

    # Adjust the filter to search by case number or patient's first and last name and department

    results = Case.objects.filter(
        Q(case_number__icontains=search_term) |
        Q(patient__first_name__icontains=search_term) |
        Q(patient__last_name__icontains=search_term) |
        Q(responsible_department__name__icontains=search_term) |
        Q(dentist__first_name__icontains=search_term) |
        Q(dentist__last_name__icontains=search_term)
    )

    context = {'results': results}
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        html = render_to_string('case/partial/search_results.html', context, request=request)
        return JsonResponse({'html': html})
    else:
        # If not AJAX, you might render a full search results page instead
        return render(request, 'case/search_results.html', context)




#XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
# test dashboard


def case_management_dashboard(request):
    # Optional: Implement filtering logic here
    # e.g., filter by status, priority, dentist, patient, etc.

    cases = Case.objects.all()

    context = {
        'cases': cases,
    }
    return render(request, 'case/case_management_dashboard.html', context)





from django.shortcuts import render, redirect
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.contrib.auth.decorators import login_required
from django.views.generic import View
from django.urls import reverse
from datetime import datetime
from django.utils import timezone
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import PermissionDenied

from .models import Tryout, TryoutLocation, Case
from .forms import TryoutForm, TryoutAttachmentFormSet, TryoutFilterForm

# CREATE VIEW
@login_required
def tryout_create(request, case_number=None):
    """Create a new tryout, optionally associated with a case."""
    try:
        if case_number:
            case = get_object_or_404(Case, case_number=case_number)
        else:
            case = None

        if request.method == 'POST':
            form = TryoutForm(request.POST, request.FILES)
            attachment_formset = TryoutAttachmentFormSet(
                request.POST,
                request.FILES
            )

            if form.is_valid() and attachment_formset.is_valid():
                try:
                    with transaction.atomic():
                        tryout = form.save(commit=False)
                        if case:
                            tryout.case = case
                        tryout.created_by = request.user
                        tryout.save()

                        attachment_formset.instance = tryout
                        attachment_formset.save()

                        messages.success(request, _('Tryout created successfully!'))
                        return redirect('case:tryout_detail', id=tryout.id)
                except Exception as e:
                    messages.error(request, _(f'Error creating tryout: {str(e)}'))
        else:
            initial = {'case': case} if case else {}
            form = TryoutForm(initial=initial)
            attachment_formset = TryoutAttachmentFormSet()

        context = {
            'form': form,
            'attachment_formset': attachment_formset,
            'case': case,
            'is_update': False,
            'title': _('Create New Tryout')
        }
        return render(request, 'case/tryout_form.html', context)
    except Exception as e:
        messages.error(request, _('An unexpected error occurred'))
        return redirect('case:tryout_list')

# UPDATE VIEW
# views.py

@login_required
def tryout_update(request, id):
    """Update an existing tryout."""
    try:
        tryout = get_object_or_404(Tryout, pk=id)

        # Permission check
        if not request.user.has_perm('case.change_tryout'):
            messages.error(request, _('You do not have permission to update this tryout'))
            return redirect('case:tryout_list')

        if request.method == 'POST':
            form = TryoutForm(request.POST, instance=tryout)
            attachment_formset = TryoutAttachmentFormSet(
                request.POST,
                request.FILES,
                instance=tryout
            )

            if form.is_valid() and attachment_formset.is_valid():
                try:
                    with transaction.atomic():
                        # Save the tryout
                        tryout = form.save(commit=False)
                        tryout.modified_by = request.user
                        tryout.modified_at = timezone.now()
                        tryout.save()
                        form.save_m2m()  # Save many-to-many relationships

                        # Save attachments
                        attachments = attachment_formset.save(commit=False)
                        for attachment in attachments:
                            attachment.tryout = tryout
                            attachment.save()

                        # Handle deleted attachments
                        for obj in attachment_formset.deleted_objects:
                            obj.delete()

                        messages.success(request, _('Tryout updated successfully!'))
                        return redirect('case:tryout_detail', id=tryout.id)
                except Exception as e:
                    messages.error(request, _(f'Error updating tryout: {str(e)}'))
            else:
                messages.error(request, _('Please correct the errors below.'))
                # Print form errors for debugging
                print("Form errors:", form.errors)
                print("Formset errors:", attachment_formset.errors)
        else:
            form = TryoutForm(instance=tryout)
            attachment_formset = TryoutAttachmentFormSet(instance=tryout)

        context = {
            'form': form,
            'attachment_formset': attachment_formset,
            'tryout': tryout,
            'is_update': True,
            'title': _('Update Tryout')
        }
        return render(request, 'case/tryout_form.html', context)
    except Exception as e:
        messages.error(request, _('An unexpected error occurred'))
        print("Exception:", str(e))
        return redirect('case:tryout_list')
# DETAIL VIEW
@login_required
def tryout_detail(request, id):
    """Display detailed information about a tryout."""
    try:
        tryout = get_object_or_404(
            Tryout.objects.select_related(
                'case',
                'case__patient',
                'location',
                'created_by',
                'modified_by'
            ),
            id=id
        )

        attachments = tryout.tryout_attachments.all()

        # Check which attachments are images
        for attachment in attachments:
            attachment.is_image = attachment.attachment.name.lower().endswith(
                ('.png', '.jpg', '.jpeg', '.gif')
            )

        context = {
            'tryout': tryout,
            'attachments': attachments,
            'title': _(f'Tryout Detail - {tryout.case.case_number if tryout.case else "No Case"}')
        }
        return render(request, 'case/tryout_detail.html', context)
    except Exception as e:
        messages.error(request, _('Error loading tryout details'))
        return redirect('case:tryout_list')

# LIST VIEW
# views.py
from django.contrib.auth.decorators import login_required
from django.shortcuts import render, redirect
from django.contrib import messages
from django.db.models import Q
from django.core.paginator import Paginator
from django.utils.translation import gettext as _
from .models import Tryout, TryoutLocation
from .forms import TryoutFilterForm
import logging

logger = logging.getLogger(__name__)

@login_required
def tryout_list(request):
    try:
        # Initialize the filter form
        filter_form = TryoutFilterForm(request.GET or None)

        # Base queryset with select_related to optimize queries
        tryouts = Tryout.objects.select_related(
            'case',
            'case__patient',
            'location'
        )

        # Apply filters if form is valid
        if filter_form.is_valid():
            # Search filter
            if filter_form.cleaned_data.get('search'):
                search_query = filter_form.cleaned_data['search']
                tryouts = tryouts.filter(
                    Q(case__case_number__icontains=search_query) |
                    Q(location__name__icontains=search_query) |
                    Q(case__patient__first_name__icontains=search_query) |
                    Q(case__patient__last_name__icontains=search_query)
                )

            # Status filter
            if filter_form.cleaned_data.get('status'):
                tryouts = tryouts.filter(status=filter_form.cleaned_data['status'])

            # Location filter
            if filter_form.cleaned_data.get('location'):
                tryouts = tryouts.filter(location=filter_form.cleaned_data['location'])

            # Date range filter
            if filter_form.cleaned_data.get('date_from'):
                tryouts = tryouts.filter(
                    date_time__date__gte=filter_form.cleaned_data['date_from']
                )
            if filter_form.cleaned_data.get('date_to'):
                tryouts = tryouts.filter(
                    date_time__date__lte=filter_form.cleaned_data['date_to']
                )

        # Calculate statistics
        stats = {
            'total_count': tryouts.count(),
            'scheduled_count': tryouts.filter(status='scheduled').count(),
            'completed_count': tryouts.filter(status='completed').count(),
            'cancelled_count': tryouts.filter(status='cancelled').count(),
        }

        # Pagination
        tryouts = Tryout.objects.all().order_by('created_at')
        paginator = Paginator(tryouts, 10)
        page = request.GET.get('page', 1)
        try:
            tryouts_page = paginator.page(page)
        except:
            tryouts_page = paginator.page(1)

        context = {
            'tryouts': tryouts_page,
            'filter_form': filter_form,
            'stats': stats,
            'status_choices': Tryout.STATUS_CHOICES,
            'title': _('Tryout List')
        }

        return render(request, 'case/tryout_list.html', context)

    except Exception as e:
        messages.error(request, f'Error loading tryout list: {str(e)}')
        return redirect('home')
# DELETE VIEW
@login_required
def tryout_delete(request, id):
    """Delete a tryout."""
    try:
        tryout = get_object_or_404(Tryout, pk=id)

        # Permission check
        if not request.user.has_perm('case.delete_tryout'):
            raise PermissionDenied

        if request.method == 'POST':
            try:
                with transaction.atomic():
                    tryout.delete()
                messages.success(request, _('Tryout deleted successfully'))
            except Exception as e:
                messages.error(request, _(f'Error deleting tryout: {str(e)}'))
        else:
            return render(request, 'case/tryout_confirm_delete.html', {
                'tryout': tryout,
                'title': _('Delete Tryout')
            })

        return redirect('case:tryout_list')
    except PermissionDenied:
        messages.error(request, _('You do not have permission to delete this tryout'))
        return redirect('case:tryout_list')
    except Exception as e:
        messages.error(request, _('An unexpected error occurred'))
        return redirect('case:tryout_list')



# ATTACHMENT HANDLING
@login_required
def tryout_attachment_delete(request, tryout_id, attachment_id):
    """Delete a specific attachment from a tryout."""
    try:
        tryout = get_object_or_404(Tryout, pk=tryout_id)
        attachment = get_object_or_404(TryoutAttachment, pk=attachment_id, tryout=tryout)

        if request.method == 'POST':
            try:
                with transaction.atomic():
                    # Delete the file from storage
                    attachment.attachment.delete(save=False)
                    # Delete the attachment record
                    attachment.delete()
                messages.success(request, _('Attachment deleted successfully'))
            except Exception as e:
                messages.error(request, _(f'Error deleting attachment: {str(e)}'))

        return redirect('case:tryout_detail', id=tryout_id)
    except Exception as e:
        messages.error(request, _('Error processing your request'))
        return redirect('case:tryout_detail', id=tryout_id)

# AJAX VIEWS (if needed)
@login_required
def load_locations(request):
    """Ajax view to load locations based on some criteria."""
    try:
        locations = TryoutLocation.objects.all().order_by('name')
        return JsonResponse({
            'locations': list(locations.values('id', 'name'))
        })
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)

@login_required
def update_tryout_status(request, id):
    """Ajax view to update tryout status."""
    if request.method == 'POST' and request.is_ajax():
        try:
            tryout = get_object_or_404(Tryout, pk=id)
            status = request.POST.get('status')

            if status in dict(Tryout.STATUS_CHOICES):
                tryout.status = status
                tryout.save(update_fields=['status'])
                return JsonResponse({'success': True})
            else:
                return JsonResponse({'error': 'Invalid status'}, status=400)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=400)
    return JsonResponse({'error': 'Invalid request'}, status=400)
################### Task view #########################

from .models import Task
from .forms import TaskForm


#task create general

from django.shortcuts import render, redirect
from .forms import TaskForm

@login_required
@permission_required('tasks.add_task', raise_exception=True)
def task_create_general(request):
    try:
        if request.method == 'POST':
            form = TaskForm(request.POST, request.FILES, user=request.user)
            if form.is_valid():
                task = form.save(commit=False)
                task.created_by = request.user
                task.save()
                messages.success(request, _('Task created successfully.'))
                return redirect('case:task_list')
            else:
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f"{field}: {error}")
        else:
            form = TaskForm(user=request.user)

        context = {
            'form': form,
            'title': _('Create New Task')
        }
        return render(request, 'case/task_create.html', context)

    except Exception as e:
        messages.error(request, str(e))
        return redirect('case:task_list')

@login_required
@permission_required('case.add_task', raise_exception=True)
def task_create(request, case_id):
    case = get_object_or_404(Case, pk=case_id)
    try:
        if request.method == 'POST':
            form = TaskForm(request.POST, request.FILES, user=request.user)
            if form.is_valid():
                task = form.save(commit=False)
                task.case = case
                task.created_by = request.user
                task.save()
                messages.success(request, _('Task created successfully.'))
                return redirect('case:case_detail', case_number=case.case_number)
            else:
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f"{field}: {error}")
        else:
            form = TaskForm(user=request.user, initial={'case': case})

        context = {
            'form': form,
            'case': case,
            'title': f'Create Task for Case #{case.case_number}'
        }
        return render(request, 'case/task_create.html', context)

    except Exception as e:
        messages.error(request, str(e))
        return redirect('case:case_detail', case_number=case.case_number)

@login_required
def task_update(request, task_id):
    task = get_object_or_404(Task, pk=task_id)
    try:
        if request.method == 'POST':
            form = TaskForm(request.POST, request.FILES, instance=task, user=request.user)
            if form.is_valid():
                task = form.save()
                messages.success(request, _('Task updated successfully.'))
                return redirect('case:case_detail', case_number=task.case.case_number)
            else:
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f"{field}: {error}")
        else:
            form = TaskForm(instance=task, user=request.user)

        context = {
            'form': form,
            'task': task,
            'title': f'Update Task: {task.title}'
        }
        return render(request, 'case/task_update.html', context)

    except Exception as e:
        messages.error(request, str(e))
        return redirect('case:task_list')

@login_required
def task_detail(request, task_id):
    task = get_object_or_404(Task, id=task_id)
    context = {
        'task': task,
        'title': f'Task: {task.title}'
    }
    return render(request, 'case/task_detail.html', context)



from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.views.generic.edit import DeleteView

class TaskDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    model = Task
    template_name = 'case/task_confirm_delete.html'
    context_object_name = 'task'
    pk_url_kwarg = 'task_id'

    def test_func(self):
        task = self.get_object()
        return self.request.user.is_superuser or task.created_by == self.request.user

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, 'Task deleted successfully.')
        return super().delete(request, *args, **kwargs)

    def get_success_url(self):
        return reverse('case:task_list')



@login_required
def task_list(request):
    # Start with all tasks
    if request.user.is_superuser:
        tasks_queryset = Task.objects.all()
    else:
        tasks_queryset = Task.objects.filter(
            Q(assigned_to=request.user) |
            Q(case__responsible_department__in=request.user.departments.all())
        ).distinct()

    # Filtrat ekzistues
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    priority_filter = request.GET.get('priority', '')
    due_date_filter = request.GET.get('due_date', '')

    # Aplikimi i filtrave
    if search_query:
        tasks_queryset = tasks_queryset.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(case__case_number__icontains=search_query) |
            Q(assigned_to__username__icontains=search_query) |
            Q(workflow_stage__name__icontains=search_query)
        )

    if status_filter:
        tasks_queryset = tasks_queryset.filter(status=status_filter)

    if priority_filter:
        tasks_queryset = tasks_queryset.filter(priority=priority_filter)

    # Due date filter using schedule items instead of scheduled_end_time
    if due_date_filter:
        try:
            due_date = datetime.strptime(due_date_filter, '%Y-%m-%d').date()
            # Get tasks that have schedule items ending on the specified date
            tasks_queryset = tasks_queryset.filter(
                schedule_items__end_time__date=due_date
            )
        except (ValueError, TypeError):
            pass

    # Optimizimi i query-ve
    tasks_queryset = tasks_queryset.select_related(
        'case',
        'workflow_stage',
        'assigned_to',
        'created_by'
    ).prefetch_related(
        'dependencies',
        'schedule_items'
    ).order_by(
        'status',
        '-priority',
        'created_at'
    )

    # Statistikat
    stats = {
        'pending_count': tasks_queryset.filter(status='pending').count(),
        'in_progress_count': tasks_queryset.filter(status='in_progress').count(),
        'completed_count': tasks_queryset.filter(status='completed').count(),
        'delayed_count': tasks_queryset.filter(status='delayed').count(),
    }

    # Pagination
    paginator = Paginator(tasks_queryset, 10)
    page = request.GET.get('page', 1)
    try:
        tasks = paginator.page(page)
    except:
        tasks = paginator.page(1)

    context = {
        'tasks': tasks,
        'search_query': search_query,
        'status_filter': status_filter,
        'priority_filter': priority_filter,
        'due_date_filter': due_date_filter,
        'status_choices': Task.STATUS_CHOICES,
        'priority_choices': Task.PRIORITY_CHOICES,
        'stats': stats,
        'current_time': django_timezone.now(),
    }

    return render(request, 'case/task_list.html', context)


def task_start(request, task_id):
    task = get_object_or_404(Task, pk=task_id)
    success, message = TaskService.start_task(task, request.user)
    if success:
        messages.success(request, message)
    else:
        messages.error(request, message)
    return redirect('case:task_list')

def task_complete(request, task_id):
    task = get_object_or_404(Task, pk=task_id)
    success, message = TaskService.complete_task(task, request.user)
    if success:
        messages.success(request, message)
    else:
        messages.error(request, message)
    return redirect('case:task_list')

# API Views
from django.http import JsonResponse

def get_workflow_stages(request, department_id):
    """
    API endpoint to get workflow stages for a department.
    """
    try:
        stages = WorkflowStage.objects.filter(department_id=department_id).order_by('order')
        data = [{
            'id': stage.id,
            'name': stage.name,
            'order': stage.order,
            'department': stage.department.name if stage.department else None,
            'estimated_time': stage.estimated_time
        } for stage in stages]
        return JsonResponse({'stages': data})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)

def patient_search(request):
    """
    API endpoint to search for patients.
    """
    try:
        query = request.GET.get('q', '')
        if len(query) < 2:
            return JsonResponse({'patients': []})

        from patient.models import Patient
        patients = Patient.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(email__icontains=query) |
            Q(phone__icontains=query)
        )[:10]  # Limit to 10 results

        data = [{
            'id': patient.id,
            'name': f"{patient.first_name} {patient.last_name}",
            'email': patient.email,
            'phone': patient.phone
        } for patient in patients]

        return JsonResponse({'patients': data})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)



########### Krijoni endpoints të reja API për dentistët për të regjistruar dhe menaxhuar rastet e tyre
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated

class DentistCaseViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Case.objects.filter(dentist_user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(dentist_user=self.request.user)






from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin

class DentistDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'dentist_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['cases'] = Case.objects.filter(dentist_user=self.request.user)
        return context


from django.views.generic.edit import CreateView
from django.urls import reverse_lazy

class DentistCaseCreateView(LoginRequiredMixin, UserContextMixin, AuditLogMixin, CreateView):
    model = Case
    form_class = DentistSimpleCaseForm
    template_name = 'create_case.html'
    success_url = reverse_lazy("dentist_dashboard")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        form.instance.dentist_user = self.request.user
        form.instance.dentist = self.request.user.dentist
        return super().form_valid(form)

@login_required
def dentist_dashboard(request):
    if not request.user.is_dentist():
        return redirect('home')
    cases = Case.objects.filter(dentist_user=request.user)
    return render(request, 'case/dentist_dashboard.html', {'cases': cases})
from patients.models import Patient
from items.models import Item, ItemRawMaterial
from case.models import Tooth

# në views.py

@login_required
def dentist_case_create(request):
    if not request.user.is_dentist():
        return redirect('home')

    if request.method == 'POST':
        form = DentistSimpleCaseForm(request.POST)
        formset = DentistSimpleCaseItemForm(request.POST, queryset=CaseItem.objects.none())

        if form.is_valid() and formset.is_valid():
            case = form.save(commit=False)
            case.dentist_user = request.user
            case.dentist = request.user.dentist_profile
            case.save()

            for case_item_form in formset:
                if case_item_form.cleaned_data and not case_item_form.cleaned_data.get('DELETE', False):
                    case_item = case_item_form.save(commit=False)
                    case_item.case = case
                    case_item.save()

            messages.success(request, 'Rasti u krijua me sukses!')
            return redirect('case:dentist_dashboard')
        else:
            messages.error(request, 'Ju lutemi korrigjoni gabimet e mëposhtme.')
    else:
        form = DentistSimpleCaseForm()
        formset = DentistSimpleCaseItemForm(queryset=CaseItem.objects.none())

    context = {
        'form': form,
        'formset': formset,
    }
    return render(request, 'case/dentist_case_create.html', context)





@login_required
def dentist_case_list(request):
    if not request.user.is_dentist():
        return redirect('home')
    cases = Case.objects.filter(dentist_user=request.user)
    return render(request, 'case/dentist_case_list.html', {'cases': cases})


@login_required
def update_case_status(request, case_id):
    # Get case and new status
    case = get_object_or_404(Case, pk=case_id)
    new_status = request.POST.get('status')
    # Use service layer
    CaseService.update_case_status(case, new_status, request.user)
    # Return response



class DentistCaseListView(ListView):
    model = Case
    template_name = 'dentist_case_list.html'
    context_object_name = 'cases'

    def get_queryset(self):
        return Case.objects.filter(dentist_user=self.request.user)















##############################


from django.db.models import Count, Avg, F, Q, ExpressionWrapper, fields
from django.db.models.functions import TruncDate
# Using django_timezone alias to avoid conflicts
from datetime import timedelta
from case.models import Case, Department
from Dentists.models import Dentist
from items.models import Item

def get_dashboard_data():
    departments = Department.objects.all()
    dashboard_data = []
    start_date = django_timezone.now().date() - timedelta(days=30)
    total_cases = 0
    total_in_progress = 0
    total_completed = 0
    total_delayed = 0

    for department in departments:
        cases = Case.objects.filter(responsible_department=department)
        completed_cases = cases.filter(
            status='ready_to_ship',
            received_date_time__isnull=False,
            ship_date_time__isnull=False,
            ship_date_time__gte=start_date
        )

        in_progress_cases = cases.filter(status='in_progress')
        delayed_cases = in_progress_cases.filter(received_date_time__lt=start_date)

        avg_completion_time = completed_cases.aggregate(
            avg_time=Avg(ExpressionWrapper(
                F('ship_date_time') - F('received_date_time'),
                output_field=fields.DurationField()
            ))
        )['avg_time']

        dept_data = {
            'department': department.name,
            'total_cases': cases.count(),
            'cases_in_progress': in_progress_cases.count(),
            'cases_completed': completed_cases.count(),
            'cases_delayed': delayed_cases.count(),
            'current_workload': in_progress_cases.count(),
            'avg_completion_time': avg_completion_time.days if avg_completion_time else 0
        }
        dashboard_data.append(dept_data)

        total_cases += dept_data['total_cases']
        total_in_progress += dept_data['cases_in_progress']
        total_completed += dept_data['cases_completed']
        total_delayed += dept_data['cases_delayed']

    # Top 5 dentistët me rastet më aktive
    top_dentists = Dentist.objects.annotate(
        active_cases=Count('cases', filter=Q(cases__status='in_progress'))
    ).order_by('-active_cases')[:5]

    # Trendi i rasteve të reja për 30 ditët e fundit
    thirty_days_ago = timezone.now().date() - timedelta(days=30)
    new_cases_trend = Case.objects.filter(
        received_date_time__date__gte=thirty_days_ago
    ).annotate(
        date=TruncDate('received_date_time')
    ).values('date').annotate(
        count=Count('case_number')
    ).order_by('date')

    # Merrni rastet urgjente (p.sh., ato që janë vonuar më shumë se 7 ditë)
    urgent_cases = Case.objects.filter(
        status='in_progress',
        received_date_time__date__lt=django_timezone.now().date() - timedelta(days=7)
    ).annotate(
        days_overdue=ExpressionWrapper(
            django_timezone.now().date() - F('received_date_time__date'),
            output_field=fields.DurationField()
        )
    ).order_by('-days_overdue')[:5]

    # Artikujt e inventarit me nivel të ulët
    low_inventory_threshold = 10
    low_inventory_items = Item.objects.filter(
        inventory__quantity__lte=low_inventory_threshold
    ).annotate(
        quantity=F('inventory__quantity')
    ).order_by('quantity')[:5]

    return {
        'dashboard_data': dashboard_data,
        'total_cases': total_cases,
        'total_in_progress': total_in_progress,
        'total_completed': total_completed,
        'total_delayed': total_delayed,
        'top_dentists': top_dentists,
        'new_cases_trend': {
            'dates': [entry['date'].strftime('%Y-%m-%d') for entry in new_cases_trend],
            'counts': [entry['count'] for entry in new_cases_trend]
        },
        'urgent_cases': urgent_cases,
        'low_inventory_items': low_inventory_items
    }
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.db.models import Sum

from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin

class ManagerDashboardView(LoginRequiredMixin, UserPassesTestMixin, TemplateView):
    template_name = 'case/manager_dashboard.html'

    def test_func(self):
        return self.request.user.is_staff or self.request.user.has_perm('view_dashboard')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(get_dashboard_data())
        return context

    ########################################
# case/views.py
# views.py
from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from .models import Department
import json
from datetime import datetime, date
from dateutil.relativedelta import relativedelta
import logging

logger = logging.getLogger(__name__)

@require_http_methods(["GET"])
def performance_dashboard(request):
    """
    View function for the dental lab performance dashboard.
    Handles both initial page load and AJAX requests for updates.
    """
    try:
        # Parse dates from request
        end_date = request.GET.get('end_date')
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else date.today()

        start_date = request.GET.get('start_date')
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else (end_date - relativedelta(months=1))

        # Get metrics - note we only pass start_date and end_date
        metrics, error = get_performance_metrics(start_date, end_date)

        if error:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': str(error),
                    'status': 'error'
                }, status=500)
            return HttpResponse(f"An error occurred: {str(error)}", status=500)

        # Add date range to context
        context = {
            **metrics,
            'start_date': start_date,
            'end_date': end_date,
        }

        # Handle AJAX requests
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'data': context,
                'status': 'success'
            })

        # Regular page load
        return render(request, 'case/performance_dashboard.html', context)

    except Exception as e:
        logger.error(f"Dashboard error: {str(e)}")
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'error': str(e),
                'status': 'error'
            }, status=500)
        return HttpResponse(f"An error occurred: {str(e)}", status=500)


from datetime import datetime, date, timedelta
# Import Django's timezone instead of datetime.timezone to avoid conflicts
from django.utils import timezone as django_timezone
from dateutil.relativedelta import relativedelta
from django.db.models import (
    Avg, Count, Sum, F, Q, ExpressionWrapper, FloatField, IntegerField,
    DecimalField, DurationField, Case as CaseExpression, When
)
from django.db.models.functions import Cast, Coalesce
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)

class PerformanceCalculator:
    def __init__(self, start_date=None, end_date=None):
        self.start_date = start_date or date.today() - relativedelta(months=1)
        self.end_date = end_date or date.today()

    def get_cases_in_range(self):
        return Case.objects.filter(
            received_date_time__date__range=[self.start_date, self.end_date]
        )

    def calculate_case_progress(self, cases):
        """Calculate weighted progress based on case priority."""
        total_cases = cases.count()
        if total_cases == 0:
            return 0

        weighted_sum = cases.aggregate(
            weighted_sum=Sum(
                CaseExpression(
                    When(priority=1, then=3),
                    When(priority=2, then=2),
                    When(priority=3, then=1),
                    default=0,
                    output_field=IntegerField()
                )
            )
        )['weighted_sum'] or 0

        total_weight = total_cases * 3
        return (float(weighted_sum) / float(total_weight)) * 100

    def calculate_task_efficiency(self):
        """Calculate task completion efficiency ratio."""
        tasks = Task.objects.filter(
            actual_start_time__date__range=[self.start_date, self.end_date],
            actual_end_time__isnull=False,
            estimated_duration__isnull=False
        ).annotate(
            calculated_duration=ExpressionWrapper(
                F('actual_end_time') - F('actual_start_time'),
                output_field=DurationField()
            ),
            actual_duration_hours=ExpressionWrapper(
                Cast(F('calculated_duration'), FloatField()) / 3600000000,
                output_field=FloatField()
            ),
            estimated_duration_hours=Cast(F('estimated_duration'), FloatField()),
            efficiency=ExpressionWrapper(
                F('estimated_duration_hours') / F('actual_duration_hours'),
                output_field=FloatField()
            )
        ).exclude(calculated_duration=timedelta(0))

        avg_efficiency = tasks.aggregate(
            avg_efficiency=Avg('efficiency')
        )['avg_efficiency'] or 0

        return avg_efficiency * 100

    def calculate_material_metrics(self):
        """Calculate material efficiency and waste metrics."""
        case_items = CaseItem.objects.filter(
            case__received_date_time__date__range=[self.start_date, self.end_date]
        )

        actual_usage = case_items.aggregate(
            total_usage=Coalesce(Sum(Cast('quantity', DecimalField())), Decimal('0'))
        )['total_usage']

        estimated_usage = ItemRawMaterial.objects.filter(
            item__caseitem__case__received_date_time__date__range=[self.start_date, self.end_date]
        ).aggregate(
            estimated_usage=Coalesce(Sum(Cast('quantity', DecimalField())), Decimal('0'))
        )['estimated_usage']

        wasted_material = case_items.filter(
            status='wasted'
        ).aggregate(
            total_waste=Coalesce(Sum(Cast('quantity', DecimalField())), Decimal('0'))
        )['total_waste']

        return {
            'material_efficiency': self._calculate_efficiency(actual_usage, estimated_usage),
            'material_waste_rate': self._calculate_waste_rate(wasted_material, actual_usage)
        }

    def calculate_financial_metrics(self):
        """Calculate financial performance metrics."""
        invoices = Invoice.objects.filter(date__range=[self.start_date, self.end_date])
        total_revenue = invoices.aggregate(
            total=Coalesce(Sum('total_amount'), Decimal('0'))
        )['total']

        total_cost = CaseItem.objects.filter(
            case__received_date_time__date__range=[self.start_date, self.end_date]
        ).annotate(
            item_cost=ExpressionWrapper(
                F('quantity') * Cast(F('item__itemrawmaterial__raw_material__price_per_unit'), DecimalField()),
                output_field=DecimalField()
            )
        ).aggregate(
            total_cost=Coalesce(Sum('item_cost'), Decimal('0'))
        )['total_cost']

        completed_cases = self.get_cases_in_range().filter(status='closed').count()

        return {
            'gross_margin': self._calculate_margin(total_revenue, total_cost),
            'revenue_per_case': self._calculate_revenue_per_case(total_revenue, completed_cases),
            'outstanding_payments': self.calculate_outstanding_payments()
        }

    def calculate_operational_metrics(self):
        """Calculate operational performance metrics."""
        cases = self.get_cases_in_range()
        return {
            'on_time_completion_rate': self.calculate_on_time_completion_rate(cases),
            'avg_case_duration': self.calculate_average_case_duration(cases),
            'avg_task_load': self.calculate_avg_task_load_per_employee(),
            'capacity_utilization': self.calculate_capacity_utilization(),
            'rework_rate': self.calculate_rework_rate(cases)
        }

    # Helper methods
    def _calculate_efficiency(self, actual, estimated):
        if estimated == Decimal('0'):
            return 100
        return float((actual / estimated) * 100)

    def _calculate_margin(self, revenue, cost):
        if revenue == Decimal('0'):
            return 0
        return float(((revenue - cost) / revenue) * 100)

    def _calculate_revenue_per_case(self, revenue, cases):
        if cases == 0:
            return 0
        return float(revenue) / float(cases)

    def _calculate_waste_rate(self, waste, total):
        if total == Decimal('0'):
            return 0
        return float((waste / total) * 100)

    # Add other calculation methods as needed...

    def calculate_outstanding_payments(self):
        """Calculate total outstanding payments."""
        return Invoice.objects.filter(
            Q(status='unpaid') | Q(status='partial'),
            date__lte=self.end_date
        ).aggregate(
            outstanding=Coalesce(Sum('total_amount'), Decimal('0'))
        )['outstanding']

    def calculate_on_time_completion_rate(self, cases):
        """Calculate percentage of cases completed on time."""
        total_cases = cases.count()
        if total_cases == 0:
            return 0

        # Ndryshimi këtu - përdorim actual_completion në vend të finished_date_time
        on_time_cases = cases.filter(
            actual_completion__lte=F('deadline')
        ).count()
        return (float(on_time_cases) / float(total_cases)) * 100

    def calculate_average_case_duration(self, cases):
        """Calculate average duration of completed cases in days."""
        durations = cases.filter(
            actual_completion__isnull=False
        ).annotate(
            duration=ExpressionWrapper(
                F('actual_completion') - F('received_date_time'),
                output_field=DurationField()
            )
        ).values_list('duration', flat=True)

        if not durations:
            return 0

        total_seconds = sum(d.total_seconds() for d in durations)
        return total_seconds / (len(durations) * 86400)

    def calculate_avg_task_load_per_employee(self):
        """Calculate average number of tasks per active employee."""
        total_tasks = Task.objects.filter(
            schedule_items__start_time__date__range=[self.start_date, self.end_date]
        ).count()

        # total_employees = User.objects.filter(is_active=True).count()

        # if total_employees == 0:
        #     return 0

        # return float(total_tasks) / float(total_employees)

    def calculate_rework_rate(self, cases):
        """Calculate percentage of cases requiring rework."""
        total_cases = cases.count()
        if total_cases == 0:
            return 0

        reworked_cases = cases.filter(status='rework').count()
        return (float(reworked_cases) / float(total_cases)) * 100

    def calculate_capacity_utilization(self):
        """Calculate current capacity utilization percentage."""
        total_capacity = Department.objects.aggregate(
            total_capacity=Coalesce(Sum('capacity'), 0)
        )['total_capacity']

        # Use schedule_items instead of scheduled_start_time
        used_capacity = Task.objects.filter(
            schedule_items__start_time__date__range=[self.start_date, self.end_date],
            status='in_progress'
        ).count()

        if total_capacity == 0:
            return 0

        return (float(used_capacity) / float(total_capacity)) * 100

    def calculate_monthly_kpis(self):
        """Calculate KPIs on a monthly basis."""
        monthly_data = []
        current_date = self.start_date

        while current_date <= self.end_date:
            month_end = (current_date + relativedelta(months=1)) - timedelta(days=1)
            if month_end > self.end_date:
                month_end = self.end_date

            monthly_cases = Case.objects.filter(
                received_date_time__date__range=[current_date, month_end]
            )

            monthly_data.append({
                'month': current_date.strftime('%Y-%m'),
                'case_progress': self.calculate_case_progress(monthly_cases),
                'on_time_completion_rate': self.calculate_on_time_completion_rate(monthly_cases),
                # Add other KPIs as needed
            })

            current_date += relativedelta(months=1)

        return monthly_data

    def get_all_metrics(self):
        """Get all metrics in one call"""
        try:
            cases = self.get_cases_in_range()
            material_metrics = self.calculate_material_metrics()
            financial_metrics = self.calculate_financial_metrics()
            operational_metrics = self.calculate_operational_metrics()

            return {
                'case_progress': self.calculate_case_progress(cases),
                'task_efficiency': self.calculate_task_efficiency(),
                'monthly_kpis': self.calculate_monthly_kpis(),
                **material_metrics,
                **financial_metrics,
                **operational_metrics
            }
        except Exception as e:
            logger.error(f"Error calculating metrics: {str(e)}")
            raise

    def get_all_metrics(self):
            """Get all metrics in one call"""
            try:
                cases = self.get_cases_in_range()
                material_metrics = self.calculate_material_metrics()
                financial_metrics = self.calculate_financial_metrics()
                operational_metrics = self.calculate_operational_metrics()

                return {
                    'case_progress': self.calculate_case_progress(cases),
                    'task_efficiency': self.calculate_task_efficiency(),
                    **material_metrics,
                    **financial_metrics,
                    **operational_metrics
                }
            except Exception as e:
                logger.error(f"Error calculating metrics: {str(e)}")
                raise

def get_performance_metrics(start_date=None, end_date=None):
    """Get all performance metrics for the dashboard."""
    try:
        calculator = PerformanceCalculator(start_date, end_date)
        metrics = calculator.get_all_metrics()
        return metrics, None

    except Exception as e:
        logger.error(f"Error calculating performance metrics: {str(e)}")
        return None, str(e)

# --- WORKFLOW STAGE VIEWS ---
from .forms import WorkflowStageForm

class WorkflowStageListView(LoginRequiredMixin, ListView):
    model = WorkflowStage
    template_name = 'case/workflow_stage_list.html'
    context_object_name = 'workflow_stages'

    def get_queryset(self):
        queryset = super().get_queryset()

        # Get filter parameters from request
        search_query = self.request.GET.get('search', '')
        department_id = self.request.GET.get('department', '')
        critical = self.request.GET.get('critical', '')

        # Apply filters
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(department__name__icontains=search_query) |
                Q(workflow__name__icontains=search_query)
            )

        if department_id:
            queryset = queryset.filter(department_id=department_id)

        if critical == 'true':
            queryset = queryset.filter(is_critical=True)
        elif critical == 'false':
            queryset = queryset.filter(is_critical=False)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Add departments for the filter dropdown
        context['departments'] = Department.objects.filter(is_active=True)

        # Add current filter values to context
        context['search_query'] = self.request.GET.get('search', '')
        context['department_filter'] = self.request.GET.get('department', '')
        context['critical_filter'] = self.request.GET.get('critical', '')

        return context

class WorkflowStageDetailView(LoginRequiredMixin, DetailView):
    model = WorkflowStage
    template_name = 'case/workflow_stage_detail.html'
    context_object_name = 'workflow_stage'

class WorkflowStageCreateView(LoginRequiredMixin, CreateView):
    model = WorkflowStage
    form_class = WorkflowStageForm
    template_name = 'case/workflow_stage_create.html'
    success_url = reverse_lazy('case:workflow_stage_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        return super().form_valid(form)

class WorkflowStageUpdateView(LoginRequiredMixin, UpdateView):
    model = WorkflowStage
    form_class = WorkflowStageForm
    template_name = 'case/workflow_stage_form.html'
    success_url = reverse_lazy('case:workflow_stage_list')

class WorkflowStageDeleteView(LoginRequiredMixin, DeleteView):
    model = WorkflowStage
    template_name = 'case/confirm_delete.html'
    success_url = reverse_lazy('case:workflow_stage_list')
    context_object_name = 'object'

# --- CASE VIEWS ---
from .forms import CaseForm, DentistSimpleCaseForm

class DentistCaseCreateView(LoginRequiredMixin, CreateView):
    model = Case
    form_class = DentistSimpleCaseForm
    template_name = 'create_case.html'
    success_url = reverse_lazy("dentist_dashboard")

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    # No need to override form_valid; form handles all business logic

# --- TASK VIEWS ---
from .forms import TaskForm

@login_required
@permission_required('case.add_task', raise_exception=True)
def task_create(request, case_id):
    case = get_object_or_404(Case, pk=case_id)
    try:
        if request.method == 'POST':
            form = TaskForm(request.POST, request.FILES, user=request.user)
            if form.is_valid():
                task = form.save(commit=False)
                task.case = case
                task.created_by = request.user
                task.save()
                messages.success(request, _('Task created successfully.'))
                return redirect('case:case_detail', case_number=case.case_number)
            else:
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f"{field}: {error}")
        else:
            form = TaskForm(user=request.user, initial={'case': case})
        context = {
            'form': form,
            'case': case,
            'title': f'Create Task for Case #{case.case_number}'
        }
        return render(request, 'case/task_create.html', context)
    except Exception as e:
        messages.error(request, str(e))
        return redirect('case:case_detail', case_number=case.case_number)

@login_required
def task_update(request, task_id):
    task = get_object_or_404(Task, pk=task_id)
    try:
        if request.method == 'POST':
            form = TaskForm(request.POST, request.FILES, instance=task, user=request.user)
            if form.is_valid():
                task = form.save()
                messages.success(request, _('Task updated successfully.'))
                return redirect('case:case_detail', case_number=task.case.case_number)
            else:
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f"{field}: {error}")
        else:
            form = TaskForm(instance=task, user=request.user)
        context = {
            'form': form,
            'task': task,
            'title': f'Update Task: {task.title}'
        }
        return render(request, 'case/task_update.html', context)
    except Exception as e:
        messages.error(request, str(e))
        return redirect('case:task_list')

# This is a duplicate of the case_list view defined earlier in the file.
# Keeping this commented out to avoid confusion.

@login_required
@permission_required('case.view_case', raise_exception=True)
def case_list(request):
    """
    Display a filtered, paginated list of cases.
    """
    try:
        # Initialize filter form with request data
        filter_form = CaseFilterForm(request.GET or None)

        # Base queryset with optimized joins
        cases = Case.objects.select_related(
            'patient', 'dentist', 'responsible_department', 'current_stage'
        ).prefetch_related(
            'invoice'
        ).annotate(
            items_count=Count('items', distinct=True),
            open_tasks_count=Count(
                'tasks',
                filter=~Q(tasks__status='completed'),
                distinct=True
            )
        ).order_by('-case_number')

        # Apply filters if provided
        if filter_form.is_valid():
            # Status filter
            status_filter = filter_form.cleaned_data.get('status')
            if status_filter:
                cases = cases.filter(status=status_filter)

            # Department filter
            department_filter = filter_form.cleaned_data.get('department')
            if department_filter:
                cases = cases.filter(responsible_department=department_filter)

            # Priority filter
            priority_filter = filter_form.cleaned_data.get('priority')
            if priority_filter:
                cases = cases.filter(priority=priority_filter)

            # Case number filter
            case_number_filter = filter_form.cleaned_data.get('case_number')
            if case_number_filter:
                cases = cases.filter(case_number__icontains=case_number_filter)

            # Date range filters
            start_date = filter_form.cleaned_data.get('start_date')
            end_date = filter_form.cleaned_data.get('end_date')

            if start_date:
                cases = cases.filter(received_date_time__date__gte=start_date)

            if end_date:
                cases = cases.filter(received_date_time__date__lte=end_date)

            # Search filter
            search_query = filter_form.cleaned_data.get('search')
            if search_query:
                cases = cases.filter(
                    Q(case_number__icontains=search_query) |
                    Q(patient__first_name__icontains=search_query) |
                    Q(patient__last_name__icontains=search_query) |
                    Q(dentist__first_name__icontains=search_query) |
                    Q(dentist__last_name__icontains=search_query) |
                    Q(dentist__clinic_name__icontains=search_query) |
                    Q(responsible_department__name__icontains=search_query)
                ).distinct()

        # Get page size from form or use default
        page_size = 10  # Default page size
        if filter_form.is_valid() and filter_form.cleaned_data.get('page_size'):
            try:
                page_size = int(filter_form.cleaned_data.get('page_size'))
            except (ValueError, TypeError):
                pass

        # Pagination
        paginator = Paginator(cases, page_size)
        page = request.GET.get('page', 1)
        try:
            cases = paginator.page(page)
        except:
            cases = paginator.page(1)

        context = {
            'cases': cases,
            'filter_form': filter_form,
            'status_choices': Case.STATUS_CHOICES,
            'priority_choices': Case.PRIORITY_CHOICES,
            'departments': Department.objects.filter(is_active=True),
            'page_sizes': [10, 25, 50, 100],
            'current_page_size': page_size
        }
        return render(request, 'case/case_list.html', context)
    except Exception as e:
        messages.error(request, f"Error loading cases: {str(e)}")
        return redirect('home')

@login_required
@permission_required('case.view_case', raise_exception=True)
def case_detail(request, case_number):
    """
    Display detailed information about a case.
    """
    try:
        case = get_object_or_404(
            Case.objects.select_related(
                'patient', 'dentist', 'responsible_department', 'current_stage'
            ).prefetch_related(
                'tasks', 'items', 'selected_teeth', 'stagehistory_set', 'invoice'
            ),
            case_number=case_number
        )

        # Get selected teeth numbers for highlighting in the dental chart
        selected_teeth_numbers = list(case.selected_teeth.values_list('tooth_number', flat=True))
        selected_teeth_str = ','.join(str(num) for num in selected_teeth_numbers)

        # Get all tasks for this case
        tasks = case.tasks.all()

        # Get all tryouts for this case
        tryouts = case.tryouts.all() if hasattr(case, 'tryouts') else []

        # Calculate progress percentage
        total_items = case.case_items.count()
        completed_items = case.case_items.filter(status='completed').count()
        progress = (completed_items / total_items * 100) if total_items > 0 else 0

        # Calculate task statistics
        task_stats = {
            'total': tasks.count(),
            'completed': tasks.filter(status='completed').count(),
            'in_progress': tasks.filter(status='in_progress').count(),
            'pending': tasks.filter(status='pending').count(),
            'priority': {
                'urgent': tasks.filter(priority=4).count(),
                'high': tasks.filter(priority=3).count(),
                'medium': tasks.filter(priority=2).count(),
                'low': tasks.filter(priority=1).count(),
            }
        }

        # Calculate case item statistics
        item_stats = {
            'total': total_items,
            'completed': completed_items,
            'in_progress': case.case_items.filter(status='in_progress').count(),
            'pending': case.case_items.filter(status='pending').count(),
        }

        # Get current date for deadline comparisons
        now = timezone.now()

        context = {
            'case': case,
            'tasks': tasks,
            'tryouts': tryouts,
            'items': case.items.all(),
            'teeth': case.selected_teeth.all(),
            'stage_history': case.stagehistory_set.all().order_by('-start_date_time'),
            'title': f'Case #{case.case_number}',
            'selected_teeth_numbers': selected_teeth_numbers,
            'selected_teeth_str': selected_teeth_str,
            'progress': progress,
            'task_stats': task_stats,
            'item_stats': item_stats,
            'now': now,
        }
        return render(request, 'case/case_detail.html', context)
    except Exception as e:
        messages.error(request, f"Error loading case details: {str(e)}")
        return redirect('case:case_list')

@login_required
@permission_required('case.add_case', raise_exception=True)
def case_create(request):
    """
    Create a new case.
    """
    try:
        if request.method == 'POST':
            form = CaseForm(request.POST, request.FILES, user=request.user)
            formset = CaseItemFormSet(request.POST, prefix='case_items', queryset=CaseItem.objects.none())

            if form.is_valid() and formset.is_valid():
                with transaction.atomic():
                    case = form.save(commit=False)
                    case.created_by = request.user
                    case.save()
                    form.save_m2m()  # Save many-to-many relationships

                    # Explicitly call process_teeth_selection to handle teeth selection
                    form.process_teeth_selection(case)

                    # Log the teeth selection for debugging
                    logger.debug(f"Processing teeth selection for case #{case.case_number}")
                    logger.debug(f"Selected teeth input: {form.cleaned_data.get('selected_teeth_input')}")

                    for case_item_form in formset:
                        if case_item_form.cleaned_data and not case_item_form.cleaned_data.get('DELETE', False):
                            case_item = case_item_form.save(commit=False)
                            case_item.case = case
                            case_item.save()

                    messages.success(request, _('Case successfully created.'))
                    return redirect('case:case_detail', case_number=case.case_number)
            else:
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f"{field}: {error}")
        else:
            form = CaseForm(user=request.user)
            formset = CaseItemFormSet(prefix='case_items', queryset=CaseItem.objects.none())

        # Get all patients for the dropdown
        patients = Patient.objects.all().order_by('first_name', 'last_name')

        context = {
            'form': form,
            'formset': formset,
            'patients': patients,
            'title': _('Create New Case')
        }
        return render(request, 'case/case_create.html', context)
    except Exception as e:
        messages.error(request, f"Error creating case: {str(e)}")
        return redirect('case:case_list')

@login_required
@permission_required('case.change_case', raise_exception=True)
def case_update(request, case_number):
    """
    Update an existing case.
    """
    try:
        case = get_object_or_404(Case, case_number=case_number)

        if request.method == 'POST':
            # Pass the prefix to match the management form fields
            form = CaseForm(request.POST, request.FILES, instance=case, user=request.user)
            formset = CaseItemFormSet(
                request.POST,
                prefix='case_items',  # Make sure prefix matches what's in the template
                queryset=case.case_items.all()
            )
            # Vendos instance-in në formset për të siguruar që items e reja lidhen me case-in
            formset.instance = case

            # For debugging
            print("Form is valid:", form.is_valid())
            print("Formset is valid:", formset.is_valid())
            if not form.is_valid():
                print("Form errors:", form.errors)
            if not formset.is_valid():
                print("Formset errors:", formset.errors)
                print("Formset non-form errors:", formset.non_form_errors())

            if form.is_valid() and formset.is_valid():
                with transaction.atomic():
                    # First save the case
                    updated_case = form.save()

                    # Explicitly process teeth selection
                    # Get teeth input directly from POST data
                    teeth_input = request.POST.get('selected_teeth_input')
                    print(f"Teeth input from POST: {teeth_input}")
                    logger.debug(f"Teeth input from POST: {teeth_input}")

                    # Manually process teeth
                    deleted_count = CaseTeeth.objects.filter(case=updated_case).delete()[0]
                    logger.debug(f"Deleted {deleted_count} existing teeth associations for case #{updated_case.case_number}")

                    if teeth_input and teeth_input.strip():
                        teeth_numbers = [int(num.strip()) for num in teeth_input.split(',')
                                        if num.strip() and num.strip().isdigit()]

                        # Get the current teeth before processing (for logging)
                        current_teeth = list(case.selected_teeth.values_list('tooth_number', flat=True))

                        # Log changes
                        added = [num for num in teeth_numbers if num not in current_teeth]
                        removed = [num for num in current_teeth if num not in teeth_numbers]
                        logger.debug(f"New teeth selection: {teeth_numbers}")
                        logger.debug(f"Teeth added: {added}")
                        logger.debug(f"Teeth removed: {removed}")

                        for number in teeth_numbers:
                            tooth, created = Tooth.objects.get_or_create(
                                tooth_number=number,
                                defaults={'tooth_name': str(number)}
                            )
                            CaseTeeth.objects.create(case=updated_case, tooth=tooth)
                            logger.debug(f"Created tooth association for number {number} (new tooth: {created})")

                        logger.debug(f"Created {len(teeth_numbers)} tooth associations for case #{updated_case.case_number}")
                    else:
                        logger.warning(f"No teeth selected for case #{updated_case.case_number}")

                    # Process formset
                    # Ruaj format ekzistuese dhe krijo objekte të reja pa i ruajtur ende
                    instances = formset.save(commit=False)

                    # Për çdo instance të re, vendos case_id
                    for instance in instances:
                        if not instance.case_id:  # Nëse case_id nuk është vendosur
                            instance.case = updated_case  # Vendos case-in
                        instance.save()  # Ruaj instance-in

                    # Fshi format e shënuara për fshirje
                    for obj in formset.deleted_objects:
                        obj.delete()

                    messages.success(request, _('Case updated successfully.'))
                    return redirect('case:case_detail', case_number=updated_case.case_number)
            else:
                # Show form errors clearly
                for field, errors in form.errors.items():
                    for error in errors:
                        messages.error(request, f"{field}: {error}")

                # Also check formset errors
                for i, form_errors in enumerate(formset.errors):
                    for field, errors in form_errors.items():
                        for error in errors:
                            messages.error(request, f"Item {i+1}, {field}: {error}")

                # Non-form errors in formset
                for error in formset.non_form_errors():
                    messages.error(request, f"Items Error: {error}")
        else:
            form = CaseForm(instance=case, user=request.user)
            formset = CaseItemFormSet(
                prefix='case_items',  # Make sure this matches what's in the template
                queryset=case.case_items.all()
            )
            # Vendos instance-in në formset për të siguruar që items e reja lidhen me case-in
            formset.instance = case

            # Ensure initial value is set for teeth
            teeth = case.selected_teeth.all()
            if teeth.exists():
                teeth_numbers = list(teeth.values_list('tooth_number', flat=True))
                form.initial['selected_teeth_input'] = ','.join(str(t) for t in teeth_numbers)

        # Get selected teeth numbers for highlighting in the dental chart
        selected_teeth_numbers = list(case.selected_teeth.values_list('tooth_number', flat=True))
        selected_teeth_str = ','.join(str(num) for num in selected_teeth_numbers)

        context = {
            'form': form,
            'formset': formset,
            'case': case,
            'title': f'Update Case #{case.case_number}',
            'selected_teeth_numbers': selected_teeth_numbers,
            'selected_teeth_str': selected_teeth_str,
            'selected_teeth_list': selected_teeth_numbers  # Add the teeth list directly to context for JS initialization
        }
        return render(request, 'case/case_update.html', context)
    except Exception as e:
        import traceback
        print(f"Exception in case_update: {str(e)}")
        print(traceback.format_exc())  # Print full traceback for debugging
        logger.error(f"Error updating case: {str(e)}", exc_info=True)
        messages.error(request, f"Error updating case: {str(e)}")
        return redirect('case:case_list')

@login_required
@permission_required('case.delete_case', raise_exception=True)
def case_delete(request, case_number):
    """
    Delete a case.
    """
    try:
        case = get_object_or_404(Case, case_number=case_number)

        if request.method == 'POST':
            case_number = case.case_number  # Store for message
            case.delete()
            messages.success(request, _(f'Case #{case_number} deleted successfully.'))
            return redirect('case:case_list')

        context = {
            'case': case,
            'title': f'Delete Case #{case.case_number}'
        }
        return render(request, 'case/case_confirm_delete.html', context)
    except Exception as e:
        messages.error(request, f"Error deleting case: {str(e)}")
        return redirect('case:case_list')

@login_required
@permission_required('case.change_case', raise_exception=True)
def case_status_update(request, case_number):
    """
    Update the status of a case.
    """
    try:
        case = get_object_or_404(Case, case_number=case_number)

        if request.method == 'POST':
            new_status = request.POST.get('status')
            if new_status and new_status in dict(Case.STATUS_CHOICES):
                # Use the service layer to update the status
                CaseService.update_case_status(case, new_status, request.user)
                messages.success(request, _(f'Case status updated to {dict(Case.STATUS_CHOICES)[new_status]}.'))
            else:
                messages.error(request, _('Invalid status provided.'))

            # Redirect back to the case detail page
            return redirect('case:case_detail', case_number=case.case_number)

        # If not POST, redirect to case detail
        return redirect('case:case_detail', case_number=case.case_number)
    except Exception as e:
        messages.error(request, f"Error updating case status: {str(e)}")
        return redirect('case:case_list')

#         # Apply filters from filter_form if valid
#         if filter_form.is_valid():
#             cases = filter_form.filter_queryset(cases)

#         # Get departments for filter
#         departments = Department.objects.filter(is_active=True)

#         # Get case statistics
#         stats = {
#             'total': cases.count(),
#             'in_progress': cases.filter(status='in_progress').count(),
#             'completed': cases.filter(status='completed').count(),
#             'delayed': cases.filter(
#                 Q(deadline__lt=timezone.now()) & ~Q(status__in=['completed', 'cancelled'])
#             ).count()
#         }

#         # Pagination
#         page_size = int(request.GET.get('page_size', 20))
#         paginator = Paginator(cases, page_size)
#         page = request.GET.get('page', 1)

#         try:
#             cases_page = paginator.page(page)
#         except (PageNotAnInteger, EmptyPage):
#             cases_page = paginator.page(1)

#         context = {
#             'cases': cases_page,
#             'filter_form': filter_form,
#             'departments': departments,
#             'status_choices': Case.STATUS_CHOICES,
#             'priority_choices': Case.PRIORITY_CHOICES,
#             'stats': stats,
#             'page_sizes': [10, 20, 50, 100],
#             'current_page_size': page_size
#         }

#         # Add export functionality if requested
#         if request.GET.get('export') == 'csv':
#             return export_cases_to_csv(cases)

#         return render(request, 'case/case_list.html', context)

#     except Exception as e:
#         logger.error(f"Error in case_list view: {str(e)}", exc_info=True)
#         messages.error(request, "An error occurred while loading the case list. Please try again later.")
#         return render(request, 'case/case_list.html', {'error': str(e)})


def case_gantt_view(request):
    try:
        # Get filter parameters
        start_date_str = request.GET.get('start_date')
        department_id = request.GET.get('department')
        status = request.GET.get('status')
        priority = request.GET.get('priority')
        page_size = int(request.GET.get('page_size', 25))

        from django.utils import timezone as django_timezone
        import json

        # Set date range
        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            except ValueError:
                start_date = django_timezone.now().date() - timedelta(days=30)
        else:
            start_date = django_timezone.now().date() - timedelta(days=30)

        end_date = start_date + timedelta(days=30)

        # Base queryset
        cases = Case.objects.select_related(
            'patient',
            'dentist',
            'current_stage',
            'responsible_department'
        ).prefetch_related(
            'tasks',
            'items',
            'selected_teeth',
            'stagehistory_set'
        )

        # Date range filter
        cases = cases.filter(
            Q(received_date_time__date__lte=end_date) &
            (Q(ship_date_time__date__gte=start_date) | Q(ship_date_time__isnull=True))
        )

        # Apply filters
        if department_id:
            cases = cases.filter(responsible_department_id=department_id)

        if status:
            cases = cases.filter(status=status)

        if priority:
            cases = cases.filter(priority=priority)

        # Analytics
        analytics = {
            'total_cases': cases.count(),
            'completed_cases': cases.filter(status='completed').count(),
            'in_progress': cases.filter(status='in_progress').count(),
            'delayed_cases': cases.filter(
                Q(deadline__lt=django_timezone.now()) &
                ~Q(status__in=['completed', 'delivered', 'cancelled'])
            ).count()
        }

        # Build Gantt data for Frappe Gantt
        gantt_tasks = []
        for case in cases:
            # Determine case start and end dates
            case_start = case.received_date_time.strftime('%Y-%m-%d') if case.received_date_time else start_date.strftime('%Y-%m-%d')
            case_end = case.ship_date_time.strftime('%Y-%m-%d') if case.ship_date_time else (django_timezone.now() + timedelta(days=5)).strftime('%Y-%m-%d')

            # Check if case is overdue
            is_overdue = False
            if case.deadline:
                # Convert deadline to date if it's a datetime
                deadline_date = case.deadline.date() if hasattr(case.deadline, 'date') else case.deadline
                current_date = django_timezone.now().date()

                if deadline_date < current_date and case.status not in ['completed', 'delivered', 'cancelled']:
                    is_overdue = True

            # Create unique ID for the case
            case_id = f"case-{case.case_number}"

            # Get status and priority display names
            status_display = dict(Case.STATUS_CHOICES).get(case.status, case.status)
            priority_display = dict(Case.PRIORITY_CHOICES).get(case.priority, case.priority)

            # Create case task object
            case_task = {
                'id': case_id,
                'name': f"Case #{case.case_number}",
                'start': case_start,
                'end': case_end,
                'progress': 100 if case.status == 'completed' else (50 if case.status == 'in_progress' else 0),
                'custom_class': f"case-{case.status}",
                'details': {
                    'type': 'case',
                    'case_number': case.case_number,
                    'status': case.status,
                    'status_display': status_display,
                    'priority': case.priority,
                    'priority_display': priority_display,
                    'patient': str(case.patient) if case.patient else 'No Patient',
                    'dentist': str(case.dentist) if case.dentist else 'No Dentist',
                    'department': str(case.responsible_department) if case.responsible_department else 'No Department',
                    'deadline': case.deadline.strftime('%Y-%m-%d') if case.deadline else 'Not set',
                    'is_overdue': is_overdue
                }
            }
            gantt_tasks.append(case_task)

            # Add stages as subtasks
            for stage in case.stagehistory_set.all().order_by('start_date_time'):
                if stage.start_date_time:
                    stage_start = stage.start_date_time.strftime('%Y-%m-%d')
                    stage_end = stage.end_date_time.strftime('%Y-%m-%d') if stage.end_date_time else case_end

                    # Calculate progress for the stage
                    progress = 100 if stage.end_date_time else 50 if stage.start_date_time else 0

                    # Create stage task object
                    gantt_tasks.append({
                        'id': f"stage-{stage.id}",
                        'name': stage.stage.name if stage.stage else 'Unknown Stage',
                        'start': stage_start,
                        'end': stage_end,
                        'progress': progress,
                        'custom_class': 'gantt-task-stage',
                        'parent': case_id,
                        'details': {
                            'type': 'stage',
                            'stage_name': stage.stage.name if stage.stage else 'Unknown Stage',
                            'case_number': case.case_number,
                            'department': str(stage.stage.department) if stage.stage and stage.stage.department else 'No Department',
                            'start_date': stage_start,
                            'end_date': stage_end,
                        }
                    })

        # Import Django's JSON encoder to handle translation proxies
        from django.core.serializers.json import DjangoJSONEncoder

        context = {
            'gantt_tasks_json': json.dumps(gantt_tasks, cls=DjangoJSONEncoder),
            'cases': cases,
            'analytics': analytics,
            'departments': Department.objects.filter(is_active=True).values('id', 'name'),
            'start_date': start_date,
            'end_date': end_date,
            'selected_department': department_id,
            'selected_status': status,
            'selected_priority': priority,
            'page_size': page_size,
            'case_statuses': Case.STATUS_CHOICES,
            'case_priorities': Case.PRIORITY_CHOICES
        }

        return render(request, 'case/gantt_view.html', context)

    except Exception as e:
        from django.utils import timezone as django_timezone
        messages.error(request, f"An error occurred: {str(e)}")
        return render(request, 'case/gantt_view.html', {
            'error': str(e),
            'start_date': django_timezone.now().date() - timedelta(days=30),
            'end_date': django_timezone.now().date(),
        })

@login_required
def debug_teeth_selection(request):
    """Temporary view to debug teeth selection"""
    context = {
        'post_data': None,
        'selected_teeth': None
    }

    if request.method == 'POST':
        # Get all selected_teeth_input values
        selected_teeth = request.POST.getlist('selected_teeth_input')
        print(f"Debug - Received teeth: {selected_teeth}")

        # Log all POST data
        print("All POST data:")
        for key, values in request.POST.lists():
            print(f"{key}: {values}")

        context['post_data'] = dict(request.POST)
        context['selected_teeth'] = selected_teeth

    return render(request, 'case/debug_teeth.html', context)


# API endpoint to fetch case items for a specific case
@login_required
def case_items_api(request, case_id):
    """API endpoint to fetch case items for a specific case."""
    try:
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"API called for case_id: {case_id}")

        case = get_object_or_404(Case, pk=case_id)
        case_items = CaseItem.objects.filter(case=case)
        logger.info(f"Found {case_items.count()} case items for case #{case.case_number}")

        # Prepare data for JSON response
        items_data = [{
            'id': item.id,
            'item_name': item.item.name,
            'quantity': item.quantity,
            'unit': item.unit.name if item.unit else '',
            'status': item.status
        } for item in case_items]

        logger.info(f"Returning {len(items_data)} items: {items_data}")
        return JsonResponse(items_data, safe=False)
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in case_items_api: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)
