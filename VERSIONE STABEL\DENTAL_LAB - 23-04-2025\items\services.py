"""
Material Requirement Planning (MRP) and Inventory Management Services
"""

from django.db import transaction
from django.utils import timezone
from django.db.models import Sum, F, Q, Prefetch
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

from .models import (
    RawMaterial, Item, Inventory, RawMaterialInventory,
    RawMaterialInventoryHistory, ItemRawMaterial, Currency, ExchangeRate,
    Supplier, SupplierRawMaterial
)

logger = logging.getLogger(__name__)


@dataclass
class MaterialRequirement:
    """Represents material requirement for a case or production plan"""
    raw_material: RawMaterial
    required_quantity: Decimal
    available_quantity: Decimal
    shortage_quantity: Decimal
    unit: 'Unit'
    estimated_cost: Decimal
    supplier_options: List[Dict[str, Any]]

    @property
    def is_sufficient(self) -> bool:
        return self.shortage_quantity <= 0

    @property
    def shortage_cost(self) -> Decimal:
        if self.shortage_quantity <= 0:
            return Decimal('0.00')
        return self.shortage_quantity * self.raw_material.price_per_unit


@dataclass
class ProductionPlan:
    """Represents a production plan with material requirements"""
    case_id: Optional[int]
    items: List[Dict[str, Any]]
    material_requirements: List[MaterialRequirement]
    total_cost: Decimal
    feasible: bool
    missing_materials: List[MaterialRequirement]
    estimated_completion_date: Optional[datetime]

    @property
    def total_shortage_cost(self) -> Decimal:
        return sum(req.shortage_cost for req in self.missing_materials)


@dataclass
class PurchaseRecommendation:
    """Represents a purchase recommendation"""
    raw_material: RawMaterial
    recommended_quantity: Decimal
    supplier: Supplier
    estimated_cost: Decimal
    priority: str  # 'urgent', 'high', 'medium', 'low'
    reason: str
    delivery_date: Optional[datetime]


class MaterialRequirementPlanningService:
    """
    Service for Material Requirement Planning (MRP) and inventory management
    """

    def __init__(self):
        self.safety_stock_multiplier = Decimal('1.1')  # 10% safety stock
        self.lead_time_buffer_days = 3  # Extra days for delivery buffer

    def calculate_case_material_requirements(self, case) -> ProductionPlan:
        """
        Calculate material requirements for a specific case

        Args:
            case: Case instance

        Returns:
            ProductionPlan with detailed material requirements
        """
        try:
            logger.info(f"Calculating material requirements for case #{case.case_number}")

            # Get all case items
            case_items = case.case_items.select_related('item', 'unit').prefetch_related(
                'item__itemrawmaterial_set__raw_material',
                'item__itemrawmaterial_set__unit'
            ).all()

            # Calculate material requirements
            material_requirements = {}
            items_data = []

            for case_item in case_items:
                item_data = {
                    'case_item_id': case_item.id,
                    'item': case_item.item,
                    'quantity': case_item.quantity,
                    'unit': case_item.unit,
                    'estimated_cost': case_item.item.cost() * case_item.quantity
                }
                items_data.append(item_data)

                # Calculate raw material requirements for this item
                for item_raw_material in case_item.item.itemrawmaterial_set.all():
                    raw_material = item_raw_material.raw_material
                    required_qty = item_raw_material.quantity * case_item.quantity

                    if raw_material.id in material_requirements:
                        material_requirements[raw_material.id]['required_quantity'] += required_qty
                    else:
                        material_requirements[raw_material.id] = {
                            'raw_material': raw_material,
                            'required_quantity': required_qty,
                            'unit': item_raw_material.unit
                        }

            # Check availability and create MaterialRequirement objects
            requirements_list = []
            missing_materials = []
            total_cost = Decimal('0.00')

            for req_data in material_requirements.values():
                raw_material = req_data['raw_material']
                required_qty = req_data['required_quantity']
                unit = req_data['unit']

                # Get current stock
                available_qty = raw_material.get_current_stock()
                shortage_qty = max(Decimal('0'), required_qty - available_qty)

                # Calculate cost
                estimated_cost = required_qty * raw_material.price_per_unit
                total_cost += estimated_cost

                # Get supplier options
                supplier_options = self._get_supplier_options(raw_material, required_qty)

                # Create MaterialRequirement
                requirement = MaterialRequirement(
                    raw_material=raw_material,
                    required_quantity=required_qty,
                    available_quantity=available_qty,
                    shortage_quantity=shortage_qty,
                    unit=unit,
                    estimated_cost=estimated_cost,
                    supplier_options=supplier_options
                )

                requirements_list.append(requirement)

                if not requirement.is_sufficient:
                    missing_materials.append(requirement)

            # Determine feasibility
            feasible = len(missing_materials) == 0

            # Estimate completion date
            estimated_completion = self._estimate_completion_date(case, missing_materials)

            return ProductionPlan(
                case_id=case.id,
                items=items_data,
                material_requirements=requirements_list,
                total_cost=total_cost,
                feasible=feasible,
                missing_materials=missing_materials,
                estimated_completion_date=estimated_completion
            )

        except Exception as e:
            logger.error(f"Error calculating material requirements for case #{case.case_number}: {str(e)}")
            raise

    def _get_supplier_options(self, raw_material: RawMaterial, required_quantity: Decimal) -> List[Dict[str, Any]]:
        """Get supplier options for a raw material"""
        try:
            supplier_materials = SupplierRawMaterial.objects.filter(
                raw_material=raw_material
            ).select_related('supplier', 'currency').order_by('price_per_unit')

            options = []
            for supplier_material in supplier_materials:
                # Calculate delivery date
                delivery_date = timezone.now() + timedelta(
                    days=supplier_material.delivery_time_days + self.lead_time_buffer_days
                )

                # Calculate total cost
                total_cost = supplier_material.price_per_unit * required_quantity

                options.append({
                    'supplier': supplier_material.supplier,
                    'price_per_unit': supplier_material.price_per_unit,
                    'currency': supplier_material.currency,
                    'delivery_time_days': supplier_material.delivery_time_days,
                    'minimum_order_quantity': supplier_material.minimum_order_quantity,
                    'total_cost': total_cost,
                    'estimated_delivery_date': delivery_date
                })

            return options

        except Exception as e:
            logger.error(f"Error getting supplier options for {raw_material.name}: {str(e)}")
            return []

    def _estimate_completion_date(self, case, missing_materials: List[MaterialRequirement]) -> Optional[datetime]:
        """Estimate completion date based on material availability"""
        try:
            if not missing_materials:
                # If no missing materials, use case's estimated completion
                return case.estimated_completion

            # Find the longest delivery time among missing materials
            max_delivery_days = 0
            for requirement in missing_materials:
                if requirement.supplier_options:
                    # Get the fastest supplier option
                    fastest_option = min(
                        requirement.supplier_options,
                        key=lambda x: x['delivery_time_days']
                    )
                    max_delivery_days = max(max_delivery_days, fastest_option['delivery_time_days'])

            # Add buffer time and production time
            estimated_date = timezone.now() + timedelta(
                days=max_delivery_days + self.lead_time_buffer_days + 2  # 2 days for production
            )

            return estimated_date

        except Exception as e:
            logger.error(f"Error estimating completion date: {str(e)}")
            return None

    def generate_purchase_recommendations(self, days_ahead: int = 30) -> List[PurchaseRecommendation]:
        """
        Generate purchase recommendations based on current stock levels and future needs

        Args:
            days_ahead: Number of days to look ahead for planning

        Returns:
            List of PurchaseRecommendation objects
        """
        try:
            logger.info(f"Generating purchase recommendations for {days_ahead} days ahead")

            recommendations = []

            # Get all raw materials that need reordering
            materials_needing_reorder = RawMaterial.objects.filter(
                is_active=True
            ).prefetch_related('suppliers', 'supplierrawmaterial_set')

            for material in materials_needing_reorder:
                current_stock = material.get_current_stock()
                reorder_point = material.reorder_point

                # Calculate future demand
                future_demand = self._calculate_future_demand(material, days_ahead)

                # Determine if purchase is needed
                total_needed = future_demand + reorder_point * self.safety_stock_multiplier

                if current_stock < total_needed:
                    shortage = total_needed - current_stock

                    # Get best supplier
                    best_supplier = self._get_best_supplier(material, shortage)

                    if best_supplier:
                        # Determine priority
                        priority = self._determine_priority(material, current_stock, reorder_point)

                        # Calculate recommended quantity (consider minimum order quantity)
                        recommended_qty = max(shortage, best_supplier.get('minimum_order_quantity', shortage))

                        # Estimate delivery date
                        delivery_date = timezone.now() + timedelta(
                            days=best_supplier.get('delivery_time_days', 7) + self.lead_time_buffer_days
                        )

                        recommendation = PurchaseRecommendation(
                            raw_material=material,
                            recommended_quantity=recommended_qty,
                            supplier=best_supplier['supplier'],
                            estimated_cost=recommended_qty * best_supplier['price_per_unit'],
                            priority=priority,
                            reason=self._generate_purchase_reason(material, current_stock, reorder_point, future_demand),
                            delivery_date=delivery_date
                        )

                        recommendations.append(recommendation)

            # Sort by priority and cost
            recommendations.sort(key=lambda x: (
                {'urgent': 0, 'high': 1, 'medium': 2, 'low': 3}[x.priority],
                x.estimated_cost
            ))

            return recommendations

        except Exception as e:
            logger.error(f"Error generating purchase recommendations: {str(e)}")
            return []

    def _calculate_future_demand(self, material: RawMaterial, days_ahead: int) -> Decimal:
        """Calculate future demand for a material based on scheduled cases"""
        try:
            from case.models import Case, CaseItem

            # Get cases scheduled for the next period
            future_date = timezone.now() + timedelta(days=days_ahead)

            scheduled_cases = Case.objects.filter(
                estimated_completion__lte=future_date,
                status__in=['pending', 'in_progress']
            ).prefetch_related('case_items__item__itemrawmaterial_set')

            total_demand = Decimal('0')

            for case in scheduled_cases:
                for case_item in case.case_items.all():
                    for item_raw_material in case_item.item.itemrawmaterial_set.filter(
                        raw_material=material
                    ):
                        demand = item_raw_material.quantity * case_item.quantity
                        total_demand += demand

            return total_demand

        except Exception as e:
            logger.error(f"Error calculating future demand for {material.name}: {str(e)}")
            return Decimal('0')

    def _get_best_supplier(self, material: RawMaterial, quantity: Decimal) -> Optional[Dict[str, Any]]:
        """Get the best supplier for a material based on price and delivery time"""
        try:
            supplier_materials = SupplierRawMaterial.objects.filter(
                raw_material=material,
                minimum_order_quantity__lte=quantity
            ).select_related('supplier', 'currency')

            if not supplier_materials.exists():
                return None

            # Score suppliers based on price and delivery time
            best_supplier = None
            best_score = float('inf')

            for supplier_material in supplier_materials:
                # Normalize price (lower is better)
                price_score = float(supplier_material.price_per_unit)

                # Normalize delivery time (lower is better)
                delivery_score = supplier_material.delivery_time_days * 0.1

                # Combined score (weighted)
                total_score = price_score * 0.7 + delivery_score * 0.3

                if total_score < best_score:
                    best_score = total_score
                    best_supplier = {
                        'supplier': supplier_material.supplier,
                        'price_per_unit': supplier_material.price_per_unit,
                        'currency': supplier_material.currency,
                        'delivery_time_days': supplier_material.delivery_time_days,
                        'minimum_order_quantity': supplier_material.minimum_order_quantity
                    }

            return best_supplier

        except Exception as e:
            logger.error(f"Error getting best supplier for {material.name}: {str(e)}")
            return None

    def _determine_priority(self, material: RawMaterial, current_stock: Decimal, reorder_point: Decimal) -> str:
        """Determine purchase priority based on stock levels"""
        if current_stock <= 0:
            return 'urgent'
        elif current_stock <= reorder_point * Decimal('0.5'):
            return 'high'
        elif current_stock <= reorder_point:
            return 'medium'
        else:
            return 'low'

    def _generate_purchase_reason(self, material: RawMaterial, current_stock: Decimal,
                                 reorder_point: Decimal, future_demand: Decimal) -> str:
        """Generate a human-readable reason for the purchase recommendation"""
        if current_stock <= 0:
            return f"Out of stock - immediate purchase required"
        elif current_stock <= reorder_point * Decimal('0.5'):
            return f"Critical low stock: {current_stock} units remaining (reorder point: {reorder_point})"
        elif future_demand > current_stock:
            return f"Insufficient stock for upcoming demand: {future_demand} units needed, {current_stock} available"
        else:
            return f"Below reorder point: {current_stock} units (reorder point: {reorder_point})"


class StockAlertService:
    """
    Service for managing stock alerts and notifications
    """

    def __init__(self):
        self.critical_threshold = Decimal('0.1')  # 10% of reorder point
        self.low_threshold = Decimal('0.5')       # 50% of reorder point

    def check_stock_levels(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Check all stock levels and categorize alerts

        Returns:
            Dictionary with categorized stock alerts
        """
        try:
            logger.info("Checking stock levels for alerts")

            alerts = {
                'critical': [],
                'low': [],
                'reorder': [],
                'overstocked': []
            }

            # Check raw materials
            raw_materials = RawMaterial.objects.filter(is_active=True)

            for material in raw_materials:
                current_stock = material.get_current_stock()
                reorder_point = material.reorder_point

                alert_data = {
                    'type': 'raw_material',
                    'item': material,
                    'current_stock': current_stock,
                    'reorder_point': reorder_point,
                    'percentage': (current_stock / reorder_point * 100) if reorder_point > 0 else 0
                }

                if current_stock <= 0:
                    alert_data['severity'] = 'out_of_stock'
                    alerts['critical'].append(alert_data)
                elif current_stock <= reorder_point * self.critical_threshold:
                    alert_data['severity'] = 'critical'
                    alerts['critical'].append(alert_data)
                elif current_stock <= reorder_point * self.low_threshold:
                    alert_data['severity'] = 'low'
                    alerts['low'].append(alert_data)
                elif current_stock <= reorder_point:
                    alert_data['severity'] = 'reorder'
                    alerts['reorder'].append(alert_data)

            # Check finished items
            items = Item.objects.filter(is_active=True)

            for item in items:
                try:
                    inventory = Inventory.objects.get(item=item)
                    current_stock = inventory.quantity
                    min_stock = item.minimum_stock_level

                    if min_stock > 0:  # Only check items with defined minimum stock
                        alert_data = {
                            'type': 'item',
                            'item': item,
                            'current_stock': current_stock,
                            'minimum_stock': min_stock,
                            'percentage': (current_stock / min_stock * 100) if min_stock > 0 else 0
                        }

                        if current_stock <= 0:
                            alert_data['severity'] = 'out_of_stock'
                            alerts['critical'].append(alert_data)
                        elif current_stock <= min_stock * self.critical_threshold:
                            alert_data['severity'] = 'critical'
                            alerts['critical'].append(alert_data)
                        elif current_stock <= min_stock * self.low_threshold:
                            alert_data['severity'] = 'low'
                            alerts['low'].append(alert_data)
                        elif current_stock <= min_stock:
                            alert_data['severity'] = 'reorder'
                            alerts['reorder'].append(alert_data)

                        # Check for overstocking (if applicable)
                        max_stock = min_stock * 5  # Assume 5x minimum as maximum
                        if current_stock > max_stock:
                            alert_data['severity'] = 'overstocked'
                            alerts['overstocked'].append(alert_data)

                except Inventory.DoesNotExist:
                    # No inventory record - create alert
                    alerts['critical'].append({
                        'type': 'item',
                        'item': item,
                        'current_stock': 0,
                        'minimum_stock': item.minimum_stock_level,
                        'severity': 'no_inventory_record',
                        'percentage': 0
                    })

            return alerts

        except Exception as e:
            logger.error(f"Error checking stock levels: {str(e)}")
            return {'critical': [], 'low': [], 'reorder': [], 'overstocked': []}

    def get_urgent_alerts(self) -> List[Dict[str, Any]]:
        """Get only urgent/critical alerts that need immediate attention"""
        alerts = self.check_stock_levels()
        return alerts['critical']

    def generate_alert_summary(self) -> Dict[str, Any]:
        """Generate a summary of all stock alerts"""
        alerts = self.check_stock_levels()

        return {
            'total_alerts': sum(len(alerts[category]) for category in alerts),
            'critical_count': len(alerts['critical']),
            'low_count': len(alerts['low']),
            'reorder_count': len(alerts['reorder']),
            'overstocked_count': len(alerts['overstocked']),
            'alerts': alerts,
            'generated_at': timezone.now()
        }


class CostTrackingService:
    """
    Service for real-time cost tracking and variance analysis
    """

    def calculate_case_cost_variance(self, case) -> Dict[str, Any]:
        """
        Calculate cost variance between estimated and actual costs for a case

        Args:
            case: Case instance

        Returns:
            Dictionary with cost variance analysis
        """
        try:
            logger.info(f"Calculating cost variance for case #{case.case_number}")

            # Calculate estimated costs
            estimated_cost = Decimal('0.00')
            actual_cost = Decimal('0.00')

            variance_details = []

            for case_item in case.case_items.all():
                item = case_item.item
                quantity = case_item.quantity

                # Estimated cost (based on current material prices)
                item_estimated_cost = item.cost() * quantity
                estimated_cost += item_estimated_cost

                # Actual cost (based on historical material usage)
                item_actual_cost = self._calculate_actual_item_cost(case_item)
                actual_cost += item_actual_cost

                # Calculate variance for this item
                item_variance = item_actual_cost - item_estimated_cost
                variance_percentage = (item_variance / item_estimated_cost * 100) if item_estimated_cost > 0 else 0

                variance_details.append({
                    'case_item': case_item,
                    'estimated_cost': item_estimated_cost,
                    'actual_cost': item_actual_cost,
                    'variance': item_variance,
                    'variance_percentage': variance_percentage
                })

            # Calculate total variance
            total_variance = actual_cost - estimated_cost
            total_variance_percentage = (total_variance / estimated_cost * 100) if estimated_cost > 0 else 0

            return {
                'case': case,
                'estimated_cost': estimated_cost,
                'actual_cost': actual_cost,
                'total_variance': total_variance,
                'variance_percentage': total_variance_percentage,
                'variance_details': variance_details,
                'analysis_date': timezone.now()
            }

        except Exception as e:
            logger.error(f"Error calculating cost variance for case #{case.case_number}: {str(e)}")
            return {}

    def _calculate_actual_item_cost(self, case_item) -> Decimal:
        """Calculate actual cost of an item based on historical material usage"""
        try:
            # For now, use the current cost calculation
            # In a more advanced implementation, this would track actual material consumption
            return case_item.item.cost() * case_item.quantity

        except Exception as e:
            logger.error(f"Error calculating actual item cost: {str(e)}")
            return Decimal('0.00')
