../../Scripts/pyhanko.exe,sha256=2n8NCLJjOPZMteJeMNoANhU5ZtdSQrnomIzOx3LMfFg,108436
pyhanko-0.28.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyhanko-0.28.0.dist-info/METADATA,sha256=G-ilAcQL6PWWB5LwM6cSj1XozffWNapjVotKC0zBocY,7657
pyhanko-0.28.0.dist-info/RECORD,,
pyhanko-0.28.0.dist-info/WHEEL,sha256=zaaOINJESkSfm_4HQVc5ssNzHCPXhJm0kEUakpsEHaU,91
pyhanko-0.28.0.dist-info/entry_points.txt,sha256=b0jU5S7K1tkTKcY3pImAO9_bYgha0fr36QZyEb2D8VE,52
pyhanko-0.28.0.dist-info/licenses/LICENSE,sha256=ppfnWrKHvGvEiG2mIZiiuZMKYvGtNMGR9Y1VbYYlwGI,1080
pyhanko-0.28.0.dist-info/licenses/src/pyhanko/pdf_utils/LICENSE.PyPDF2,sha256=8EvYkTHRNGo0vU2UspJ9oe-SW9zyng3TxjDWEKbQhQU,1726
pyhanko-0.28.0.dist-info/top_level.txt,sha256=2Z0D0SOjWu7_9ud976jTS2ZCzzNQlb5QOQ0OVBvZksM,8
pyhanko/__init__.py,sha256=dLAwzotOjMTfS31pciKplHyH8GBO8V1daYpgsbT8X2s,98
pyhanko/__main__.py,sha256=r_wpx5NBClHu_Vi6IyBsGeo9HZipV73UzPw3u7vEZos,123
pyhanko/__pycache__/__init__.cpython-313.pyc,,
pyhanko/__pycache__/__main__.cpython-313.pyc,,
pyhanko/__pycache__/keys.cpython-313.pyc,,
pyhanko/__pycache__/stamp.cpython-313.pyc,,
pyhanko/__pycache__/version.cpython-313.pyc,,
pyhanko/cli/__init__.py,sha256=W-dXL7Ks_h_f83BydK6DO0Xq1z2vXC7imWns9knvgVA,336
pyhanko/cli/__pycache__/__init__.cpython-313.pyc,,
pyhanko/cli/__pycache__/_ctx.cpython-313.pyc,,
pyhanko/cli/__pycache__/_root.cpython-313.pyc,,
pyhanko/cli/__pycache__/_trust.cpython-313.pyc,,
pyhanko/cli/__pycache__/config.cpython-313.pyc,,
pyhanko/cli/__pycache__/plugin_api.cpython-313.pyc,,
pyhanko/cli/__pycache__/runtime.cpython-313.pyc,,
pyhanko/cli/__pycache__/utils.cpython-313.pyc,,
pyhanko/cli/_ctx.py,sha256=57uPbIEnUdIF8Ev3kTs1925h2kXXjMx-tcrgJS3HGUM,2428
pyhanko/cli/_root.py,sha256=pygtTuyChs8JSL2o_du8zTtjZR7LciObAuVhtoKgmqs,5287
pyhanko/cli/_trust.py,sha256=Mb3iuM05AnLvQ7fH1df-TOzTaDk430grHOiSxMR2Cvk,5250
pyhanko/cli/commands/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyhanko/cli/commands/__pycache__/__init__.cpython-313.pyc,,
pyhanko/cli/commands/__pycache__/crypt.cpython-313.pyc,,
pyhanko/cli/commands/__pycache__/fields.cpython-313.pyc,,
pyhanko/cli/commands/__pycache__/stamp.cpython-313.pyc,,
pyhanko/cli/commands/crypt.py,sha256=wF7qUabC48f1WiPEXNbrewCdWG4_Yjy3XptbCe9CQv8,7433
pyhanko/cli/commands/fields.py,sha256=__7BfAbqhYw9Om868mM1NKeaOGzBBx8TejaQ-2g5AiY,2118
pyhanko/cli/commands/signing/__init__.py,sha256=bS6RCDM3Ze7u5nfrYdGLISIOy_BujItQ5Zkhqexxk3w,9075
pyhanko/cli/commands/signing/__pycache__/__init__.cpython-313.pyc,,
pyhanko/cli/commands/signing/__pycache__/pkcs11_cli.cpython-313.pyc,,
pyhanko/cli/commands/signing/__pycache__/plugin.cpython-313.pyc,,
pyhanko/cli/commands/signing/__pycache__/simple.cpython-313.pyc,,
pyhanko/cli/commands/signing/__pycache__/utils.cpython-313.pyc,,
pyhanko/cli/commands/signing/pkcs11_cli.py,sha256=2bn9pQXImL_iSiH2lrDulHyL9aY9m2_V09zoWw_-A8w,5509
pyhanko/cli/commands/signing/plugin.py,sha256=vSNa6cPHIkF9566t_N7908dp1vxtP3c_yjdvQDcXws8,5515
pyhanko/cli/commands/signing/simple.py,sha256=_vImPQ9Bx83DdPzCBgU0MhErkCn7m16QhEv78K6t_-M,8600
pyhanko/cli/commands/signing/utils.py,sha256=gTtqVMbTBWiKOuXm_tTpYu3W4bzsDMiQ9ybfi8l29N8,2054
pyhanko/cli/commands/stamp.py,sha256=e321axB3qB48vXtIqyEvjRBVFCiG88joK9fbgUfAwuI,2825
pyhanko/cli/commands/validation/__init__.py,sha256=b595plA9s5lCNLb9o25MQ-MMdLZGd0ojxJ0GRTQlAlc,43
pyhanko/cli/commands/validation/__pycache__/__init__.cpython-313.pyc,,
pyhanko/cli/commands/validation/__pycache__/ltv.cpython-313.pyc,,
pyhanko/cli/commands/validation/__pycache__/validate.cpython-313.pyc,,
pyhanko/cli/commands/validation/ltv.py,sha256=NNGNq-AgO32e4H8sEe914g2gp4AAsL8P0qKV6xEr4sE,3615
pyhanko/cli/commands/validation/validate.py,sha256=nqAYXAjgY9vFkZliWAriv_i0H8QRzAmKxb_Q_woDgN0,10875
pyhanko/cli/config.py,sha256=TH604IupVilLrj1mhc59dPfRCcjD-c2jb0tMLWlERxA,10059
pyhanko/cli/plugin_api.py,sha256=ANFvR4XlhTT7Q8V7zEI2PwfsBnhQv3MYGJW6ssvX2L0,3333
pyhanko/cli/runtime.py,sha256=YIA9KVGFqtOymrGzgaExmmfTd7nNGBNvsMe5H05QSek,2595
pyhanko/cli/utils.py,sha256=GPDOibQQ2YqPPz7UG9Z7zsOiihJ22a5oM1CF_q49eNE,1854
pyhanko/config/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyhanko/config/__pycache__/__init__.cpython-313.pyc,,
pyhanko/config/__pycache__/api.cpython-313.pyc,,
pyhanko/config/__pycache__/errors.cpython-313.pyc,,
pyhanko/config/__pycache__/local_keys.cpython-313.pyc,,
pyhanko/config/__pycache__/logging.cpython-313.pyc,,
pyhanko/config/__pycache__/pkcs11.cpython-313.pyc,,
pyhanko/config/__pycache__/trust.cpython-313.pyc,,
pyhanko/config/api.py,sha256=Y0Q_fHULs8XYxbRWSlZbqmqSRLZ8ZwsuG-u9_L09ho8,8808
pyhanko/config/errors.py,sha256=gii9svb8L_2IUiXkO3-P57huQNlXyXUPJ5bBmptk6ts,165
pyhanko/config/local_keys.py,sha256=NSMZne_bhdAXcvLAfco7IsAiFzw1OEC0h6eL2ryTSb0,2882
pyhanko/config/logging.py,sha256=1NczV1OgAe0r9yyIrbWA8rLT614_C8pOPZsHl6DdGzc,2991
pyhanko/config/pkcs11.py,sha256=xA6J28cUVBSquzvNKAoKpBcXy9pobFryvYuxNzPJHPo,9079
pyhanko/config/trust.py,sha256=wDS4BeejOSfd91CD2xSIqss0bUQzYS2XsSIkkoZzfrc,2526
pyhanko/generated/__init__.py,sha256=p90GkGWkNXXURZlLROoelqiPshe31lKft3XNBz2546w,59
pyhanko/generated/__pycache__/__init__.cpython-313.pyc,,
pyhanko/generated/__pycache__/xml.cpython-313.pyc,,
pyhanko/generated/etsi/__init__.py,sha256=SyyeK1AoW_0NmIFNVO9zgIuvggDZVRv5HMK1nYg8zv0,11628
pyhanko/generated/etsi/__pycache__/__init__.cpython-313.pyc,,
pyhanko/generated/etsi/__pycache__/ts_11910202.cpython-313.pyc,,
pyhanko/generated/etsi/__pycache__/ts_119612.cpython-313.pyc,,
pyhanko/generated/etsi/__pycache__/xades.cpython-313.pyc,,
pyhanko/generated/etsi/ts_11910202.py,sha256=BFkHmf_T5hnsx_v3PPSiAk2IddqGukozv0Ls8mca0fg,50556
pyhanko/generated/etsi/ts_119612.py,sha256=cZeuhMB4o7yB0DgrUHk3ijnvZR7B44q5o9ES4Kxgny8,31142
pyhanko/generated/etsi/xades.py,sha256=ii31sRnAEOM6sACbtPno_WxDE-VDFyKGfpXRVm-H_7c,41318
pyhanko/generated/w3c/__init__.py,sha256=HHBRwTle1ykxFsZAc7uHzMdh2r5Zvw8fyHFTPQCBytQ,1889
pyhanko/generated/w3c/__pycache__/__init__.cpython-313.pyc,,
pyhanko/generated/w3c/__pycache__/xmldsig_core.cpython-313.pyc,,
pyhanko/generated/w3c/xmldsig_core.py,sha256=LLDm6PUrXPSGijfKb9ZFFc7wckVu5zxCHKxwNvwBnls,21491
pyhanko/generated/xml.py,sha256=MX9Bw2Sv-Mp9Qmz4p7MgL7hEFRTpqta7YRRYs9Cjq0g,118
pyhanko/keys.py,sha256=4dZ-J3DYcsjRWN4zyRFvXhZjxT4iw0gf3HtKYPoYGRI,4597
pyhanko/pdf_utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyhanko/pdf_utils/__pycache__/__init__.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/barcodes.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/content.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/embed.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/extensions.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/filters.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/generic.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/images.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/incremental_writer.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/layout.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/misc.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/qr.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/reader.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/rw_common.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/text.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/writer.cpython-313.pyc,,
pyhanko/pdf_utils/__pycache__/xref.cpython-313.pyc,,
pyhanko/pdf_utils/barcodes.py,sha256=rzPt_RABABkVK8AU5YKtIFX09lSwqdYUR_4VByIVMNg,3439
pyhanko/pdf_utils/content.py,sha256=piuiV2O6DdwldTcwt4hpDLDNi_yZtmDi5tbRAfhsidE,9657
pyhanko/pdf_utils/crypt/__init__.py,sha256=BKFaUmBhyIJPD_e_ThDQSRRsMX20okfSNHM05eje7RY,4192
pyhanko/pdf_utils/crypt/__pycache__/__init__.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/__pycache__/_iso32004_asn1.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/__pycache__/_legacy.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/__pycache__/_saslprep.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/__pycache__/_util.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/__pycache__/api.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/__pycache__/cred_ser.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/__pycache__/filter_mixins.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/__pycache__/pdfmac.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/__pycache__/permissions.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/__pycache__/pubkey.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/__pycache__/standard.cpython-313.pyc,,
pyhanko/pdf_utils/crypt/_iso32004_asn1.py,sha256=1Kd-qoae8K22yEEX7Y3tjoHySepUrESKmZYXaVnjzS8,1759
pyhanko/pdf_utils/crypt/_legacy.py,sha256=dmu5VMWYOi7e9fntwIxs4Yv4iDmSB_JDwT-c0A5JtYE,8951
pyhanko/pdf_utils/crypt/_saslprep.py,sha256=MJ_Vcdv0138ljDeqm7QHIcVqv2yH2Me53x7YKYs_ISs,3664
pyhanko/pdf_utils/crypt/_util.py,sha256=Gq9bSqR7TdaH9BQYGQZobXQSZHWnEH_bewf7fzCGJ08,1898
pyhanko/pdf_utils/crypt/api.py,sha256=OMcYAUA8vDmWmozCzoVLa_hhM6XLytq1YckFVOTeLcQ,30223
pyhanko/pdf_utils/crypt/cred_ser.py,sha256=BHgqkSAZP4ONZ3cuq_jdyjrfB7IiI5VSi9V05pccw6Y,3548
pyhanko/pdf_utils/crypt/filter_mixins.py,sha256=4DPrXduhAHpm6DOrbwzOGLCrXBxZVBE0nbJ12Jo-s1k,7357
pyhanko/pdf_utils/crypt/pdfmac.py,sha256=9XiBLXruZRElC2kt7ipZ7f_uGhJkx4XXje81CUlXyfw,23204
pyhanko/pdf_utils/crypt/permissions.py,sha256=jN8wZKbn4zeRE40PeU8yDJ7AiIFw4ScguNCpETuqNeU,3677
pyhanko/pdf_utils/crypt/pubkey.py,sha256=AzKThdkE9_WBoNOP5oF-4a-Jl1pgh3cqvJLUaO1OMCw,54694
pyhanko/pdf_utils/crypt/standard.py,sha256=xpqtf4dcGJMdKuksVdJRAM8N-I3-fi1-1E7AX44dRoc,31387
pyhanko/pdf_utils/embed.py,sha256=bwUDO70lBMewfXaCyHSeMasVT109TY2IWHwbQLxZ0ew,17513
pyhanko/pdf_utils/extensions.py,sha256=YJyp8TGkZCE_5YWngVrRHPvfJCrQLVFo2EYWNZVxV9E,3956
pyhanko/pdf_utils/filters.py,sha256=AYg_7-3cG_b3ZBZh6RW-c94PrfDGGg7gQODIXe3054U,9267
pyhanko/pdf_utils/font/__init__.py,sha256=4lF1of8QHFxTk1HzMdtw1KsVueKLcIN9oY27CfmtrTU,253
pyhanko/pdf_utils/font/__pycache__/__init__.cpython-313.pyc,,
pyhanko/pdf_utils/font/__pycache__/api.cpython-313.pyc,,
pyhanko/pdf_utils/font/__pycache__/basic.cpython-313.pyc,,
pyhanko/pdf_utils/font/__pycache__/opentype.cpython-313.pyc,,
pyhanko/pdf_utils/font/api.py,sha256=W4qnAHMXbspGsExCK96XpKSaXyIUUTdi7o6rH37SyB8,3777
pyhanko/pdf_utils/font/basic.py,sha256=FdFq6TPmyqU9fd2kNWBFfC2WGb_ci0mgTeXb_sk8oFU,4603
pyhanko/pdf_utils/font/opentype.py,sha256=sFziqOAnJjis2aY4Ca9_TMjE_KoI9iA6QuN1xf7WXQQ,31034
pyhanko/pdf_utils/generic.py,sha256=OQ6QIrG91KctfyqOWE40ygprtnDj1C2cZQa5ZNUGrJY,68527
pyhanko/pdf_utils/images.py,sha256=4xZ1ytEp-JgkaAAhugtc89lYq7g51B3DlG5-ya4vF4Q,7207
pyhanko/pdf_utils/incremental_writer.py,sha256=v7BlTSpeBXj5e5eShHNlxh-LE3MQ8abA0V0RTN-sVjw,11226
pyhanko/pdf_utils/layout.py,sha256=WIxhZ8EpfS57jlw89YjGpVZrc5mgrvKJjAMQcx6QbeY,17504
pyhanko/pdf_utils/metadata/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyhanko/pdf_utils/metadata/__pycache__/__init__.cpython-313.pyc,,
pyhanko/pdf_utils/metadata/__pycache__/info.cpython-313.pyc,,
pyhanko/pdf_utils/metadata/__pycache__/model.cpython-313.pyc,,
pyhanko/pdf_utils/metadata/__pycache__/xmp_xml.cpython-313.pyc,,
pyhanko/pdf_utils/metadata/info.py,sha256=IZlgAPTYNZuR7UTUU1_J-zLehC8eyAfQiweeT-m1CR0,4240
pyhanko/pdf_utils/metadata/model.py,sha256=H5XaGAVZhTEEM4F1BaOi8p4YwPL9o79bo4sbxcvfiig,13048
pyhanko/pdf_utils/metadata/xmp_xml.py,sha256=LB82w--d-c67nxNVVh7suuwed3jn4F4YhWm-_ufEqrU,18186
pyhanko/pdf_utils/misc.py,sha256=Y_71Y1NFQD1v9zt3805fdF2NCxZ24wDeAAJlyQnQn1U,15485
pyhanko/pdf_utils/qr.py,sha256=W-jCyo-arJFG-5dXpiYngj7Ja7W-7kRk46_qlHoohgE,9026
pyhanko/pdf_utils/reader.py,sha256=kEhQ3DN47LLQ2MYK74bhK3Q-rfZDrYzR8y3X1E3rKIs,47499
pyhanko/pdf_utils/rw_common.py,sha256=iPtz4N-0cUIomyt5kj278dXIVluBAq1xy4qLyGat0nU,6494
pyhanko/pdf_utils/text.py,sha256=XrqJNtAQTFvE0zrSKLoBfsvIHfzsPct8PZGIzHL2oko,8195
pyhanko/pdf_utils/writer.py,sha256=it73j-ERk6cwGgYsk-NFK1Zs4A6iLcUdS8CMvmpnvsk,52873
pyhanko/pdf_utils/xref.py,sha256=ZN4JrIpIESUh7G5BfQk4BUgmXmhsEW-ncD_oChUA2Aw,50332
pyhanko/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyhanko/sign/__init__.py,sha256=htfFWTnaN4S52H9ljU7R-9AKnKKq0Lg0Y0KN0hEgoCk,23
pyhanko/sign/__pycache__/__init__.cpython-313.pyc,,
pyhanko/sign/__pycache__/attributes.cpython-313.pyc,,
pyhanko/sign/__pycache__/fields.cpython-313.pyc,,
pyhanko/sign/__pycache__/general.cpython-313.pyc,,
pyhanko/sign/__pycache__/pkcs11.cpython-313.pyc,,
pyhanko/sign/ades/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyhanko/sign/ades/__pycache__/__init__.cpython-313.pyc,,
pyhanko/sign/ades/__pycache__/api.cpython-313.pyc,,
pyhanko/sign/ades/__pycache__/asn1_util.cpython-313.pyc,,
pyhanko/sign/ades/__pycache__/cades_asn1.cpython-313.pyc,,
pyhanko/sign/ades/__pycache__/report.cpython-313.pyc,,
pyhanko/sign/ades/api.py,sha256=XBWF4NMHoUdD3veEywn-MRNbGCfUdZhv8dGKam19igs,5328
pyhanko/sign/ades/asn1_util.py,sha256=OcYtSd1Nao9nagAxAhZsq483OfTf5_YrsOrBcdcQ7OU,464
pyhanko/sign/ades/cades_asn1.py,sha256=tEy5McIFxxZFsAHzALyVDWYWHfYtGM5FyeLV3gvgqNQ,7195
pyhanko/sign/ades/report.py,sha256=kyZK57yXOtQ4LFj5MwnEBntiwHVF6VPKJI1wc0vKsk4,2181
pyhanko/sign/attributes.py,sha256=D3XK2DmlpJIESE9nZm2tP6ZaJQzUH_3C1YI4cjQnrVo,6807
pyhanko/sign/diff_analysis/__init__.py,sha256=5KhFJNOLdrmFexvkn_Mtg21WwQ2C4pH1p0O_BWLAKdo,1547
pyhanko/sign/diff_analysis/__pycache__/__init__.cpython-313.pyc,,
pyhanko/sign/diff_analysis/__pycache__/commons.cpython-313.pyc,,
pyhanko/sign/diff_analysis/__pycache__/constants.cpython-313.pyc,,
pyhanko/sign/diff_analysis/__pycache__/form_rules_api.cpython-313.pyc,,
pyhanko/sign/diff_analysis/__pycache__/policies.cpython-313.pyc,,
pyhanko/sign/diff_analysis/__pycache__/policy_api.cpython-313.pyc,,
pyhanko/sign/diff_analysis/__pycache__/rules_api.cpython-313.pyc,,
pyhanko/sign/diff_analysis/commons.py,sha256=-1GDKsLM59kFaZUuQ2se_d2kmhI8IoxGOtdJFhe9A4Q,7884
pyhanko/sign/diff_analysis/constants.py,sha256=U8Dz-5dzSVWcJ6ravJ0vn_YhLdX-6vUsoFbqCEU6DfM,1091
pyhanko/sign/diff_analysis/form_rules_api.py,sha256=zuYiSr14l_LxlBlQXI2k43KoWWLuo6jrIF1oVKtuUR8,16636
pyhanko/sign/diff_analysis/policies.py,sha256=R-3erhC02vcqGAhOBlHDf70yfIff43UXea49ebzwGuY,18566
pyhanko/sign/diff_analysis/policy_api.py,sha256=zkLsSyIrETug5ISjNNLeowFpY5CVgZu6v7LwxiCoSOo,5098
pyhanko/sign/diff_analysis/rules/__init__.py,sha256=JV3VtW7sRcc5KpPMg0VV29fjmmjkJxEzxtI_JEqoyAE,74
pyhanko/sign/diff_analysis/rules/__pycache__/__init__.cpython-313.pyc,,
pyhanko/sign/diff_analysis/rules/__pycache__/file_structure_rules.cpython-313.pyc,,
pyhanko/sign/diff_analysis/rules/__pycache__/form_field_rules.cpython-313.pyc,,
pyhanko/sign/diff_analysis/rules/__pycache__/metadata_rules.cpython-313.pyc,,
pyhanko/sign/diff_analysis/rules/file_structure_rules.py,sha256=MnsT9aj5eCvuqIfW_jTLtqRUS1DyE6xLO3QESOdNTms,4666
pyhanko/sign/diff_analysis/rules/form_field_rules.py,sha256=0A8ifq4UxKVm_XCOmK91G7aOQH74sclEtKqcWV-bDHE,40858
pyhanko/sign/diff_analysis/rules/metadata_rules.py,sha256=bi79n-s6v2nG9LZkxkJi5To5uDi_4-jzongNgxlFZeg,5541
pyhanko/sign/diff_analysis/rules_api.py,sha256=97KWD6SD39IJDOuNxhHDJXnlwJ3zwupEd6F2LRvbYtA,7724
pyhanko/sign/fields.py,sha256=CGgPRMLzH_d3Pu6pTLIAymo8njLRec5-6hAKGmuB82I,60482
pyhanko/sign/general.py,sha256=ueI6nqaKF3G-b5-YCg684pohdUqrECYDK4H-A-br2fo,19286
pyhanko/sign/pkcs11.py,sha256=DfIfjforDeSxci9rhAT1HTXpfB9LpV6HacbRLvmTdig,25372
pyhanko/sign/signers/__init__.py,sha256=SFi6a9fAeAcTbQMaWiG5lue4IBb7Q97Bp8DkQRMvZVE,1152
pyhanko/sign/signers/__pycache__/__init__.cpython-313.pyc,,
pyhanko/sign/signers/__pycache__/cms_embedder.cpython-313.pyc,,
pyhanko/sign/signers/__pycache__/constants.cpython-313.pyc,,
pyhanko/sign/signers/__pycache__/csc_signer.cpython-313.pyc,,
pyhanko/sign/signers/__pycache__/functions.cpython-313.pyc,,
pyhanko/sign/signers/__pycache__/pdf_byterange.cpython-313.pyc,,
pyhanko/sign/signers/__pycache__/pdf_cms.cpython-313.pyc,,
pyhanko/sign/signers/__pycache__/pdf_signer.cpython-313.pyc,,
pyhanko/sign/signers/cms_embedder.py,sha256=BfZherUWvbhm3lCLjs0MPiWhuSKHJgtyD68COesmnxE,16919
pyhanko/sign/signers/constants.py,sha256=MJPm2iDeWWralyZncVjlnawO8Ml9i0Hlfo2Zm1Efnbw,3078
pyhanko/sign/signers/csc_signer.py,sha256=dMa9LW6wXEE3Azoeum9OLciZ-YaKBEYbJFAqPtQwDC4,28291
pyhanko/sign/signers/functions.py,sha256=X_P_jGMGI69CBcLkLEu1zZtIp30rnL5nUB3eWWvf994,8306
pyhanko/sign/signers/pdf_byterange.py,sha256=NnxVab23GUTzpRBKuRQE-pUugsMTnS4pMIq_28BLlQ8,17109
pyhanko/sign/signers/pdf_cms.py,sha256=f-NVKqGZUrv8DsH1rViU2Ct3XmTbenRXvug6mv7FdOw,69025
pyhanko/sign/signers/pdf_signer.py,sha256=YqQp_6aHA7Y8ljzC59AUoMTJ525X2wlGeT8X4QgxBQQ,110005
pyhanko/sign/timestamps/__init__.py,sha256=G4sodXv4YVi2cmIoJaotbAEz9Y2-JxFjKWSemUgObn0,275
pyhanko/sign/timestamps/__pycache__/__init__.cpython-313.pyc,,
pyhanko/sign/timestamps/__pycache__/aiohttp_client.cpython-313.pyc,,
pyhanko/sign/timestamps/__pycache__/api.cpython-313.pyc,,
pyhanko/sign/timestamps/__pycache__/common_utils.cpython-313.pyc,,
pyhanko/sign/timestamps/__pycache__/dummy_client.cpython-313.pyc,,
pyhanko/sign/timestamps/__pycache__/requests_client.cpython-313.pyc,,
pyhanko/sign/timestamps/aiohttp_client.py,sha256=U-GjArYqgl84h-WPEqV8dIse3PRAFf9WkI3jgA85kkY,3502
pyhanko/sign/timestamps/api.py,sha256=_9ZrlVBYcZ61xUqDIK1eOo__TrJ03iESM2kwoBUcuyc,6141
pyhanko/sign/timestamps/common_utils.py,sha256=h-q0gNNriRzBdyFlk716w1_OGZO-BtHu6OPYFm1-aqk,2667
pyhanko/sign/timestamps/dummy_client.py,sha256=R1QE38KQg0smIT0r6s5Py2PXCCJ97O-LjgWt9P8SxtA,5816
pyhanko/sign/timestamps/requests_client.py,sha256=-wSIxva8xGhATLp98mbKVPteuaiOEXGPDxOnX7n2hEw,2227
pyhanko/sign/validation/__init__.py,sha256=rDxbSktMpMNWzC5qTPmfBDhUT0bmJS8YcwUaoJ_9ILY,13116
pyhanko/sign/validation/__pycache__/__init__.cpython-313.pyc,,
pyhanko/sign/validation/__pycache__/ades.cpython-313.pyc,,
pyhanko/sign/validation/__pycache__/dss.cpython-313.pyc,,
pyhanko/sign/validation/__pycache__/errors.cpython-313.pyc,,
pyhanko/sign/validation/__pycache__/generic_cms.cpython-313.pyc,,
pyhanko/sign/validation/__pycache__/ltv.cpython-313.pyc,,
pyhanko/sign/validation/__pycache__/pdf_embedded.cpython-313.pyc,,
pyhanko/sign/validation/__pycache__/policy_decl.cpython-313.pyc,,
pyhanko/sign/validation/__pycache__/settings.cpython-313.pyc,,
pyhanko/sign/validation/__pycache__/status.cpython-313.pyc,,
pyhanko/sign/validation/__pycache__/utils.cpython-313.pyc,,
pyhanko/sign/validation/ades.py,sha256=5nKt2l6NY1G21F1yX1ZenFL0NOdJKp5DnaaNmd6241s,84349
pyhanko/sign/validation/dss.py,sha256=xN75JtoghUuv6GldYShD4NDIdx4biHDPDPLWCwB11ZA,24801
pyhanko/sign/validation/errors.py,sha256=KILAYGeMZ29c1_CyBS-_jS0cgOhJooY1gKRgSzmLCSw,2195
pyhanko/sign/validation/generic_cms.py,sha256=NNDQpxoVSS44it8OdZcj4l0J7zAj8-Dc02mtB6QE014,44407
pyhanko/sign/validation/ltv.py,sha256=Y-0-6yNme0Bjwbj4QPn-1CHqcU0tjT7a5ryk50cmXRE,23806
pyhanko/sign/validation/pdf_embedded.py,sha256=nyHc3ZNbyTzBr1947W2p7aRRo40CpeVOLtmUgZJViWQ,35777
pyhanko/sign/validation/policy_decl.py,sha256=4O-a1JeCOHLWYko5K9-JlduZidlO2DgRsrttqwFIDxU,6194
pyhanko/sign/validation/report/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyhanko/sign/validation/report/__pycache__/__init__.cpython-313.pyc,,
pyhanko/sign/validation/report/__pycache__/tools.cpython-313.pyc,,
pyhanko/sign/validation/report/tools.py,sha256=KCqKLlPnwWm9__c5kLeUYfZcWF6tN0n8wGFQuz1wZ84,15851
pyhanko/sign/validation/settings.py,sha256=4i8gtUjp5ULxeFYIAFyjzBZ9pUtHFJIUtso38ippA7s,7511
pyhanko/sign/validation/status.py,sha256=zfY3ZYboyYNnEWFlcF8AGO35VIxKmVvbjqXCe1waXTw,28882
pyhanko/sign/validation/utils.py,sha256=fL_rysSJK1kGFkIqMc5c0RH9fr4yYoEo6NJZ7H1Dl4M,8768
pyhanko/stamp.py,sha256=VVqzP_IL7cIH71t6L1THTJtV5su2SG2ANs0230zMQ9w,32802
pyhanko/version.py,sha256=Qc-zsFIphx7PQk4drmEhYrnixCDMuORLGtSFa-uhCKc,53
