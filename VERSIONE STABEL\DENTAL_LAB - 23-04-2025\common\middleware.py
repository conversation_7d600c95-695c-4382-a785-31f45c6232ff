"""
Centralized error handling and validation middleware
"""

import json
import logging
from django.http import JsonResponse, HttpResponse
from django.template.loader import render_to_string
from django.core.exceptions import ValidationError, PermissionDenied
from django.db import IntegrityError
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from django.utils.translation import gettext as _

from .exceptions import (
    DentalLabException, BusinessRuleViolation, DataIntegrityError,
    FinancialError, WorkflowError, PermissionError, ResourceNotFoundError,
    handle_exception
)

logger = logging.getLogger(__name__)


class ErrorHandlingMiddleware(MiddlewareMixin):
    """
    Centralized error handling middleware that provides consistent error responses
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_exception(self, request, exception):
        """
        Process exceptions and return appropriate responses
        """
        # Get user context for logging
        user_context = {
            'user_id': getattr(request.user, 'id', None) if hasattr(request, 'user') else None,
            'path': request.path,
            'method': request.method,
            'ip_address': self.get_client_ip(request),
        }
        
        # Handle our custom exceptions
        if isinstance(exception, DentalLabException):
            return self.handle_dental_lab_exception(request, exception, user_context)
        
        # Handle Django built-in exceptions
        if isinstance(exception, ValidationError):
            return self.handle_validation_error(request, exception, user_context)
        
        if isinstance(exception, PermissionDenied):
            return self.handle_permission_denied(request, exception, user_context)
        
        if isinstance(exception, IntegrityError):
            return self.handle_integrity_error(request, exception, user_context)
        
        # Handle other exceptions in debug mode
        if settings.DEBUG:
            return None  # Let Django handle it
        
        # Handle unknown exceptions in production
        return self.handle_unknown_exception(request, exception, user_context)
    
    def handle_dental_lab_exception(self, request, exception, context):
        """Handle custom dental lab exceptions"""
        logger.error(
            f"DentalLabException: {exception.error_code} - {exception.message}",
            extra=context,
            exc_info=True
        )
        
        if self.is_ajax_request(request):
            return JsonResponse(exception.to_dict(), status=400)
        
        # Return HTML error page
        return self.render_error_page(
            request, 
            exception.message, 
            exception.error_code,
            status=400
        )
    
    def handle_validation_error(self, request, exception, context):
        """Handle Django validation errors"""
        logger.warning(f"ValidationError: {exception}", extra=context)
        
        # Convert to our format
        if hasattr(exception, 'error_dict'):
            field_errors = {
                field: [str(error) for error in errors] 
                for field, errors in exception.error_dict.items()
            }
            message = _("Validation failed")
        else:
            field_errors = {}
            message = str(exception)
        
        if self.is_ajax_request(request):
            return JsonResponse({
                'error': True,
                'error_code': 'VALIDATION_ERROR',
                'message': message,
                'field_errors': field_errors
            }, status=400)
        
        return self.render_error_page(request, message, 'VALIDATION_ERROR', status=400)
    
    def handle_permission_denied(self, request, exception, context):
        """Handle permission denied errors"""
        logger.warning(f"PermissionDenied: {exception}", extra=context)
        
        message = str(exception) or _("You don't have permission to access this resource")
        
        if self.is_ajax_request(request):
            return JsonResponse({
                'error': True,
                'error_code': 'PERMISSION_DENIED',
                'message': message
            }, status=403)
        
        return self.render_error_page(request, message, 'PERMISSION_DENIED', status=403)
    
    def handle_integrity_error(self, request, exception, context):
        """Handle database integrity errors"""
        logger.error(f"IntegrityError: {exception}", extra=context, exc_info=True)
        
        # Try to provide user-friendly message
        message = self.get_user_friendly_integrity_message(str(exception))
        
        if self.is_ajax_request(request):
            return JsonResponse({
                'error': True,
                'error_code': 'DATA_INTEGRITY_ERROR',
                'message': message
            }, status=400)
        
        return self.render_error_page(request, message, 'DATA_INTEGRITY_ERROR', status=400)
    
    def handle_unknown_exception(self, request, exception, context):
        """Handle unknown exceptions"""
        logger.error(f"UnknownException: {exception}", extra=context, exc_info=True)
        
        message = _("An unexpected error occurred. Please try again later.")
        
        if self.is_ajax_request(request):
            return JsonResponse({
                'error': True,
                'error_code': 'INTERNAL_ERROR',
                'message': message
            }, status=500)
        
        return self.render_error_page(request, message, 'INTERNAL_ERROR', status=500)
    
    def get_user_friendly_integrity_message(self, error_message):
        """Convert technical integrity error to user-friendly message"""
        error_lower = error_message.lower()
        
        if 'unique constraint' in error_lower or 'duplicate' in error_lower:
            return _("This record already exists. Please check your data.")
        
        if 'foreign key constraint' in error_lower:
            return _("Cannot complete this action due to related data dependencies.")
        
        if 'not null constraint' in error_lower:
            return _("Required information is missing. Please fill in all required fields.")
        
        return _("Data integrity error. Please check your input and try again.")
    
    def is_ajax_request(self, request):
        """Check if request is AJAX"""
        return (
            request.headers.get('X-Requested-With') == 'XMLHttpRequest' or
            request.content_type == 'application/json' or
            'application/json' in request.headers.get('Accept', '')
        )
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def render_error_page(self, request, message, error_code, status=400):
        """Render error page with consistent styling"""
        context = {
            'error_message': message,
            'error_code': error_code,
            'status_code': status,
        }
        
        # Use appropriate template based on status
        if status == 403:
            template = 'accounts/errors/403.html'
        elif status == 404:
            template = 'accounts/errors/404.html'
        elif status == 500:
            template = 'accounts/errors/500.html'
        else:
            template = 'accounts/errors/generic.html'
        
        try:
            content = render_to_string(template, context, request=request)
            return HttpResponse(content, status=status)
        except:
            # Fallback to simple error page
            return HttpResponse(
                f"<h1>Error {status}</h1><p>{message}</p>",
                status=status
            )


class InputSanitizationMiddleware(MiddlewareMixin):
    """
    Middleware to sanitize user input and prevent XSS attacks
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """Sanitize request data"""
        if request.method in ['POST', 'PUT', 'PATCH']:
            # Sanitize POST data
            if hasattr(request, 'POST'):
                request.POST = self.sanitize_data(request.POST)
            
            # Sanitize JSON data
            if request.content_type == 'application/json':
                try:
                    data = json.loads(request.body)
                    sanitized_data = self.sanitize_data(data)
                    request._body = json.dumps(sanitized_data).encode('utf-8')
                except (json.JSONDecodeError, UnicodeDecodeError):
                    pass
        
        return None
    
    def sanitize_data(self, data):
        """Sanitize data recursively"""
        if isinstance(data, dict):
            return {key: self.sanitize_data(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self.sanitize_data(item) for item in data]
        elif isinstance(data, str):
            return self.sanitize_string(data)
        else:
            return data
    
    def sanitize_string(self, value):
        """Sanitize string value"""
        if not value:
            return value
        
        # Remove potentially dangerous characters
        dangerous_chars = ['<script', '</script', 'javascript:', 'onload=', 'onerror=']
        
        for char in dangerous_chars:
            value = value.replace(char, '')
        
        # Limit string length to prevent DoS
        if len(value) > 10000:  # 10KB limit
            value = value[:10000]
        
        return value.strip()
