{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Case Material Analysis" %} - Case #{{ case.case_number }}{% endblock %}

{% block content %}
<div class="container-fluid my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">
            <i class="bi bi-clipboard-data"></i> {% trans "Material Analysis" %} - Case #{{ case.case_number }}
        </h1>
        <div>
            <a href="{% url 'case:case_detail' case.id %}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left"></i> {% trans "Back to Case" %}
            </a>
            <a href="{% url 'items:material_requirements_planning' %}" class="btn btn-primary">
                <i class="bi bi-diagram-3"></i> {% trans "MRP Dashboard" %}
            </a>
        </div>
    </div>

    <!-- Case Information -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle"></i> {% trans "Case Information" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>{% trans "Case Number:" %}</strong> {{ case.case_number }}</p>
                            <p><strong>{% trans "Patient:" %}</strong> {{ case.patient.full_name }}</p>
                            <p><strong>{% trans "Dentist:" %}</strong> {{ case.dentist.name }}</p>
                            <p><strong>{% trans "Status:" %}</strong> 
                                <span class="badge bg-{{ case.status == 'completed' and 'success' or case.status == 'in_progress' and 'warning' or 'secondary' }}">
                                    {{ case.get_status_display }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{% trans "Created:" %}</strong> {{ case.created_at|date:"M d, Y" }}</p>
                            <p><strong>{% trans "Deadline:" %}</strong> 
                                {% if case.deadline %}
                                    {{ case.deadline|date:"M d, Y" }}
                                {% else %}
                                    {% trans "Not set" %}
                                {% endif %}
                            </p>
                            <p><strong>{% trans "Estimated Completion:" %}</strong> 
                                {% if case.estimated_completion %}
                                    {{ case.estimated_completion|date:"M d, Y" }}
                                {% else %}
                                    {% trans "Not set" %}
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-currency-dollar"></i> {% trans "Cost Summary" %}
                    </h5>
                </div>
                <div class="card-body">
                    <p><strong>{% trans "Total Material Cost:" %}</strong> ${{ total_cost|floatformat:2 }}</p>
                    <p><strong>{% trans "Shortage Cost:" %}</strong> 
                        {% if shortage_cost > 0 %}
                            <span class="text-danger">${{ shortage_cost|floatformat:2 }}</span>
                        {% else %}
                            <span class="text-success">$0.00</span>
                        {% endif %}
                    </p>
                    <p><strong>{% trans "Feasible:" %}</strong> 
                        {% if feasible %}
                            <span class="badge bg-success">{% trans "Yes" %}</span>
                        {% else %}
                            <span class="badge bg-danger">{% trans "No" %}</span>
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Material Requirements -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-list-check"></i> {% trans "Material Requirements" %}
            </h5>
        </div>
        <div class="card-body">
            {% if material_requirements %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{% trans "Raw Material" %}</th>
                                <th>{% trans "Required Quantity" %}</th>
                                <th>{% trans "Available Quantity" %}</th>
                                <th>{% trans "Shortage" %}</th>
                                <th>{% trans "Unit Cost" %}</th>
                                <th>{% trans "Total Cost" %}</th>
                                <th>{% trans "Status" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for req in material_requirements %}
                                <tr>
                                    <td>
                                        <strong>{{ req.raw_material.name }}</strong>
                                        {% if req.raw_material.description %}
                                            <br><small class="text-muted">{{ req.raw_material.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ req.required_quantity }} {{ req.unit.name }}</td>
                                    <td>{{ req.available_quantity }} {{ req.unit.name }}</td>
                                    <td>
                                        {% if req.shortage_quantity > 0 %}
                                            <span class="text-danger">{{ req.shortage_quantity }} {{ req.unit.name }}</span>
                                        {% else %}
                                            <span class="text-success">{% trans "None" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>${{ req.raw_material.price_per_unit|floatformat:2 }}</td>
                                    <td>${{ req.estimated_cost|floatformat:2 }}</td>
                                    <td>
                                        {% if req.is_sufficient %}
                                            <span class="badge bg-success">{% trans "Available" %}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{% trans "Shortage" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-inbox fs-1 text-muted"></i>
                    <p class="text-muted mt-2">{% trans "No material requirements found for this case." %}</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Missing Materials Alert -->
    {% if missing_materials %}
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle-fill"></i> {% trans "Missing Materials - Action Required" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <strong>{% trans "Warning:" %}</strong> {% trans "This case cannot be completed with current inventory levels. The following materials need to be ordered:" %}
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{% trans "Material" %}</th>
                                <th>{% trans "Shortage Quantity" %}</th>
                                <th>{% trans "Estimated Cost" %}</th>
                                <th>{% trans "Best Supplier" %}</th>
                                <th>{% trans "Action" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for missing in missing_materials %}
                                <tr>
                                    <td>
                                        <strong>{{ missing.raw_material.name }}</strong>
                                    </td>
                                    <td>{{ missing.shortage_quantity }} {{ missing.unit.name }}</td>
                                    <td>${{ missing.shortage_cost|floatformat:2 }}</td>
                                    <td>
                                        {% if missing.supplier_options %}
                                            {{ missing.supplier_options.0.supplier.name }}
                                            <br><small class="text-muted">
                                                ${{ missing.supplier_options.0.price_per_unit|floatformat:2 }}/{{ missing.unit.name }}
                                                - {{ missing.supplier_options.0.delivery_time_days }} {% trans "days" %}
                                            </small>
                                        {% else %}
                                            <span class="text-muted">{% trans "No supplier found" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'items:purchase_recommendations' %}" class="btn btn-sm btn-danger">
                                            <i class="bi bi-cart-plus"></i> {% trans "Order Now" %}
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <a href="{% url 'items:purchase_recommendations' %}" class="btn btn-danger">
                        <i class="bi bi-cart-plus"></i> {% trans "Create Purchase Orders for Missing Materials" %}
                    </a>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Case Items -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-box"></i> {% trans "Case Items" %}
            </h5>
        </div>
        <div class="card-body">
            {% if production_plan.items %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{% trans "Item" %}</th>
                                <th>{% trans "Quantity" %}</th>
                                <th>{% trans "Unit" %}</th>
                                <th>{% trans "Estimated Cost" %}</th>
                                <th>{% trans "Status" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item_data in production_plan.items %}
                                <tr>
                                    <td>
                                        <strong>{{ item_data.item.name }}</strong>
                                        {% if item_data.item.description %}
                                            <br><small class="text-muted">{{ item_data.item.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ item_data.quantity }}</td>
                                    <td>{{ item_data.unit.name }}</td>
                                    <td>${{ item_data.estimated_cost|floatformat:2 }}</td>
                                    <td>
                                        {% if feasible %}
                                            <span class="badge bg-success">{% trans "Ready" %}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{% trans "Pending Materials" %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-inbox fs-1 text-muted"></i>
                    <p class="text-muted mt-2">{% trans "No items found for this case." %}</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Cost Variance Analysis -->
    {% if cost_variance %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i> {% trans "Cost Variance Analysis" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% trans "Estimated Cost" %}</h6>
                                <h4 class="text-primary">${{ cost_variance.estimated_cost|floatformat:2 }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% trans "Actual Cost" %}</h6>
                                <h4 class="text-info">${{ cost_variance.actual_cost|floatformat:2 }}</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% trans "Variance" %}</h6>
                                <h4 class="{% if cost_variance.total_variance > 0 %}text-danger{% else %}text-success{% endif %}">
                                    {% if cost_variance.total_variance > 0 %}+{% endif %}${{ cost_variance.total_variance|floatformat:2 }}
                                    <small>({{ cost_variance.variance_percentage|floatformat:1 }}%)</small>
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>

                {% if cost_variance.variance_details %}
                    <h6>{% trans "Item-by-Item Variance" %}</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{% trans "Item" %}</th>
                                    <th>{% trans "Estimated" %}</th>
                                    <th>{% trans "Actual" %}</th>
                                    <th>{% trans "Variance" %}</th>
                                    <th>{% trans "%" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for detail in cost_variance.variance_details %}
                                    <tr>
                                        <td>{{ detail.case_item.item.name }}</td>
                                        <td>${{ detail.estimated_cost|floatformat:2 }}</td>
                                        <td>${{ detail.actual_cost|floatformat:2 }}</td>
                                        <td class="{% if detail.variance > 0 %}text-danger{% else %}text-success{% endif %}">
                                            {% if detail.variance > 0 %}+{% endif %}${{ detail.variance|floatformat:2 }}
                                        </td>
                                        <td class="{% if detail.variance_percentage > 0 %}text-danger{% else %}text-success{% endif %}">
                                            {% if detail.variance_percentage > 0 %}+{% endif %}{{ detail.variance_percentage|floatformat:1 }}%
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% endif %}
            </div>
        </div>
    {% endif %}

    <!-- Recommendations -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="bi bi-lightbulb"></i> {% trans "Recommendations" %}
            </h5>
        </div>
        <div class="card-body">
            {% if not feasible %}
                <div class="alert alert-warning">
                    <h6><i class="bi bi-exclamation-triangle"></i> {% trans "Immediate Actions Required" %}</h6>
                    <ul class="mb-0">
                        <li>{% trans "Order missing materials before starting production" %}</li>
                        <li>{% trans "Update case timeline based on material delivery dates" %}</li>
                        <li>{% trans "Consider alternative materials if available" %}</li>
                    </ul>
                </div>
            {% else %}
                <div class="alert alert-success">
                    <h6><i class="bi bi-check-circle"></i> {% trans "Case Ready for Production" %}</h6>
                    <p class="mb-0">{% trans "All required materials are available. Production can begin immediately." %}</p>
                </div>
            {% endif %}

            <div class="mt-3">
                <a href="{% url 'items:purchase_recommendations' %}" class="btn btn-primary me-2">
                    <i class="bi bi-cart-plus"></i> {% trans "View Purchase Recommendations" %}
                </a>
                <a href="{% url 'items:stock_alerts_dashboard' %}" class="btn btn-outline-warning">
                    <i class="bi bi-exclamation-triangle"></i> {% trans "Check Stock Alerts" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
