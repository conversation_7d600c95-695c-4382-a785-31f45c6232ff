"""
Management command to run system validation checks
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from common.validators import CentralizedValidationService, run_full_validation
from common.exceptions import DentalLabException
import json


class Command(BaseCommand):
    help = 'Run comprehensive system validation checks'

    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['all', 'integrity', 'business', 'summary'],
            default='all',
            help='Type of validation to run'
        )
        parser.add_argument(
            '--format',
            type=str,
            choices=['text', 'json'],
            default='text',
            help='Output format'
        )
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Attempt to fix issues automatically (where possible)'
        )

    def handle(self, *args, **options):
        validation_type = options['type']
        output_format = options['format']
        auto_fix = options['fix']

        self.stdout.write(
            self.style.SUCCESS(f'Starting {validation_type} validation...')
        )

        try:
            if validation_type == 'all':
                results = self.run_all_validations()
            elif validation_type == 'integrity':
                results = self.run_integrity_validation()
            elif validation_type == 'business':
                results = self.run_business_validation()
            elif validation_type == 'summary':
                results = self.run_summary_validation()

            # Output results
            if output_format == 'json':
                self.stdout.write(json.dumps(results, indent=2, default=str))
            else:
                self.display_text_results(results)

            # Auto-fix if requested
            if auto_fix and results.get('fixable_issues'):
                self.attempt_auto_fix(results['fixable_issues'])

        except Exception as e:
            raise CommandError(f'Validation failed: {e}')

    def run_all_validations(self):
        """Run all validation types"""
        results = {
            'timestamp': timezone.now(),
            'validation_type': 'comprehensive',
            'results': {}
        }

        # Data integrity validation
        integrity_results = CentralizedValidationService.validate_data_integrity()
        results['results']['integrity'] = integrity_results

        # Legacy validation
        legacy_errors = run_full_validation()
        results['results']['legacy'] = legacy_errors

        # Summary
        summary = CentralizedValidationService.get_validation_summary()
        results['results']['summary'] = summary

        # Calculate totals
        total_errors = 0
        if integrity_results:
            total_errors += sum(len(errors) for errors in integrity_results.values())
        total_errors += len(legacy_errors)

        results['total_errors'] = total_errors
        results['status'] = 'healthy' if total_errors == 0 else 'issues_found'

        return results

    def run_integrity_validation(self):
        """Run data integrity validation only"""
        results = {
            'timestamp': timezone.now(),
            'validation_type': 'integrity',
            'results': CentralizedValidationService.validate_data_integrity()
        }

        total_errors = sum(len(errors) for errors in results['results'].values())
        results['total_errors'] = total_errors
        results['status'] = 'healthy' if total_errors == 0 else 'issues_found'

        return results

    def run_business_validation(self):
        """Run business rule validation"""
        results = {
            'timestamp': timezone.now(),
            'validation_type': 'business',
            'results': {}
        }

        # Test business operations
        business_tests = [
            ('case_completion', {}),
            ('workflow_transition', {}),
            ('payment_allocation', {}),
        ]

        for operation, kwargs in business_tests:
            try:
                errors = CentralizedValidationService.validate_business_operation(
                    operation, **kwargs
                )
                results['results'][operation] = errors
            except Exception as e:
                results['results'][operation] = [f'Validation error: {e}']

        total_errors = sum(len(errors) for errors in results['results'].values())
        results['total_errors'] = total_errors
        results['status'] = 'healthy' if total_errors == 0 else 'issues_found'

        return results

    def run_summary_validation(self):
        """Run validation summary only"""
        return {
            'timestamp': timezone.now(),
            'validation_type': 'summary',
            'results': CentralizedValidationService.get_validation_summary()
        }

    def display_text_results(self, results):
        """Display results in text format"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(f"VALIDATION REPORT - {results['validation_type'].upper()}")
        self.stdout.write('='*60)
        self.stdout.write(f"Timestamp: {results['timestamp']}")
        
        if 'status' in results:
            status_style = self.style.SUCCESS if results['status'] == 'healthy' else self.style.ERROR
            self.stdout.write(f"Status: {status_style(results['status'])}")
            self.stdout.write(f"Total Errors: {results.get('total_errors', 0)}")

        self.stdout.write('\nDETAILS:')
        self.stdout.write('-'*40)

        if results['validation_type'] == 'comprehensive':
            self.display_comprehensive_results(results['results'])
        elif results['validation_type'] == 'integrity':
            self.display_integrity_results(results['results'])
        elif results['validation_type'] == 'business':
            self.display_business_results(results['results'])
        elif results['validation_type'] == 'summary':
            self.display_summary_results(results['results'])

    def display_comprehensive_results(self, results):
        """Display comprehensive validation results"""
        for category, data in results.items():
            self.stdout.write(f"\n{category.upper()}:")
            
            if category == 'integrity':
                self.display_integrity_results(data)
            elif category == 'legacy':
                for error in data:
                    self.stdout.write(f"  ❌ {error}")
            elif category == 'summary':
                self.display_summary_results(data)

    def display_integrity_results(self, results):
        """Display integrity validation results"""
        if not results:
            self.stdout.write("  ✅ No integrity issues found")
            return

        for category, errors in results.items():
            self.stdout.write(f"  {category.upper()}:")
            for error in errors:
                self.stdout.write(f"    ❌ {error}")

    def display_business_results(self, results):
        """Display business validation results"""
        if not results:
            self.stdout.write("  ✅ No business rule violations found")
            return

        for operation, errors in results.items():
            self.stdout.write(f"  {operation.upper()}:")
            if not errors:
                self.stdout.write("    ✅ No issues")
            else:
                for error in errors:
                    self.stdout.write(f"    ❌ {error}")

    def display_summary_results(self, results):
        """Display summary validation results"""
        status = results.get('status', 'unknown')
        status_style = self.style.SUCCESS if status == 'healthy' else self.style.ERROR
        
        self.stdout.write(f"  Overall Status: {status_style(status)}")
        self.stdout.write(f"  Total Errors: {results.get('total_errors', 0)}")
        self.stdout.write(f"  Error Categories: {', '.join(results.get('error_categories', []))}")
        self.stdout.write(f"  Last Checked: {results.get('last_checked', 'Unknown')}")

    def attempt_auto_fix(self, fixable_issues):
        """Attempt to automatically fix issues"""
        self.stdout.write('\n' + '='*40)
        self.stdout.write('ATTEMPTING AUTO-FIX...')
        self.stdout.write('='*40)
        
        # This would contain actual fix logic
        # For now, just display what would be fixed
        for issue in fixable_issues:
            self.stdout.write(f"Would fix: {issue}")
        
        self.stdout.write(
            self.style.WARNING('Auto-fix functionality not yet implemented')
        )
