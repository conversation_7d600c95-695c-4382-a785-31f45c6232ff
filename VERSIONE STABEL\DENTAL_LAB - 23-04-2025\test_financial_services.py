#!/usr/bin/env python
"""
Test script for enhanced financial services
"""

import os
import sys
import django
from decimal import Decimal
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

from django.utils import timezone
from common.services import CurrencyService, FinancialTransactionService, CalculationService
from common.validators import BusinessRuleValidator
from common.audit import FinancialAuditService
from finance.models import Payment, Account
from billing.models import Invoice
from items.models import Currency, ExchangeRate
from Dentists.models import Dentist

class FinancialServicesTest:
    def __init__(self):
        self.test_results = []
        self.errors = []
    
    def log_test(self, test_name, success, message=""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = f"{status}: {test_name}"
        if message:
            result += f" - {message}"
        
        self.test_results.append(result)
        print(result)
        
        if not success:
            self.errors.append(f"{test_name}: {message}")
    
    def test_currency_service(self):
        """Test enhanced currency service"""
        print("\n=== Testing Currency Service ===")
        
        # Test 1: Same currency conversion
        result = CurrencyService.convert_amount(Decimal('100.00'), 'USD', 'USD')
        success = result['success'] and result['converted_amount'] == Decimal('100.00')
        self.log_test("Same currency conversion", success, 
                     f"Expected 100.00, got {result.get('converted_amount')}")
        
        # Test 2: Currency validation
        validation = CurrencyService.validate_currency_pair('USD', 'EUR')
        success = validation['valid']
        self.log_test("Currency pair validation", success, 
                     f"Validation errors: {validation.get('errors', [])}")
        
        # Test 3: Exchange rate caching
        rate1 = CurrencyService.get_exchange_rate('USD', 'ALL', use_cache=True)
        rate2 = CurrencyService.get_exchange_rate('USD', 'ALL', use_cache=True)
        success = rate1 == rate2 and rate1 is not None
        self.log_test("Exchange rate caching", success, 
                     f"Rate1: {rate1}, Rate2: {rate2}")
        
        # Test 4: Currency conversion with real rates
        if rate1:
            conversion = CurrencyService.convert_amount(Decimal('100.00'), 'USD', 'ALL')
            expected = Decimal('100.00') * rate1
            success = conversion['success'] and abs(conversion['converted_amount'] - expected) < Decimal('0.01')
            self.log_test("Real currency conversion", success, 
                         f"Expected ~{expected}, got {conversion.get('converted_amount')}")
    
    def test_payment_validation(self):
        """Test payment validation rules"""
        print("\n=== Testing Payment Validation ===")
        
        # Test 1: Valid payment data
        valid_payment_data = {
            'dentist': 1,  # Assuming dentist with ID 1 exists
            'amount': Decimal('500.00'),
            'currency': 1,  # Assuming currency with ID 1 exists
            'account': 1,   # Assuming account with ID 1 exists
            'date': timezone.now().date(),
            'payment_method': 'cash'
        }
        
        errors = BusinessRuleValidator.validate_payment_creation(valid_payment_data)
        success = len(errors) == 0
        self.log_test("Valid payment validation", success, 
                     f"Validation errors: {errors}")
        
        # Test 2: Invalid payment data (negative amount)
        invalid_payment_data = valid_payment_data.copy()
        invalid_payment_data['amount'] = Decimal('-100.00')
        
        errors = BusinessRuleValidator.validate_payment_creation(invalid_payment_data)
        success = len(errors) > 0 and 'amount' in errors
        self.log_test("Negative amount validation", success, 
                     f"Expected amount error, got: {errors}")
        
        # Test 3: Future date validation
        future_payment_data = valid_payment_data.copy()
        future_payment_data['date'] = timezone.now().date() + timedelta(days=1)
        
        errors = BusinessRuleValidator.validate_payment_creation(future_payment_data)
        success = len(errors) > 0 and 'date' in errors
        self.log_test("Future date validation", success, 
                     f"Expected date error, got: {errors}")
    
    def test_financial_transaction_service(self):
        """Test financial transaction service"""
        print("\n=== Testing Financial Transaction Service ===")
        
        # Test 1: Get payment summary for existing payment
        try:
            payment = Payment.objects.first()
            if payment:
                summary = FinancialTransactionService.get_payment_summary(payment.id)
                success = 'payment_id' in summary and 'total_amount' in summary
                self.log_test("Payment summary generation", success, 
                             f"Summary keys: {list(summary.keys())}")
            else:
                self.log_test("Payment summary generation", False, "No payments found in database")
        except Exception as e:
            self.log_test("Payment summary generation", False, f"Error: {e}")
        
        # Test 2: Payment allocation validation
        try:
            payment = Payment.objects.first()
            invoice = Invoice.objects.first()
            
            if payment and invoice:
                remaining = CalculationService.calculate_payment_allocation_remaining(payment)
                success = isinstance(remaining, Decimal) and remaining >= 0
                self.log_test("Payment allocation calculation", success, 
                             f"Remaining amount: {remaining}")
            else:
                self.log_test("Payment allocation calculation", False, 
                             "No payment or invoice found")
        except Exception as e:
            self.log_test("Payment allocation calculation", False, f"Error: {e}")
    
    def test_calculation_service(self):
        """Test calculation service"""
        print("\n=== Testing Calculation Service ===")
        
        # Test 1: Invoice total calculation
        try:
            invoice = Invoice.objects.first()
            if invoice:
                # Get invoice items
                items = invoice.invoice_items.all()
                if items.exists():
                    calculated_total = CalculationService.calculate_invoice_total(items)
                    success = isinstance(calculated_total, Decimal) and calculated_total >= 0
                    self.log_test("Invoice total calculation", success, 
                                 f"Calculated total: {calculated_total}")
                else:
                    self.log_test("Invoice total calculation", False, "No invoice items found")
            else:
                self.log_test("Invoice total calculation", False, "No invoices found")
        except Exception as e:
            self.log_test("Invoice total calculation", False, f"Error: {e}")
    
    def test_audit_service(self):
        """Test audit service"""
        print("\n=== Testing Audit Service ===")
        
        # Test 1: Currency conversion logging
        conversion_result = {
            'success': True,
            'original_amount': Decimal('100.00'),
            'from_currency': 'USD',
            'to_currency': 'ALL',
            'converted_amount': Decimal('9250.00'),
            'exchange_rate': Decimal('92.50'),
            'date': timezone.now().date()
        }
        
        try:
            FinancialAuditService.log_currency_conversion(conversion_result)
            self.log_test("Currency conversion audit logging", True, "Logged successfully")
        except Exception as e:
            self.log_test("Currency conversion audit logging", False, f"Error: {e}")
        
        # Test 2: Audit trail retrieval
        try:
            audit_logs = FinancialAuditService.get_audit_trail(
                operation_type='currency_conversion',
                limit=5
            )
            success = len(audit_logs) >= 0  # Should at least not error
            self.log_test("Audit trail retrieval", success, 
                         f"Retrieved {len(audit_logs)} logs")
        except Exception as e:
            self.log_test("Audit trail retrieval", False, f"Error: {e}")
    
    def test_data_consistency(self):
        """Test data consistency checks"""
        print("\n=== Testing Data Consistency ===")
        
        # Test 1: Invoice total consistency
        try:
            invoice = Invoice.objects.first()
            if invoice:
                # Recalculate total
                from common.services import DataConsistencyService
                was_fixed = DataConsistencyService.recalculate_invoice_total(invoice)
                success = isinstance(was_fixed, bool)
                self.log_test("Invoice total consistency check", success, 
                             f"Total was {'fixed' if was_fixed else 'already correct'}")
            else:
                self.log_test("Invoice total consistency check", False, "No invoices found")
        except Exception as e:
            self.log_test("Invoice total consistency check", False, f"Error: {e}")
    
    def run_all_tests(self):
        """Run all financial service tests"""
        print("Starting Financial Services Test Suite...")
        print("=" * 60)
        
        self.test_currency_service()
        self.test_payment_validation()
        self.test_financial_transaction_service()
        self.test_calculation_service()
        self.test_audit_service()
        self.test_data_consistency()
        
        # Summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if "✅ PASS" in r])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
        
        if self.errors:
            print("\nFAILED TESTS:")
            for error in self.errors:
                print(f"❌ {error}")
        
        if failed_tests == 0:
            print("\n🎉 All tests passed! Financial services are working correctly.")
            return True
        else:
            print(f"\n⚠️  {failed_tests} test(s) failed. Please review the issues above.")
            return False

if __name__ == '__main__':
    tester = FinancialServicesTest()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)
