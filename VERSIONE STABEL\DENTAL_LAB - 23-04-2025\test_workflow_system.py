#!/usr/bin/env python
"""
Test script for the workflow automation system
"""

import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

from common.workflow_automation import workflow_automation, TransitionContext
from common.status_synchronization import status_sync
from common.dependency_management import dependency_manager
from common.exceptions import WorkflowError


def test_state_machine_initialization():
    """Test state machine initialization"""
    print("🧪 Testing State Machine Initialization...")
    
    try:
        # Check if state machines are initialized
        state_machines = workflow_automation.state_machines
        print(f"✅ State machines initialized: {list(state_machines.keys())}")
        
        # Test case state machine
        case_sm = state_machines.get('case')
        if case_sm:
            print(f"✅ Case state machine: {len(case_sm.transitions)} transition groups")
        else:
            print("❌ Case state machine not found")
        
        # Test task state machine
        task_sm = state_machines.get('task')
        if task_sm:
            print(f"✅ Task state machine: {len(task_sm.transitions)} transition groups")
        else:
            print("❌ Task state machine not found")
        
        # Test schedule state machine
        schedule_sm = state_machines.get('schedule')
        if schedule_sm:
            print(f"✅ Schedule state machine: {len(schedule_sm.transitions)} transition groups")
        else:
            print("❌ Schedule state machine not found")
        
        print("✅ State machine initialization test completed\n")
        return True
        
    except Exception as e:
        print(f"❌ State machine initialization failed: {e}")
        return False


def test_workflow_transitions():
    """Test workflow transition logic"""
    print("🧪 Testing Workflow Transitions...")
    
    try:
        # Create a mock case object for testing
        class MockCase:
            def __init__(self):
                self.id = 1
                self.status = 'pending_acceptance'
                self.workflow_template = None
                self.current_stage = None
                self.assigned_technicians = MockQuerySet([])
                self.tasks = MockQuerySet([])
            
            def save(self):
                pass
        
        class MockQuerySet:
            def __init__(self, items):
                self.items = items
            
            def exists(self):
                return len(self.items) > 0
            
            def all(self):
                return self.items
            
            def filter(self, **kwargs):
                return MockQuerySet([])
        
        mock_case = MockCase()
        
        # Test getting valid transitions
        case_sm = workflow_automation.state_machines.get('case')
        if case_sm:
            valid_transitions = case_sm.get_valid_transitions(mock_case, 'pending_acceptance')
            print(f"✅ Valid transitions from 'pending_acceptance': {len(valid_transitions)}")
            
            for transition in valid_transitions:
                print(f"  - {transition.from_state} → {transition.to_state}")
        
        print("✅ Workflow transitions test completed\n")
        return True
        
    except Exception as e:
        print(f"❌ Workflow transitions test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_status_synchronization():
    """Test status synchronization logic"""
    print("🧪 Testing Status Synchronization...")
    
    try:
        # Test status determination logic
        task_counts = {
            'total': 3,
            'pending': 1,
            'in_progress': 1,
            'completed': 1,
            'blocked': 0,
            'cancelled': 0
        }
        
        result_status = status_sync._determine_case_status_from_tasks(task_counts, 'pending_acceptance')
        print(f"✅ Status determination: {task_counts} → {result_status}")
        
        # Test with all completed
        task_counts_completed = {
            'total': 3,
            'pending': 0,
            'in_progress': 0,
            'completed': 3,
            'blocked': 0,
            'cancelled': 0
        }
        
        result_status_completed = status_sync._determine_case_status_from_tasks(
            task_counts_completed, 'in_progress'
        )
        print(f"✅ All completed status: {task_counts_completed} → {result_status_completed}")
        
        # Test consistency validation
        consistency_errors = status_sync.validate_status_consistency()
        print(f"✅ Status consistency check: {len(consistency_errors)} errors found")
        
        if consistency_errors:
            for error in consistency_errors[:3]:  # Show first 3 errors
                print(f"  - {error}")
        
        print("✅ Status synchronization test completed\n")
        return True
        
    except Exception as e:
        print(f"❌ Status synchronization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dependency_management():
    """Test dependency management logic"""
    print("🧪 Testing Dependency Management...")
    
    try:
        # Create mock tasks for testing
        class MockTask:
            def __init__(self, task_id, status='pending'):
                self.id = task_id
                self.status = status
                self.dependencies = MockQuerySet([])
                self.dependent_tasks = MockQuerySet([])
        
        class MockQuerySet:
            def __init__(self, items):
                self.items = items
            
            def all(self):
                return self.items
            
            def exists(self):
                return len(self.items) > 0
            
            def exclude(self, **kwargs):
                if 'status' in kwargs:
                    excluded_status = kwargs['status']
                    filtered_items = [item for item in self.items if item.status != excluded_status]
                    return MockQuerySet(filtered_items)
                return MockQuerySet(self.items)
        
        task1 = MockTask(1, 'completed')
        task2 = MockTask(2, 'pending')
        task3 = MockTask(3, 'blocked')
        
        # Test circular dependency detection
        has_circular = dependency_manager._has_circular_dependency(task2, task1)
        print(f"✅ Circular dependency check: {has_circular}")
        
        # Test dependency chain
        dependency_chain = dependency_manager.get_task_dependency_chain(task2)
        print(f"✅ Dependency chain: {dependency_chain.get('task_id', 'Unknown')}")
        
        print("✅ Dependency management test completed\n")
        return True
        
    except Exception as e:
        print(f"❌ Dependency management test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_workflow_automation_service():
    """Test the main workflow automation service"""
    print("🧪 Testing Workflow Automation Service...")
    
    try:
        # Test service initialization
        print(f"✅ Workflow automation service initialized")
        print(f"✅ Available entity types: {list(workflow_automation.state_machines.keys())}")
        
        # Test getting valid transitions for non-existent entity
        try:
            transitions = workflow_automation.get_valid_transitions('nonexistent', None)
            print(f"✅ Non-existent entity handling: {len(transitions)} transitions")
        except Exception as e:
            print(f"✅ Non-existent entity error handling: {type(e).__name__}")
        
        print("✅ Workflow automation service test completed\n")
        return True
        
    except Exception as e:
        print(f"❌ Workflow automation service test failed: {e}")
        return False


def test_exception_handling():
    """Test workflow exception handling"""
    print("🧪 Testing Exception Handling...")
    
    try:
        # Test WorkflowError
        try:
            raise WorkflowError("Test workflow error", case_id=1, current_stage="test", target_stage="test2")
        except WorkflowError as e:
            print(f"✅ WorkflowError handling: {e.error_code} - {e.message}")
        
        # Test exception conversion
        from common.exceptions import handle_exception
        test_exception = ValueError("Test error")
        converted = handle_exception(test_exception, context={'test': True})
        print(f"✅ Exception conversion: {converted.error_code}")
        
        print("✅ Exception handling test completed\n")
        return True
        
    except Exception as e:
        print(f"❌ Exception handling test failed: {e}")
        return False


def main():
    """Run all workflow tests"""
    print("🚀 Starting Workflow Automation System Tests\n")
    print("="*60)
    
    tests = [
        test_state_machine_initialization,
        test_workflow_transitions,
        test_status_synchronization,
        test_dependency_management,
        test_workflow_automation_service,
        test_exception_handling,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("="*60)
    print(f"🎉 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All workflow automation tests completed successfully!")
        print("🚀 System is ready for workflow automation")
    else:
        print(f"⚠️  {total - passed} tests failed")
        print("🔧 Some components may need attention")
    
    return 0 if passed == total else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
