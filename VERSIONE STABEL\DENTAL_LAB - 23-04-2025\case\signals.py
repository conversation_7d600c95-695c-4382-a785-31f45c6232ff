from django.db.models.signals import post_save
from django.dispatch import receiver
from case.models import Case, CaseItem, Task, WorkflowStage
from items.models import RawMaterialInventory, Unit
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.db import transaction
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)

User = get_user_model()

@receiver(post_save, sender=CaseItem)
def create_or_update_task_from_case_item(sender, instance, created, **kwargs):
    try:
        with transaction.atomic():
            logger.info(f"Signal triggered for CaseItem: {instance.id} - {instance.item.name}")
            case = instance.case
            logger.info(f"Case: {case.case_number}, Current Stage: {case.current_stage}")

            if case.responsible_department:
                department_users = User.objects.filter(
                    departments__department=case.responsible_department
                )

                if department_users.exists():
                    assigned_user = department_users.first()

                    # Set default estimated duration if none provided
                    estimated_duration = instance.estimated_production_time or timedelta(hours=1)

                    # Check if current_stage exists, otherwise find a default stage
                    workflow_stage = case.current_stage
                    logger.info(f"Initial workflow_stage: {workflow_stage}")

                    if not workflow_stage:
                        logger.info(f"No current_stage found, looking for department stage")
                        # Try to get the first workflow stage for the department
                        workflow_stage = WorkflowStage.objects.filter(
                            department=case.responsible_department
                        ).order_by('order').first()

                        if not workflow_stage:
                            logger.info(f"No department stage found, looking for any stage")
                            # If no department-specific stage, get any stage
                            workflow_stage = WorkflowStage.objects.order_by('order').first()

                        if workflow_stage:
                            logger.info(f"Found workflow_stage: {workflow_stage.id} - {workflow_stage.name}")
                            # Update the case's current_stage
                            case.current_stage = workflow_stage
                            case.save(update_fields=['current_stage'])
                        else:
                            logger.error(f"No workflow stage found for case: {case}")
                            return  # Exit if no workflow stage can be found

                    # Create or update task with required fields
                    logger.info(f"Creating/updating task with workflow_stage: {workflow_stage.id} - {workflow_stage.name}")
                    task, created = Task.objects.get_or_create(
                        case=case,
                        workflow_stage=workflow_stage,
                        case_item=instance,  # Link directly to the CaseItem
                        defaults={
                            'title': f'Work on Case #{case.case_number} - {instance.item.name}',
                            'description': (
                                f'Complete work for Case #{case.case_number}\n'
                                f'Item: {instance.item.name}\n'
                                f'Quantity: {instance.quantity} {instance.unit.name}'
                            ),
                            'assigned_to': assigned_user,
                            'status': 'pending',
                            'priority': case.priority,
                            'estimated_duration': estimated_duration,
                            'created_by': case.created_by
                        }
                    )

                    if not created:
                        task.assigned_to = assigned_user
                        task.estimated_duration = estimated_duration
                        task.save()

                    logger.info(f"Task {'created' if created else 'updated'} for user: {assigned_user}, task: {task}")
                else:
                    logger.warning(f"No users found for department: {case.responsible_department}")
            else:
                logger.warning(f"No responsible department set for case: {case}")

            # Handle RawMaterialInventory
            if hasattr(instance, 'item') and hasattr(instance.item, 'itemrawmaterial_set'):
                for item_raw_material in instance.item.itemrawmaterial_set.all():
                    raw_material = item_raw_material.raw_material
                    unit = raw_material.unit

                    if unit:
                        # Get or create with update=True to avoid MultipleObjectsReturned
                        inventory, _ = RawMaterialInventory.objects.get_or_create(
                            raw_material=raw_material,
                            unit=unit,
                            defaults={'quantity': 0}
                        )
                    else:
                        logger.warning(f"No unit set for raw material: {raw_material}")

    except Exception as e:
        logger.error(f"Error in create_or_update_task_from_case_item: {str(e)}", exc_info=True)