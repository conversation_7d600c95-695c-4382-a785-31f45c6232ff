"""
Enhanced permission system for granular access control
"""

from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import PermissionDenied
from django.contrib.auth.mixins import UserPassesTestMixin
from django.contrib.auth.decorators import user_passes_test
from django.shortcuts import redirect
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from functools import wraps
import logging

logger = logging.getLogger(__name__)

class PermissionService:
    """
    Service for managing granular permissions
    """

    # Define permission categories
    PERMISSION_CATEGORIES = {
        'case_management': [
            'view_case', 'add_case', 'change_case', 'delete_case',
            'view_case_details', 'change_case_status', 'assign_case'
        ],
        'financial': [
            'view_invoice', 'add_invoice', 'change_invoice', 'delete_invoice',
            'view_payment', 'add_payment', 'change_payment', 'delete_payment',
            'process_payment', 'allocate_payment', 'view_financial_reports'
        ],
        'inventory': [
            'view_item', 'add_item', 'change_item', 'delete_item',
            'view_inventory', 'adjust_inventory', 'manage_suppliers'
        ],
        'scheduling': [
            'view_schedule', 'add_schedule', 'change_schedule', 'delete_schedule',
            'assign_tasks', 'view_department_schedule'
        ],
        'user_management': [
            'view_user', 'add_user', 'change_user', 'delete_user',
            'manage_permissions', 'view_user_activity'
        ],
        'reports': [
            'view_basic_reports', 'view_advanced_reports', 'view_financial_reports',
            'export_reports', 'view_analytics'
        ],
        'system_admin': [
            'manage_system_settings', 'view_audit_logs', 'manage_exchange_rates',
            'backup_restore', 'manage_departments'
        ]
    }

    # Define role-based permission sets
    ROLE_PERMISSIONS = {
        'super_admin': [
            'case_management', 'financial', 'inventory', 'scheduling',
            'user_management', 'reports', 'system_admin'
        ],
        'lab_manager': [
            'case_management', 'financial', 'inventory', 'scheduling',
            'reports'
        ],
        'department_manager': [
            'case_management', 'scheduling', 'inventory', 'reports'
        ],
        'technician': [
            'case_management', 'scheduling'
        ],
        'accountant': [
            'financial', 'reports'
        ],
        'dentist': [
            'case_management'  # Limited to own cases
        ],
        'receptionist': [
            'case_management'  # Limited operations
        ]
    }

    @classmethod
    def create_permission_groups(cls):
        """Create permission groups and assign permissions"""
        created_groups = []

        for role_name, categories in cls.ROLE_PERMISSIONS.items():
            group, created = Group.objects.get_or_create(name=role_name)
            if created:
                created_groups.append(role_name)

            # Clear existing permissions
            group.permissions.clear()

            # Add permissions for each category
            for category in categories:
                permissions = cls.PERMISSION_CATEGORIES.get(category, [])
                for perm_codename in permissions:
                    try:
                        # Try to find existing permission
                        permission = Permission.objects.get(codename=perm_codename)
                        group.permissions.add(permission)
                    except Permission.DoesNotExist:
                        # Create custom permission if it doesn't exist
                        content_type = ContentType.objects.get_for_model(Group)
                        permission = Permission.objects.create(
                            codename=perm_codename,
                            name=f'Can {perm_codename.replace("_", " ")}',
                            content_type=content_type
                        )
                        group.permissions.add(permission)

        return created_groups

    @classmethod
    def assign_user_to_role(cls, user, role_name):
        """Assign user to a role group"""
        try:
            group = Group.objects.get(name=role_name)
            user.groups.clear()  # Remove from all groups first
            user.groups.add(group)
            logger.info(f"User {user.email} assigned to role {role_name}")
            return True
        except Group.DoesNotExist:
            logger.error(f"Role {role_name} does not exist")
            return False

    @classmethod
    def user_has_permission(cls, user, permission_name, obj=None):
        """Check if user has specific permission"""
        if user.is_superuser:
            return True

        # For custom permissions, check if user's groups have the permission
        user_groups = user.groups.all()
        for group in user_groups:
            if group.permissions.filter(codename=permission_name).exists():
                return True

        # Check direct permission
        if user.has_perm(permission_name, obj):
            return True

        # Check if permission exists in user's permissions by codename
        if user.user_permissions.filter(codename=permission_name).exists():
            return True

        return False

    @classmethod
    def get_user_permissions(cls, user):
        """Get all permissions for a user"""
        if user.is_superuser:
            return list(Permission.objects.all())

        # Get permissions from groups
        group_permissions = Permission.objects.filter(group__user=user)

        # Get direct permissions
        user_permissions = user.user_permissions.all()

        # Combine and return unique permissions
        all_permissions = group_permissions.union(user_permissions)
        return list(all_permissions)

    @classmethod
    def check_department_access(cls, user, department):
        """Check if user has access to specific department"""
        if user.is_superuser:
            return True

        # Check if user is assigned to this department
        from accounts.models import UserDepartment
        return UserDepartment.objects.filter(
            user=user,
            department=department,
            is_active=True
        ).exists()

    @classmethod
    def check_object_ownership(cls, user, obj):
        """Check if user owns or has access to specific object"""
        if user.is_superuser:
            return True

        # Check various ownership patterns
        ownership_fields = ['created_by', 'user', 'dentist', 'assigned_to']

        for field in ownership_fields:
            if hasattr(obj, field):
                owner = getattr(obj, field)
                if owner == user:
                    return True
                # For dentist objects, check if user is the dentist
                if hasattr(user, 'dentist_profile') and owner == user.dentist_profile:
                    return True

        return False

class DepartmentPermissionMixin(UserPassesTestMixin):
    """
    Mixin to check department-level permissions
    """
    required_department_permission = None

    def test_func(self):
        user = self.request.user

        if user.is_superuser:
            return True

        if not self.required_department_permission:
            return True

        # Get the object to check department access
        obj = self.get_object() if hasattr(self, 'get_object') else None

        if obj and hasattr(obj, 'department'):
            return PermissionService.check_department_access(user, obj.department)

        return False

    def handle_no_permission(self):
        messages.error(
            self.request,
            _("You don't have permission to access this department.")
        )
        return redirect('home')

class ObjectOwnershipMixin(UserPassesTestMixin):
    """
    Mixin to check object ownership
    """

    def test_func(self):
        user = self.request.user
        obj = self.get_object()

        return PermissionService.check_object_ownership(user, obj)

    def handle_no_permission(self):
        messages.error(
            self.request,
            _("You don't have permission to access this resource.")
        )
        return redirect('home')

class EnhancedPermissionMixin(UserPassesTestMixin):
    """
    Enhanced permission mixin with multiple checks
    """
    required_permissions = []
    check_ownership = False
    check_department = False

    def test_func(self):
        user = self.request.user

        if user.is_superuser:
            return True

        # Check required permissions
        for permission in self.required_permissions:
            if not PermissionService.user_has_permission(user, permission):
                return False

        # Check ownership if required
        if self.check_ownership:
            obj = self.get_object() if hasattr(self, 'get_object') else None
            if obj and not PermissionService.check_object_ownership(user, obj):
                return False

        # Check department access if required
        if self.check_department:
            obj = self.get_object() if hasattr(self, 'get_object') else None
            if obj and hasattr(obj, 'department'):
                if not PermissionService.check_department_access(user, obj.department):
                    return False

        return True

    def handle_no_permission(self):
        messages.error(
            self.request,
            _("You don't have sufficient permissions to perform this action.")
        )
        return redirect('home')

# Decorators for function-based views
def require_permissions(permissions):
    """
    Decorator to require specific permissions
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            user = request.user

            if not user.is_authenticated:
                messages.error(request, _("You must be logged in to access this page."))
                return redirect('accounts:login')

            if user.is_superuser:
                return view_func(request, *args, **kwargs)

            # Check permissions
            for permission in permissions:
                if not PermissionService.user_has_permission(user, permission):
                    messages.error(
                        request,
                        _("You don't have permission to perform this action.")
                    )
                    return redirect('home')

            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

def require_role(role_name):
    """
    Decorator to require specific role
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            user = request.user

            if not user.is_authenticated:
                messages.error(request, _("You must be logged in to access this page."))
                return redirect('accounts:login')

            if user.is_superuser:
                return view_func(request, *args, **kwargs)

            # Check if user has the required role
            if not user.groups.filter(name=role_name).exists():
                messages.error(
                    request,
                    _("You don't have the required role to access this page.")
                )
                return redirect('home')

            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator

def require_department_access(view_func):
    """
    Decorator to check department access for objects
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        user = request.user

        if not user.is_authenticated:
            messages.error(request, _("You must be logged in to access this page."))
            return redirect('accounts:login')

        if user.is_superuser:
            return view_func(request, *args, **kwargs)

        # This decorator assumes the view will handle department checking
        # or that the object has department information
        return view_func(request, *args, **kwargs)
    return wrapper
