{% extends 'base.html' %}

{% block content %}
<div class="container mt-4 animate__animated animate__fadeIn">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}" class="text-decoration-none"><i class="bi bi-house-door"></i> Home</a></li>
            <li class="breadcrumb-item"><a href="{% url 'case:task_list' %}" class="text-decoration-none"><i class="bi bi-list-task"></i> Tasks</a></li>
            <li class="breadcrumb-item"><a href="{% url 'case:case_detail' task.case.case_number %}" class="text-decoration-none"><i class="bi bi-folder2-open"></i> Case #{{ task.case.case_number }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Task #{{ task.id }}</li>
        </ol>
    </nav>

    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="text-primary mb-0">
                <i class="bi bi-clipboard-check me-2"></i>{{ task.title }}
            </h2>
            <p class="text-muted mb-0"><small>Task ID: #{{ task.id }}</small></p>
        </div>
        <div class="d-flex gap-2">
            {% if task.status == 'pending' %}
            <a href="{% url 'case:task_start' task.id %}" class="btn btn-success">
                <i class="bi bi-play-fill"></i> Start Task
            </a>
            {% elif task.status == 'in_progress' %}
            <a href="{% url 'case:task_complete' task.id %}" class="btn btn-success">
                <i class="bi bi-check-lg"></i> Complete Task
            </a>
            {% endif %}
            <div class="btn-group">
                <a href="{% url 'case:task_update' task.id %}" class="btn btn-outline-primary">
                    <i class="bi bi-pencil-square"></i> Edit
                </a>
                <button type="button" class="btn btn-outline-danger" data-action="delete">
                    <i class="bi bi-trash"></i> Delete
                </button>
            </div>
        </div>
    </div>

    <!-- Main Card -->
    <div class="card shadow-sm task-detail-card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="priority-indicator priority-{{ task.priority }} me-3" data-bs-toggle="tooltip" title="Priority: {{ task.get_priority_display }}">
                        {% if task.priority == 4 %}
                            <i class="bi bi-exclamation-triangle-fill"></i>
                        {% elif task.priority == 3 %}
                            <i class="bi bi-arrow-up-circle-fill"></i>
                        {% elif task.priority == 2 %}
                            <i class="bi bi-dash-circle-fill"></i>
                        {% else %}
                            <i class="bi bi-arrow-down-circle-fill"></i>
                        {% endif %}
                    </div>
                    <h5 class="card-title mb-0">
                        Task Details
                    </h5>
                </div>
                <div class="status-badge-container">
                    <span class="badge status-badge status-{{ task.status }}">
                        <i class="bi bi-circle-fill me-1"></i>
                        {{ task.get_status_display }}
                    </span>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                <!-- Left Column: Task Information -->
                <div class="col-lg-8">
                    <!-- Task Details Grid -->
                    <div class="row g-4">
                        <!-- Case Information Section -->
                        <div class="col-md-6">
                            <div class="info-section">
                                <h6 class="section-title"><i class="bi bi-folder2 text-primary me-2"></i>Case Information</h6>
                                <div class="info-item">
                                    <div class="info-label">Case Number</div>
                                    <div class="info-value">
                                        <a href="{% url 'case:case_detail' task.case.case_number %}" class="text-decoration-none">
                                            <span class="badge bg-primary">#{{ task.case.case_number }}</span>
                                        </a>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Workflow Stage</div>
                                    <div class="info-value">
                                        <span class="badge bg-info">
                                            <i class="bi bi-diagram-3 me-1"></i>
                                            {{ task.workflow_stage.name }}
                                        </span>
                                    </div>
                                </div>

                                {% if task.case_item %}
                                <div class="info-item">
                                    <div class="info-label">Case Item</div>
                                    <div class="info-value">
                                        <i class="bi bi-box me-1"></i>
                                        {{ task.case_item.item.name }} ({{ task.case_item.quantity }} {{ task.case_item.unit.name }})
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Item Status</div>
                                    <div class="info-value">
                                        <span class="badge status-badge status-{{ task.case_item.status }}">
                                            <i class="bi bi-circle-fill me-1"></i>
                                            {{ task.case_item.get_status_display }}
                                        </span>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Assignment Section -->
                        <div class="col-md-6">
                            <div class="info-section">
                                <h6 class="section-title"><i class="bi bi-person text-primary me-2"></i>Assignment</h6>
                                <div class="info-item">
                                    <div class="info-label">Assigned To</div>
                                    <div class="info-value">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-2">
                                                <i class="bi bi-person-fill"></i>
                                            </div>
                                            <span>{{ task.assigned_to.get_full_name }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Created By</div>
                                    <div class="info-value">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-2 bg-light">
                                                <i class="bi bi-person-fill"></i>
                                            </div>
                                            <span>{{ task.created_by.get_full_name }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Estimated Duration</div>
                                    <div class="info-value">
                                        <i class="bi bi-hourglass-split me-1"></i>
                                        {{ task.estimated_duration }}
                                    </div>
                                </div>

                                {% if task.actual_duration %}
                                <div class="info-item">
                                    <div class="info-label">Actual Duration</div>
                                    <div class="info-value">
                                        <i class="bi bi-hourglass me-1"></i>
                                        {{ task.actual_duration }}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Timing Section -->
                        <div class="col-md-6">
                            <div class="info-section">
                                <h6 class="section-title"><i class="bi bi-calendar-event text-primary me-2"></i>Timing</h6>
                                {% if task.actual_start_time %}
                                <div class="info-item">
                                    <div class="info-label">Started</div>
                                    <div class="info-value">
                                        <i class="bi bi-play-circle me-1"></i>
                                        {{ task.actual_start_time|date:"F j, Y H:i" }}
                                    </div>
                                </div>
                                {% endif %}

                                {% if task.actual_end_time %}
                                <div class="info-item">
                                    <div class="info-label">Completed</div>
                                    <div class="info-value">
                                        <i class="bi bi-check-circle me-1"></i>
                                        {{ task.actual_end_time|date:"F j, Y H:i" }}
                                    </div>
                                </div>
                                {% endif %}

                                <div class="info-item">
                                    <div class="info-label">Created</div>
                                    <div class="info-value">
                                        <i class="bi bi-clock-history me-1"></i>
                                        {{ task.created_at|date:"F j, Y H:i" }}
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Last Updated</div>
                                    <div class="info-value">
                                        <i class="bi bi-clock me-1"></i>
                                        {{ task.updated_at|date:"F j, Y H:i" }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Section -->
                        <div class="col-md-6">
                            <div class="info-section">
                                <h6 class="section-title"><i class="bi bi-graph-up text-primary me-2"></i>Progress</h6>
                                <div class="info-item">
                                    <div class="info-label">Status</div>
                                    <div class="info-value">
                                        <span class="badge status-badge status-{{ task.status }}">
                                            <i class="bi bi-circle-fill me-1"></i>
                                            {{ task.get_status_display }}
                                        </span>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <div class="info-label">Progress</div>
                                    <div class="info-value w-100">
                                        <div class="progress" style="height: 10px;">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ task.progress }}%"
                                                aria-valuenow="{{ task.progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <small class="text-muted">{{ task.progress }}% complete</small>
                                    </div>
                                </div>

                                {% if task.quality_check_passed is not None %}
                                <div class="info-item">
                                    <div class="info-label">Quality Check</div>
                                    <div class="info-value">
                                        {% if task.quality_check_passed %}
                                            <span class="badge bg-success"><i class="bi bi-check-circle-fill me-1"></i> Passed</span>
                                        {% else %}
                                            <span class="badge bg-danger"><i class="bi bi-x-circle-fill me-1"></i> Failed</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Description Section -->
                    <div class="mt-4 info-section">
                        <h6 class="section-title"><i class="bi bi-file-text text-primary me-2"></i>Description</h6>
                        <div class="description-box">
                            {% if task.description %}
                                {{ task.description|linebreaks }}
                            {% else %}
                                <p class="text-muted mb-0"><em>No description provided</em></p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Notes Section -->
                    {% if task.notes %}
                    <div class="mt-4 info-section">
                        <h6 class="section-title"><i class="bi bi-journal-text text-primary me-2"></i>Notes</h6>
                        <div class="description-box">
                            {{ task.notes|linebreaks }}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Attachments Section -->
                    {% if task.attachments %}
                    <div class="mt-4 info-section">
                        <h6 class="section-title"><i class="bi bi-paperclip text-primary me-2"></i>Attachments</h6>
                        <div class="attachment-list">
                            <a href="{{ task.attachments.url }}" class="attachment-item" target="_blank">
                                <div class="attachment-icon">
                                    <i class="bi bi-file-earmark"></i>
                                </div>
                                <div class="attachment-info">
                                    <div class="attachment-name">{{ task.attachments.name|slice:"14:" }}</div>
                                    <div class="attachment-meta">Click to view</div>
                                </div>
                            </a>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Right Column: Task Timeline and Actions -->
                <div class="col-lg-4">
                    <!-- Task Actions Card -->
                    <div class="card mb-4 action-card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-lightning-charge me-2"></i>Task Actions</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                {% if task.status == 'pending' %}
                                <a href="{% url 'case:task_start' task.id %}" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <div class="action-icon bg-success text-white">
                                        <i class="bi bi-play-fill"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-bold">Start Task</div>
                                        <small class="text-muted">Mark this task as in progress</small>
                                    </div>
                                </a>
                                {% elif task.status == 'in_progress' %}
                                <a href="{% url 'case:task_complete' task.id %}" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <div class="action-icon bg-success text-white">
                                        <i class="bi bi-check-lg"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-bold">Complete Task</div>
                                        <small class="text-muted">Mark this task as completed</small>
                                    </div>
                                </a>
                                {% endif %}

                                <a href="{% url 'case:task_update' task.id %}" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <div class="action-icon bg-primary text-white">
                                        <i class="bi bi-pencil-square"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-bold">Edit Task</div>
                                        <small class="text-muted">Modify task details</small>
                                    </div>
                                </a>

                                <a href="#" class="list-group-item list-group-item-action d-flex align-items-center" data-action="delete">
                                    <div class="action-icon bg-danger text-white">
                                        <i class="bi bi-trash"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-bold">Delete Task</div>
                                        <small class="text-muted">Remove this task permanently</small>
                                    </div>
                                </a>

                                <a href="{% url 'scheduling:schedule_create' %}?task={{ task.id }}" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <div class="action-icon bg-info text-white">
                                        <i class="bi bi-calendar-plus"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-bold">Schedule Task</div>
                                        <small class="text-muted">Add this task to the schedule</small>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Related Items Card -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-link-45deg me-2"></i>Related Items</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                <a href="{% url 'case:case_detail' task.case.case_number %}" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <div class="action-icon bg-primary text-white">
                                        <i class="bi bi-folder2"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-bold">Case #{{ task.case.case_number }}</div>
                                        <small class="text-muted">View parent case</small>
                                    </div>
                                </a>

                                {% if task.case_item %}
                                <a href="{% url 'case:case_detail' task.case.case_number %}#items" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <div class="action-icon bg-success text-white">
                                        <i class="bi bi-box"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-bold">{{ task.case_item.item.name }}</div>
                                        <small class="text-muted">View case item details</small>
                                    </div>
                                </a>
                                {% endif %}

                                <a href="{% url 'scheduling:schedule_list' %}?task={{ task.id }}" class="list-group-item list-group-item-action d-flex align-items-center">
                                    <div class="action-icon bg-info text-white">
                                        <i class="bi bi-calendar-week"></i>
                                    </div>
                                    <div class="ms-3">
                                        <div class="fw-bold">Schedule</div>
                                        <small class="text-muted">View task in schedule</small>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card Footer -->
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{% url 'case:task_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i> Back to Task List
                    </a>
                    <a href="{% url 'case:case_detail' task.case.case_number %}" class="btn btn-outline-primary ms-2">
                        <i class="bi bi-folder2 me-1"></i> Back to Case
                    </a>
                </div>
                <div class="d-flex align-items-center">
                    <div class="activity-indicator me-2" data-bs-toggle="tooltip" title="Last updated {{ task.updated_at|date:'F j, Y H:i' }}">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <small class="text-muted">
                        Last activity: {{ task.updated_at|timesince }} ago
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    Confirm Delete
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this task?</p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-circle me-2"></i>
                    This action cannot be undone. All data associated with this task will be permanently removed.
                </div>
                <div class="task-summary p-3 bg-light rounded mb-3">
                    <div><strong>Task:</strong> {{ task.title }}</div>
                    <div><strong>Case:</strong> #{{ task.case.case_number }}</div>
                    <div><strong>Status:</strong> {{ task.get_status_display }}</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i> Cancel
                </button>
                <form action="{% url 'case:task_delete' task.id %}" method="POST" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-1"></i> Delete Task
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
    /* Color Variables */
    :root {
        --primary: #4f46e5;
        --primary-light: #6366f1;
        --primary-dark: #4338ca;
        --secondary: #64748b;
        --success: #22c55e;
        --success-light: #86efac;
        --danger: #ef4444;
        --danger-light: #fca5a5;
        --warning: #f59e0b;
        --warning-light: #fcd34d;
        --info: #3b82f6;
        --info-light: #93c5fd;
        --gray-50: #f8fafc;
        --gray-100: #f1f5f9;
        --gray-200: #e2e8f0;
        --gray-300: #cbd5e1;
        --gray-400: #94a3b8;
        --gray-500: #64748b;
        --gray-600: #475569;
        --gray-700: #334155;
        --gray-800: #1e293b;
        --gray-900: #0f172a;
    }

    /* Card Styles */
    .task-detail-card {
        border: none;
        border-radius: 1rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .task-detail-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .card-header {
        background: linear-gradient(to right, var(--primary-dark), var(--primary));
        color: white;
        border-bottom: none;
        padding: 1rem 1.5rem;
    }

    .card-footer {
        background-color: var(--gray-50);
        border-top: 1px solid var(--gray-200);
        padding: 1rem 1.5rem;
    }

    /* Info Section Styles */
    .info-section {
        background-color: var(--gray-50);
        border-radius: 0.75rem;
        padding: 1.25rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
    }

    .info-section:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-2px);
    }

    .section-title {
        color: var(--gray-700);
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .info-item {
        display: flex;
        margin-bottom: 0.75rem;
        align-items: flex-start;
    }

    .info-label {
        width: 40%;
        color: var(--gray-600);
        font-weight: 500;
        font-size: 0.875rem;
    }

    .info-value {
        width: 60%;
        color: var(--gray-800);
        font-weight: 500;
    }

    /* Description Box */
    .description-box {
        background-color: white;
        border: 1px solid var(--gray-200);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 0.5rem;
    }

    /* Status Badge Styles */
    .status-badge {
        padding: 0.5rem 0.75rem;
        border-radius: 0.5rem;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .status-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .status-pending {
        background-color: var(--primary);
        color: white;
    }

    .status-in_progress {
        background-color: var(--warning);
        color: var(--gray-900);
    }

    .status-completed {
        background-color: var(--success);
        color: white;
    }

    .status-cancelled {
        background-color: var(--danger);
        color: white;
    }

    .status-delayed {
        background-color: var(--danger-light);
        color: var(--gray-900);
    }

    .status-blocked {
        background-color: var(--gray-500);
        color: white;
    }

    .status-review {
        background-color: var(--info);
        color: white;
    }

    /* Priority Indicator */
    .priority-indicator {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .priority-1 { /* Low */
        background-color: var(--success-light);
        color: var(--success);
    }

    .priority-2 { /* Medium */
        background-color: var(--info-light);
        color: var(--info);
    }

    .priority-3 { /* High */
        background-color: var(--warning-light);
        color: var(--warning);
    }

    .priority-4 { /* Urgent */
        background-color: var(--danger-light);
        color: var(--danger);
    }

    /* Action Card Styles */
    .action-card {
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
    }

    .action-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .action-icon {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
    }

    .list-group-item-action {
        transition: all 0.2s ease;
    }

    .list-group-item-action:hover {
        background-color: var(--gray-100);
        transform: translateX(5px);
    }

    /* Avatar Circle */
    .avatar-circle {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        background-color: var(--primary-light);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    /* Attachment Styles */
    .attachment-list {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .attachment-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        border-radius: 0.5rem;
        background-color: white;
        border: 1px solid var(--gray-200);
        text-decoration: none;
        color: var(--gray-800);
        transition: all 0.2s ease;
    }

    .attachment-item:hover {
        background-color: var(--gray-100);
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .attachment-icon {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 0.5rem;
        background-color: var(--primary-light);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        margin-right: 0.75rem;
    }

    .attachment-info {
        flex: 1;
    }

    .attachment-name {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .attachment-meta {
        font-size: 0.75rem;
        color: var(--gray-500);
    }

    /* Activity Indicator */
    .activity-indicator {
        width: 1.75rem;
        height: 1.75rem;
        border-radius: 50%;
        background-color: var(--gray-200);
        color: var(--gray-600);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }

    /* Animation Classes */
    .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: .5;
        }
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .info-item {
            flex-direction: column;
        }

        .info-label, .info-value {
            width: 100%;
        }

        .info-label {
            margin-bottom: 0.25rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enable tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })

        // Delete confirmation using modal
        var deleteButtons = document.querySelectorAll('[data-action="delete"]');
        deleteButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                var deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                deleteModal.show();
            });
        });

        // Add hover effects to info sections
        var infoSections = document.querySelectorAll('.info-section');
        infoSections.forEach(function(section) {
            section.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'var(--gray-100)';
            });

            section.addEventListener('mouseleave', function() {
                this.style.backgroundColor = 'var(--gray-50)';
            });
        });

        // Add animation to status badge
        var statusBadge = document.querySelector('.status-badge');
        if (statusBadge) {
            statusBadge.classList.add('animate__animated', 'animate__pulse');
        }

        // Add progress animation
        var progressBar = document.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = '0%';
            setTimeout(function() {
                progressBar.style.width = progressBar.getAttribute('aria-valuenow') + '%';
                progressBar.style.transition = 'width 1s ease-in-out';
            }, 300);
        }
    });
</script>
{% endblock %}