{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Stock Alerts Dashboard" %}{% endblock %}

{% block content %}
<div class="container-fluid my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">
            <i class="bi bi-exclamation-triangle"></i> {% trans "Stock Alerts Dashboard" %}
        </h1>
        <div>
            <a href="{% url 'items:material_requirements_planning' %}" class="btn btn-info me-2">
                <i class="bi bi-diagram-3"></i> {% trans "MRP Dashboard" %}
            </a>
            <a href="{% url 'items:purchase_recommendations' %}" class="btn btn-primary">
                <i class="bi bi-cart-plus"></i> {% trans "Purchase Recommendations" %}
            </a>
        </div>
    </div>

    <!-- Alert Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">{% trans "Critical Alerts" %}</h6>
                            <h3 class="mb-0">{{ alert_summary.critical_count }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-exclamation-triangle-fill fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">{% trans "Low Stock" %}</h6>
                            <h3 class="mb-0">{{ alert_summary.low_count }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-exclamation-circle-fill fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">{% trans "Reorder Needed" %}</h6>
                            <h3 class="mb-0">{{ alert_summary.reorder_count }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-arrow-repeat fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">{% trans "Overstocked" %}</h6>
                            <h3 class="mb-0">{{ alert_summary.overstocked_count }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-box-seam fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Critical Alerts -->
    {% if critical_alerts %}
        <div class="card mb-4">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle-fill"></i> {% trans "Critical Alerts - Immediate Action Required" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{% trans "Item" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Current Stock" %}</th>
                                <th>{% trans "Threshold" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Action" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for alert in critical_alerts %}
                                <tr>
                                    <td>
                                        <strong>{{ alert.item.name }}</strong>
                                        {% if alert.item.description %}
                                            <br><small class="text-muted">{{ alert.item.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {% if alert.type == 'raw_material' %}
                                                {% trans "Raw Material" %}
                                            {% else %}
                                                {% trans "Finished Item" %}
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        {% if alert.severity == 'out_of_stock' %}
                                            <span class="text-danger fw-bold">{% trans "OUT OF STOCK" %}</span>
                                        {% else %}
                                            {{ alert.current_stock }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if alert.type == 'raw_material' %}
                                            {{ alert.reorder_point }}
                                        {% else %}
                                            {{ alert.minimum_stock }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if alert.severity == 'out_of_stock' %}
                                            <span class="badge bg-danger">{% trans "Out of Stock" %}</span>
                                        {% elif alert.severity == 'critical' %}
                                            <span class="badge bg-danger">{% trans "Critical Low" %}</span>
                                        {% elif alert.severity == 'no_inventory_record' %}
                                            <span class="badge bg-warning">{% trans "No Inventory Record" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if alert.type == 'raw_material' %}
                                            <a href="{% url 'items:purchase_recommendations' %}" class="btn btn-sm btn-danger">
                                                <i class="bi bi-cart-plus"></i> {% trans "Purchase" %}
                                            </a>
                                        {% else %}
                                            <a href="{% url 'items:item_detail' alert.item.id %}" class="btn btn-sm btn-info">
                                                <i class="bi bi-eye"></i> {% trans "View" %}
                                            </a>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Low Stock Alerts -->
    {% if low_alerts %}
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-circle"></i> {% trans "Low Stock Alerts" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{% trans "Item" %}</th>
                                <th>{% trans "Current Stock" %}</th>
                                <th>{% trans "Threshold" %}</th>
                                <th>{% trans "Percentage" %}</th>
                                <th>{% trans "Action" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for alert in low_alerts %}
                                <tr>
                                    <td>
                                        <strong>{{ alert.item.name }}</strong>
                                        <span class="badge bg-secondary ms-2">
                                            {% if alert.type == 'raw_material' %}
                                                {% trans "Raw Material" %}
                                            {% else %}
                                                {% trans "Item" %}
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>{{ alert.current_stock }}</td>
                                    <td>
                                        {% if alert.type == 'raw_material' %}
                                            {{ alert.reorder_point }}
                                        {% else %}
                                            {{ alert.minimum_stock }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="progress" style="width: 100px;">
                                            <div class="progress-bar bg-warning" role="progressbar" 
                                                 style="width: {{ alert.percentage }}%" 
                                                 aria-valuenow="{{ alert.percentage }}" 
                                                 aria-valuemin="0" aria-valuemax="100">
                                                {{ alert.percentage|floatformat:0 }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="{% url 'items:purchase_recommendations' %}" class="btn btn-sm btn-warning">
                                            <i class="bi bi-cart-plus"></i> {% trans "Order" %}
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Reorder Alerts -->
    {% if reorder_alerts %}
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-arrow-repeat"></i> {% trans "Reorder Point Reached" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for alert in reorder_alerts %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title">{{ alert.item.name }}</h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            {% trans "Current:" %} {{ alert.current_stock }}<br>
                                            {% trans "Reorder Point:" %} 
                                            {% if alert.type == 'raw_material' %}
                                                {{ alert.reorder_point }}
                                            {% else %}
                                                {{ alert.minimum_stock }}
                                            {% endif %}
                                        </small>
                                    </p>
                                    <a href="{% url 'items:purchase_recommendations' %}" class="btn btn-sm btn-info">
                                        {% trans "Reorder" %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Overstocked Items -->
    {% if overstocked_alerts %}
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-box-seam"></i> {% trans "Overstocked Items" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    {% trans "These items have stock levels significantly above their minimum requirements. Consider reducing future orders or using them in upcoming projects." %}
                </div>
                <div class="row">
                    {% for alert in overstocked_alerts %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-secondary">
                                <div class="card-body">
                                    <h6 class="card-title">{{ alert.item.name }}</h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            {% trans "Current:" %} {{ alert.current_stock }}<br>
                                            {% trans "Minimum:" %} {{ alert.minimum_stock }}
                                        </small>
                                    </p>
                                    <span class="badge bg-secondary">{% trans "Overstocked" %}</span>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- No Alerts -->
    {% if total_alerts == 0 %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                <h3 class="mt-3 text-success">{% trans "All Stock Levels Normal" %}</h3>
                <p class="text-muted">{% trans "No stock alerts at this time. All inventory levels are within acceptable ranges." %}</p>
                <a href="{% url 'items:item_inventory' %}" class="btn btn-outline-primary">
                    {% trans "View Inventory" %}
                </a>
            </div>
        </div>
    {% endif %}
</div>

<script>
// Auto-refresh the page every 5 minutes to keep alerts current
setTimeout(function() {
    location.reload();
}, 300000); // 5 minutes
</script>
{% endblock %}
