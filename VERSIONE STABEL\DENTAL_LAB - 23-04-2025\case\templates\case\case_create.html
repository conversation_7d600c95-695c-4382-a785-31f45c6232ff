{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% load i18n %}
{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
<style>
    /* Modern Design System */
    :root {
        --primary-color: #4361ee;
        --primary-hover: #3a56d4;
        --primary-light: #eef2ff;
        --primary-ultra-light: #f5f8ff;
        --secondary-color: #3f37c9;
        --accent-color: #4cc9f0;
        --success-color: #0cce6b;
        --success-hover: #0ab25d;
        --warning-color: #ff9800;
        --danger-color: #f44336;
        --danger-hover: #e53935;
        --light-gray: #f8f9fa;
        --medium-gray: #e9ecef;
        --dark-gray: #495057;
        --text-primary: #212529;
        --text-secondary: #6c757d;
        --text-muted: #939dab;
        --border-radius: 0.5rem;
        --border-radius-sm: 0.375rem;
        --border-radius-lg: 0.75rem;
        --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        --box-shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.08);
        --transition: all 0.25s ease-in-out;
        --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }
body {
    font-family: var(--font-family);
    color: var(--text-primary);
}

/* Page Layout */
.page-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 1.75rem;
    width: 95%;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.75rem;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    background-color: white;
    box-shadow: var(--box-shadow);
}

.page-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.625rem;
    letter-spacing: -0.02em;
}

.page-title i {
    color: var(--primary-color);
    background-color: var(--primary-ultra-light);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.form-layout {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 1.75rem;
}

.col-span-5 {
    grid-column: span 5;
}

.col-span-7 {
    grid-column: span 7;
}

/* Cards */
.card {
    background-color: #fff;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid var(--medium-gray);
    margin-bottom: 1.75rem;
    overflow: hidden;
    position: relative;
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    border-color: rgba(67, 97, 238, 0.1);
}

.card-header {
    background-color: #fff;
    padding: 1.125rem 1.5rem;
    border-bottom: 1px solid var(--medium-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.625rem;
    color: var(--text-primary);
}

.card-header h3 i {
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

/* Form Sections */
.form-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--medium-gray);
}

.form-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.form-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.125rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-section-title i {
    color: var(--primary-color);
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--medium-gray);
    padding: 0.625rem 0.875rem;
    transition: var(--transition);
    font-size: 0.9375rem;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.02);
    background-color: #ffffff;
}

.form-control:hover, .form-select:hover {
    border-color: #b9c3d0;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.15);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.9375rem;
}

.select2-container--bootstrap-5 .select2-selection {
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--medium-gray);
    min-height: 41px;
    padding: 0.375rem 0;
    font-size: 0.9375rem;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.select2-container--bootstrap-5 .select2-selection__rendered {
    padding-left: 0.875rem;
}

.select2-container--bootstrap-5.select2-container--focus .select2-selection {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.15);
}

.select2-container--bootstrap-5 .select2-dropdown {
    border-color: var(--primary-color);
    border-radius: var(--border-radius-sm);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.select2-container--bootstrap-5 .select2-dropdown .select2-results__option--highlighted[aria-selected] {
    background-color: var(--primary-ultra-light);
    color: var(--primary-color);
}

.select2-container--bootstrap-5 .select2-dropdown .select2-results__option[aria-selected=true] {
    background-color: var(--primary-color);
    color: white;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius-sm);
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    transition: var(--transition);
    font-size: 0.9375rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn i {
    font-size: 0.875rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover, .btn-success:focus {
    background-color: var(--success-hover);
    border-color: var(--success-hover);
}

.btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--medium-gray);
    background-color: white;
}

.btn-outline-secondary:hover, .btn-outline-secondary:focus {
    background-color: var(--light-gray);
    color: var(--text-primary);
    border-color: var(--medium-gray);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: white;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--primary-ultra-light);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn-icon {
    width: 2.25rem;
    height: 2.25rem;
    padding: 0;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-icon i {
    font-size: 1rem;
}

/* Teeth Selection */
.dental-chart-wrapper {
    position: relative;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
    border: 1px dashed var(--medium-gray);
    transition: all 0.3s ease;
}

.dental-chart-wrapper:hover {
    background-color: #f0f2f5;
    border-color: var(--primary-color);
}

.dental-chart {
    max-width: 100%;
    height: auto;
    margin: 0 auto;
    display: block;
}

.tooth {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    stroke: #cccccc;
    stroke-width: 1;
}

.tooth:hover {
    filter: brightness(0.95);
    transform: scale(1.03);
}

.tooth.selected {
    fill: var(--primary-color);
    stroke: var(--primary-color);
    stroke-width: 1.5;
}

.tooth.deselected {
    fill: #76f5f1;
}



.selected-teeth-container {
    background-color: var(--primary-ultra-light);
    border: 1px solid rgba(67, 97, 238, 0.2);
    border-radius: var(--border-radius);
    padding: 1.25rem;
    margin-top: 1.25rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    animation: fadeIn 0.5s ease;
}

.selected-teeth-display {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.selected-teeth-display .badge {
    background-color: var(--primary-color);
    color: white;
    font-weight: 500;
    padding: 0.375em 0.75em;
    border-radius: var(--border-radius-sm);
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
    animation: bounceIn 0.3s ease;
}

.teeth-selection-buttons {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.teeth-selection-buttons .btn {
    flex: 1;
    min-width: 120px;
}

/* Formset Items */
.formset-item {
    position: relative;
    background-color: #fff;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
}

.formset-item:hover {
    border-color: rgba(67, 97, 238, 0.2);
    box-shadow: var(--box-shadow-lg);
}

.btn-remove {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
    opacity: 0.9;
    transition: var(--transition);
    z-index: 5;
}

.btn-remove:hover {
    opacity: 1;
    background-color: var(--danger-hover);
    transform: scale(1.05);
}

.empty-state {
    padding: 3rem 1.5rem;
    text-align: center;
    border: 2px dashed var(--medium-gray);
    border-radius: var(--border-radius);
    background-color: var(--light-gray);
}

.empty-state-icon {
    margin-bottom: 1.25rem;
    color: var(--text-muted);
}

.empty-state-icon i {
    font-size: 3rem;
    opacity: 0.5;
}

.empty-state p {
    color: var(--text-secondary);
    font-size: 1rem;
    max-width: 80%;
    margin: 0 auto;
}

/* Enhanced Form Rows */
.row {
    margin-left: -0.75rem;
    margin-right: -0.75rem;
}

.row > * {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
    border: none;
    box-shadow: var(--box-shadow);
}

.alert-info {
    background-color: #e6f6ff;
    color: #0c5d8e;
    border-left: 4px solid #4cc9f0;
}

.alert i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

/* Custom File Upload */
.custom-file-upload input[type="file"] {
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--medium-gray);
    width: 100%;
}

/* Enhanced Patient Selection */
.patient-selection {
    position: relative;
}

/* Responsive Adjustments */
@media (max-width: 1199px) {
    .form-layout {
        grid-template-columns: repeat(9, 1fr);
    }

    .col-span-5 {
        grid-column: span 4;
    }

    .col-span-7 {
        grid-column: span 5;
    }
}

@media (max-width: 992px) {
    .form-layout {
        grid-template-columns: 1fr;
    }

    .col-span-5, .col-span-7 {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .page-actions {
        width: 100%;
    }

    .page-actions .btn {
        width: 100%;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .card-header .btn-group {
        align-self: flex-end;
        margin-top: -2.5rem;
    }
}

@media (max-width: 576px) {
    .page-container {
        padding: 1rem;
    }

    .card-body, .card-header {
        padding: 1rem;
    }

    .col-md-3, .col-md-6, .col-md-9, .col-md-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1050;
}

.toast {
    min-width: 300px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    border: none;
    color: white;
    padding: 0;
    overflow: hidden;
}

.toast.bg-success {
    background-color: var(--success-color) !important;
}

.toast.bg-error {
    background-color: var(--danger-color) !important;
}

.toast.bg-info {
    background-color: var(--accent-color) !important;
}

.toast-body {
    padding: 0.875rem 1.25rem;
    display: flex;
    align-items: center;
}

.toast .btn-close {
    padding: 0.875rem 1.25rem;
    margin: 0;
    color: white;
    opacity: 0.8;
}

.toast .btn-close:hover {
    opacity: 1;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes bounceIn {
    0% { opacity: 0; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.05); }
    100% { transform: scale(1); }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease forwards;
}

.animate-bounce-in {
    animation: bounceIn 0.4s ease forwards;
}

/* Loading Overlay */
#loading-overlay {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

#loading-overlay .spinner-border {
    width: 3rem;
    height: 3rem;
    color: var(--primary-color);
}

#loading-overlay p {
    color: var(--text-primary);
    font-weight: 500;
    margin-top: 1rem;
}

/* Required field indicator */
.required:after {
    content: '*';
    color: var(--danger-color);
    margin-left: 4px;
}

/* Patient Modal */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: 1px solid var(--medium-gray);
    padding: 1.25rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--medium-gray);
    padding: 1.25rem 1.5rem;
}

.modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Card transitions */
.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}
</style>
{% endblock %}
{% block content %}
<div class="page-container">
    <!-- Page Header -->
    <div class="page-header animate__animated animate__fadeIn">
        <h1 class="page-title">
            <i class="fas fa-plus-circle"></i> {% trans "Create New Case" %}
        </h1>
        <div class="page-actions">
            <a href="{% url 'case:case_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i> {% trans "View All Cases" %}
            </a>
        </div>
    </div>
<form method="post" enctype="multipart/form-data" id="case-create-form">
    {% csrf_token %}

    <!-- Hidden field for teeth selection -->
    <input type="hidden" id="selected_teeth_input" name="selected_teeth_input" value="{{ form.initial.selected_teeth_input }}">

    <div class="form-layout">
        <!-- Left Column - Basic Info & Teeth Selection -->
        <div class="col-span-5">
            <!-- Basic Information Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3><i class="fas fa-info-circle"></i> Basic Information</h3>
                </div>
                <div class="card-body">
                    <!-- Patient Selection with Add New Button -->
                    <div class="form-section">
                        <div class="form-section-title">
                            <i class="fas fa-user"></i> Patient Information
                        </div>
                        <div class="patient-selection">
                            <div class="row mb-3">
                                <div class="col-md-9">
                                    <label for="id_patient" class="form-label required">Patient</label>
                                    <select name="patient" id="id_patient" class="form-control select2" required>
                                        <option value="">Select a patient</option>
                                        {% for patient in patients %}
                                            <option value="{{ patient.id }}" {% if form.patient.value == patient.id %}selected{% endif %}>
                                                {{ patient.get_full_name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                    {% if form.patient.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.patient.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="button" class="btn btn-primary w-100" onclick="openPatientModal()">
                                        <i class="fas fa-plus"></i> New
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Dentist Selection -->
                        <div class="row mb-3">
                            <div class="col-12">
                                {{ form.dentist|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <!-- Case Details -->
                    <div class="form-section">
                        <div class="form-section-title">
                            <i class="fas fa-clipboard-list"></i> Case Details
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.status|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.priority|as_crispy_field }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.workflow_template|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.responsible_department|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <!-- Dates Section -->
                    <div class="form-section">
                        <div class="form-section-title">
                            <i class="fas fa-calendar-alt"></i> Important Dates
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.received_date_time|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.deadline|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Teeth Selection Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3><i class="fas fa-tooth"></i> {% trans "Teeth Selection" %}</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle"></i> {% trans "Click on teeth to select them. Selected teeth will be highlighted in blue." %}
                    </div>

                    <div class="teeth-selection-buttons">
                        <button type="button" class="btn btn-outline-primary" id="selectAllUpper">
                            <i class="fas fa-chevron-up"></i> {% trans "Upper Teeth" %}
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="selectAllLower">
                            <i class="fas fa-chevron-down"></i> {% trans "Lower Teeth" %}
                        </button>
                        <button type="button" class="btn btn-outline-danger" id="clearAll">
                            <i class="fas fa-times"></i> {% trans "Clear All" %}
                        </button>
                    </div>

                    <div class="dental-chart-wrapper">
                        <!-- Dental chart -->
                        <div class="dental-chart-container">
                            {% include 'case/select_tooth.html' %}
                        </div>

                        <!-- Teeth selection instructions -->
                        <div class="text-center mt-3 text-muted small">
                            <i class="fas fa-info-circle"></i> {% trans "Click on individual teeth to select/deselect them, or use the buttons above to select groups." %}
                        </div>
                    </div>

                    <div class="selected-teeth-container mt-4">
                        <h5 class="mb-3"><i class="fas fa-check-circle text-success"></i> {% trans "Selected Teeth" %}</h5>
                        <div id="selectedTeethDisplay" class="selected-teeth-display text-muted">{% trans "None selected" %}</div>
                    </div>

                    <div class="teeth-color-section mt-4">
                        <div class="form-section-title">
                            <i class="fas fa-palette"></i> {% trans "Teeth Color" %}
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.teeth_color|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Items & Additional Info -->
        <div class="col-span-7">
            <!-- Treatment Items Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3><i class="fas fa-list-ul"></i> {% trans "Treatment Items" %}</h3>
                    <button type="button" id="add-item" class="btn btn-primary">
                        <i class="fas fa-plus"></i> {% trans "Add Item" %}
                    </button>
                </div>
                <div class="card-body">
                    <!-- Formset Management Form -->
                    <input type="hidden" name="case_items-TOTAL_FORMS" value="{{ formset|length }}" id="id_case_items-TOTAL_FORMS">
                    <input type="hidden" name="case_items-INITIAL_FORMS" value="{{ formset.initial_forms|length }}" id="id_case_items-INITIAL_FORMS">
                    <input type="hidden" name="case_items-MIN_NUM_FORMS" value="0" id="id_case_items-MIN_NUM_FORMS">
                    <input type="hidden" name="case_items-MAX_NUM_FORMS" value="1000" id="id_case_items-MAX_NUM_FORMS">

                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle"></i> {% trans "Add treatment items for this case. Each item will be tracked separately in the workflow." %}
                    </div>

                    <div id="formset-container" class="row">
                        {% for form in formset %}
                            <div class="col-md-6 mb-3 animate__animated animate__fadeIn">
                                <div class="formset-item">
                                    {{ form|crispy }}
                                    <button type="button" class="btn-remove btn btn-sm btn-danger" title="{% trans 'Remove item' %}">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        {% empty %}
                            <div class="col-12 empty-state animate__animated animate__fadeIn">
                                <div class="empty-state-icon mb-3">
                                    <i class="fas fa-clipboard-list fa-3x"></i>
                                </div>
                                <p>{% trans "No items added yet. Click 'Add Item' to add treatment items." %}</p>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Additional Information Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h3><i class="fas fa-file-alt"></i> {% trans "Additional Information" %}</h3>
                </div>
                <div class="card-body">
                    <div class="form-section">
                        <div class="form-section-title">
                            <i class="fas fa-sticky-note"></i> {% trans "Notes & Requirements" %}
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                {{ form.notes|as_crispy_field }}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                {{ form.special_requirements|as_crispy_field }}
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <div class="form-section-title">
                            <i class="fas fa-paperclip"></i> {% trans "Attachments" %}
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="custom-file-upload">
                                    {{ form.attachments|as_crispy_field }}
                                    <small class="form-text text-muted">
                                        {% trans "Upload images, documents, or other files related to this case." %}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Section -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="alert alert-warning mb-4">
                        <i class="fas fa-exclamation-triangle"></i> {% trans "Please review all information before creating the case. Make sure you have selected teeth and added at least one treatment item." %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'case:case_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> {% trans "Cancel" %}
                        </a>
                        <button type="submit" class="btn btn-lg btn-success animate__animated animate__pulse animate__infinite animate__slower">
                            <i class="fas fa-save"></i> {% trans "Create Case" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
</div>
<!-- New Patient Modal -->
<div class="modal fade" id="patientModal" tabindex="-1" aria-labelledby="patientModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content animate__animated animate__fadeInUp">
            <div class="modal-header">
                <h5 class="modal-title" id="patientModalLabel">
                    <i class="fas fa-user-plus text-primary"></i> {% trans "Add New Patient" %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" onclick="closePatientModal()"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle"></i> {% trans "Fill in the patient details. Fields marked with * are required." %}
                </div>

                <div class="mb-3">
                    <label for="new_patient_first_name" class="form-label required">{% trans "First Name" %}</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input type="text" class="form-control" id="new_patient_first_name" required>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="new_patient_last_name" class="form-label required">{% trans "Last Name" %}</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input type="text" class="form-control" id="new_patient_last_name" required>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="new_patient_phone" class="form-label">{% trans "Phone Number" %}</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-phone"></i></span>
                        <input type="tel" class="form-control" id="new_patient_phone">
                    </div>
                </div>
                <div class="mb-3">
                    <label for="new_patient_email" class="form-label">{% trans "Email" %}</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                        <input type="email" class="form-control" id="new_patient_email">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" onclick="closePatientModal()">
                    <i class="fas fa-times"></i> {% trans "Cancel" %}
                </button>
                <button type="button" class="btn btn-primary" onclick="saveNewPatient()">
                    <i class="fas fa-save"></i> {% trans "Save Patient" %}
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Loading Overlay -->
<div id="loading-overlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="z-index: 9999;">
    <div class="d-flex flex-column align-items-center justify-content-center h-100">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="visually-hidden">{% trans "Loading..." %}</span>
        </div>
        <p class="mt-3 h5">{% trans "Processing your request..." %}</p>
        <div class="progress mt-3" style="width: 200px; height: 8px;">
            <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary" role="progressbar" style="width: 100%"></div>
        </div>
    </div>
</div>
<!-- Toast Container -->
<div class="toast-container"></div>
{% endblock %}
{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 for patient dropdown
    $('#id_patient').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select a patient',
        allowClear: true,
        width: '100%'
    });

    // Initialize other select2 dropdowns
    $('.select2').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });

    // Form validation
    setupFormValidation();

    // Add event listeners to remove buttons
    setupRemoveButtons();

    // Update teeth display
    updateTeethDisplay();

    // Setup teeth selection buttons
    setupTeethSelectionButtons();

    // Prevent form submission on Enter key (except in modal)
    preventFormSubmitOnEnter();
});

// Form validation setup
function setupFormValidation() {
    const caseForm = document.getElementById('case-create-form');
    if (caseForm) {
        caseForm.addEventListener('submit', function(e) {
            // Show loading overlay
            document.getElementById('loading-overlay').classList.remove('d-none');

            // Validate teeth selection
            const selectedTeethInput = document.getElementById('selected_teeth_input');
            const selectedTeeth = selectedTeethInput ? selectedTeethInput.value : '';

            if (!selectedTeeth) {
                e.preventDefault();
                document.getElementById('loading-overlay').classList.add('d-none');
                showNotification('Please select at least one tooth', 'error');
                scrollToElement(document.querySelector('.dental-chart-container'));
                return false;
            }

            // Validate patient selection
            const patientSelect = document.getElementById('id_patient');
            if (!patientSelect.value) {
                e.preventDefault();
                document.getElementById('loading-overlay').classList.add('d-none');
                showNotification('Please select a patient', 'error');
                scrollToElement(patientSelect);
                return false;
            }

            // Validate if at least one treatment item exists
            const formsetContainer = document.getElementById('formset-container');
            const formItems = formsetContainer.querySelectorAll('.formset-item:not(.deleted)');

            if (formItems.length === 0) {
                e.preventDefault();
                document.getElementById('loading-overlay').classList.add('d-none');
                showNotification('Please add at least one treatment item', 'error');
                scrollToElement(document.getElementById('add-item'));
                return false;
            }

            return true;
        });
    }
}

// Setup teeth selection buttons
function setupTeethSelectionButtons() {
    // Select all upper teeth
    document.getElementById('selectAllUpper').addEventListener('click', function() {
        // Check if the select_tooth.html script has initialized its own functions
        if (window.selectedTeeth !== undefined) {
            // Use the functions from select_tooth.html
            const teethRanges = {
                upper: [{ start: 11, end: 18 }, { start: 21, end: 28 }]
            };

            teethRanges.upper.forEach(function(range) {
                for (let i = range.start; i <= range.end; i++) {
                    if (!window.selectedTeeth.includes(i.toString())) {
                        window.selectedTeeth.push(i.toString());
                        $(`.tooth-${i}-parent`).css('fill', 'red');
                    }
                }
            });

            // Call the update functions from select_tooth.html
            if (typeof window.updateSelectedTeethInput === 'function') {
                window.updateSelectedTeethInput();
            }

            if (typeof window.updateSelectedTeethDisplay === 'function') {
                window.updateSelectedTeethDisplay();
            }
        }

        // Also update our own display
        updateTeethDisplay();
        showNotification('All upper teeth selected', 'info');
    });

    // Select all lower teeth
    document.getElementById('selectAllLower').addEventListener('click', function() {
        // Check if the select_tooth.html script has initialized its own functions
        if (window.selectedTeeth !== undefined) {
            // Use the functions from select_tooth.html
            const teethRanges = {
                lower: [{ start: 31, end: 38 }, { start: 41, end: 48 }]
            };

            teethRanges.lower.forEach(function(range) {
                for (let i = range.start; i <= range.end; i++) {
                    if (!window.selectedTeeth.includes(i.toString())) {
                        window.selectedTeeth.push(i.toString());
                        $(`.tooth-${i}-parent`).css('fill', 'red');
                    }
                }
            });

            // Call the update functions from select_tooth.html
            if (typeof window.updateSelectedTeethInput === 'function') {
                window.updateSelectedTeethInput();
            }

            if (typeof window.updateSelectedTeethDisplay === 'function') {
                window.updateSelectedTeethDisplay();
            }
        }

        // Also update our own display
        updateTeethDisplay();
        showNotification('All lower teeth selected', 'info');
    });

    // Clear all teeth
    document.getElementById('clearAll').addEventListener('click', function() {
        // Check if the select_tooth.html script has initialized its own functions
        if (window.selectedTeeth !== undefined) {
            // Use the functions from select_tooth.html
            // Make a copy of the array to avoid modification during iteration
            const teethToDeselect = [...window.selectedTeeth];

            // Clear all teeth
            teethToDeselect.forEach(function(teethId) {
                $(`.tooth-${teethId}-parent`).css('fill', '#76f5f1');
            });

            // Clear the array
            window.selectedTeeth.length = 0;

            // Call the update functions from select_tooth.html
            if (typeof window.updateSelectedTeethInput === 'function') {
                window.updateSelectedTeethInput();
            }

            if (typeof window.updateSelectedTeethDisplay === 'function') {
                window.updateSelectedTeethDisplay();
            }
        }

        // Also clear any teeth selected with the class-based approach
        document.querySelectorAll('.tooth.selected').forEach(tooth => {
            tooth.classList.remove('selected');
        });

        // Clear the hidden input field directly
        const teethInput = document.getElementById('selected_teeth_input');
        if (teethInput) {
            teethInput.value = '';
        }

        // Update our own display
        updateTeethDisplay();
        showNotification('Teeth selection cleared', 'info');
    });
}

// Function to update the hidden input with selected teeth
function updateSelectedTeethInput() {
    // If the select_tooth.html script has initialized its own functions and data
    if (window.selectedTeeth !== undefined) {
        // Use the data from select_tooth.html
        document.getElementById('selected_teeth_input').value = window.selectedTeeth.join(',');
    } else {
        // Fallback to our own implementation
        const selectedTeeth = document.querySelectorAll('.tooth.selected');
        const teethNumbers = Array.from(selectedTeeth).map(tooth => {
            // Extract tooth number from class name (e.g., tooth-11 -> 11)
            const classNames = tooth.classList;
            for (const className of classNames) {
                if (className.startsWith('tooth-') && /^tooth-\d+$/.test(className)) {
                    return className.replace('tooth-', '');
                }
            }
            return null;
        }).filter(Boolean); // Remove any null values

        document.getElementById('selected_teeth_input').value = teethNumbers.join(',');
    }
}

// Patient Modal Functions
function openPatientModal() {
    const modal = new bootstrap.Modal(document.getElementById('patientModal'));
    modal.show();
}

function closePatientModal() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('patientModal'));
    if (modal) {
        modal.hide();
    }

    // Clear form fields
    document.getElementById('new_patient_first_name').value = '';
    document.getElementById('new_patient_last_name').value = '';
    document.getElementById('new_patient_phone').value = '';
    document.getElementById('new_patient_email').value = '';
}

function saveNewPatient() {
    const firstName = document.getElementById('new_patient_first_name').value.trim();
    const lastName = document.getElementById('new_patient_last_name').value.trim();
    const phone = document.getElementById('new_patient_phone').value.trim();
    const email = document.getElementById('new_patient_email').value.trim();

    if (!firstName || !lastName) {
        showNotification('First and last name are required', 'error');
        return;
    }

    // Create form data for AJAX request
    const formData = new FormData();
    formData.append('first_name', firstName);
    formData.append('last_name', lastName);
    formData.append('phone_number', phone);
    formData.append('email', email);

    // Get CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    // Show loading state
    const saveButton = document.querySelector('.modal-footer .btn-primary');
    const originalText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    saveButton.disabled = true;

    // Send AJAX request to create patient
    fetch('{% url "patients:patient_create_ajax" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': csrfToken
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add new patient to dropdown and select it
            const patientDropdown = document.getElementById('id_patient');
            const newOption = new Option(
                data.patient_name,
                data.patient_id,
                true,
                true
            );

            // Add to Select2
            $(patientDropdown).append(newOption).trigger('change');

            showNotification('New patient added successfully', 'success');
            closePatientModal();
        } else {
            // Show error message
            let errorMessage = 'Failed to create patient';
            if (data.errors) {
                errorMessage = Object.values(data.errors)
                    .map(err => err.join(', '))
                    .join('; ');
            }
            showNotification(errorMessage, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while creating the patient', 'error');
    })
    .finally(() => {
        // Reset button state
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;
    });
}

// Helper functions
function showNotification(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'error' : type}`;
    toast.id = toastId;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    toast.style.animation = 'fadeIn 0.3s ease forwards';

    // Toast content
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 5000
    });
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

function scrollToElement(element) {
    if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Highlight the element
        const originalBackgroundColor = element.style.backgroundColor;
        element.style.backgroundColor = 'rgba(67, 97, 238, 0.1)';
        element.style.transition = 'background-color 0.5s ease';

        setTimeout(() => {
            element.style.backgroundColor = originalBackgroundColor;
        }, 1500);
    }
}

function updateTeethDisplay() {
    const teethInput = document.getElementById('selected_teeth_input');
    const selectedTeethDisplay = document.getElementById('selectedTeethDisplay');

    if (teethInput && selectedTeethDisplay) {
        // If the select_tooth.html script has initialized its own functions and data
        if (window.selectedTeeth !== undefined) {
            // Use the data from select_tooth.html
            if (window.selectedTeeth.length > 0) {
                selectedTeethDisplay.innerHTML = window.selectedTeeth.map(num =>
                    `<span class="badge bg-primary me-1">${num.trim()}</span>`
                ).join(' ');
                selectedTeethDisplay.classList.remove('text-muted');
            } else {
                selectedTeethDisplay.innerHTML = '<span class="text-muted">None selected</span>';
            }
        } else {
            // Fallback to our own implementation
            const teethValue = teethInput.value || '';

            if (!teethValue) {
                selectedTeethDisplay.innerHTML = '<span class="text-muted">None selected</span>';
            } else {
                const teethNumbers = teethValue.split(',');
                selectedTeethDisplay.innerHTML = teethNumbers.map(num =>
                    `<span class="badge bg-primary me-1">${num.trim()}</span>`
                ).join(' ');
            }
        }
    }
}

// Formset Management
function setupRemoveButtons() {
    document.querySelectorAll('.btn-remove').forEach(button => {
        button.addEventListener('click', removeFormItem);
    });
}

function removeFormItem(e) {
    e.preventDefault();
    const formItemCol = e.target.closest('.col-md-6') || e.currentTarget.closest('.col-md-6');
    const formItem = e.target.closest('.formset-item') || e.currentTarget.closest('.formset-item');

    // Check if it's an existing item (has ID)
    const formId = formItem.querySelector('input[name$="-id"]')?.value;

    if (formId) {
        // Mark for deletion if it's an existing item
        const deleteCheckbox = formItem.querySelector('input[name$="-DELETE"]');
        if (deleteCheckbox) {
            deleteCheckbox.checked = true;
            formItem.classList.add('deleted');

            // Fade out the item
            formItemCol.style.opacity = '0';
            formItemCol.style.transform = 'scale(0.9)';
            formItemCol.style.transition = 'all 0.3s ease';

            setTimeout(() => {
                formItemCol.style.display = 'none';
                updateFormsetManagement();
            }, 300);
        }
    } else {
        // Remove completely if it's a new item
        formItemCol.style.opacity = '0';
        formItemCol.style.transform = 'scale(0.9)';
        formItemCol.style.transition = 'all 0.3s ease';

        setTimeout(() => {
            formItemCol.remove();
            updateFormsetManagement();
        }, 300);
    }

    showNotification('Item removed', 'info');
}

function updateFormsetManagement() {
    // Count visible items (not marked for deletion)
    const visibleItems = document.querySelectorAll('#formset-container .formset-item:not(.deleted)');
    const totalFormsInput = document.getElementById('id_case_items-TOTAL_FORMS');

    if (totalFormsInput) {
        totalFormsInput.value = visibleItems.length.toString();
    }

    // Check if we need to show the empty state
    const formsetContainer = document.getElementById('formset-container');
    if (visibleItems.length === 0 && formsetContainer) {
        // Add empty state message if not already present
        if (!document.querySelector('#formset-container .empty-state')) {
            const emptyState = document.createElement('div');
            emptyState.className = 'col-12 empty-state animate-fade-in';
            emptyState.innerHTML = `
                <div class="empty-state-icon mb-3">
                    <i class="fas fa-clipboard-list fa-3x"></i>
                </div>
                <p>No items added yet. Click "Add Item" to add treatment items.</p>
            `;
            formsetContainer.appendChild(emptyState);
        }
    } else {
        // Remove empty state if items exist
        const emptyState = document.querySelector('#formset-container .empty-state');
        if (emptyState) {
            emptyState.remove();
        }
    }
}

// Prevent form submission on Enter key
function preventFormSubmitOnEnter() {
    document.querySelector('#case-create-form').addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.target.tagName !== 'TEXTAREA' &&
           !e.target.closest('.modal')) {
            e.preventDefault();
            return false;
        }
    });
}

// Add new item to formset
const addItemButton = document.getElementById('add-item');
if (addItemButton) {
    addItemButton.addEventListener('click', function(e) {
        e.preventDefault();

        // Remove empty state if present
        const emptyState = document.querySelector('#formset-container .empty-state');
        if (emptyState) {
            emptyState.remove();
        }

        // Get current form count
        const formCount = document.querySelectorAll('#formset-container .formset-item').length;
        const formIdx = formCount;

        // Create column for the new item
        const newCol = document.createElement('div');
        newCol.className = 'col-md-6 mb-3';
        newCol.style.opacity = '0';
        newCol.style.transform = 'translateY(20px)';

        // Create the formset item container
        const newFormItem = document.createElement('div');
        newFormItem.className = 'formset-item animate-bounce-in';

        // Get template from the first item or create basic structure
        let templateHtml = '';
        const firstItem = document.querySelector('#formset-container .formset-item');

        if (firstItem) {
            // Clone from existing item
            templateHtml = firstItem.innerHTML;
            // Update indices
            templateHtml = templateHtml.replace(/case_items-\d+/g, `case_items-${formIdx}`);
            // Clear any values
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = templateHtml;

            // Reset select values
            tempDiv.querySelectorAll('select').forEach(select => {
                select.selectedIndex = 0;
            });

            // Reset text inputs
            tempDiv.querySelectorAll('input[type="text"], input[type="number"]').forEach(input => {
                if (input.name.includes('quantity')) {
                    input.value = '1'; // Default quantity to 1
                } else {
                    input.value = '';
                }
            });

            templateHtml = tempDiv.innerHTML;
        } else {
            // Basic template if no existing item
            templateHtml = `
                <div class="mb-3">
                    <label for="id_case_items-${formIdx}-item" class="form-label required">Item</label>
                    <select name="case_items-${formIdx}-item" id="id_case_items-${formIdx}-item" class="form-select" required>
                        <option value="">Select an item</option>
                        <!-- Options will be populated from server -->
                    </select>
                </div>
                <div class="mb-3">
                    <label for="id_case_items-${formIdx}-quantity" class="form-label required">Quantity</label>
                    <input type="number" name="case_items-${formIdx}-quantity" id="id_case_items-${formIdx}-quantity" class="form-control" value="1" min="1" required>
                </div>
                <div class="mb-3">
                    <label for="id_case_items-${formIdx}-unit" class="form-label required">Unit</label>
                    <select name="case_items-${formIdx}-unit" id="id_case_items-${formIdx}-unit" class="form-select" required>
                        <option value="">Select unit</option>
                        <!-- Options will be populated from server -->
                    </select>
                </div>
                <input type="hidden" name="case_items-${formIdx}-id" id="id_case_items-${formIdx}-id">
            `;
        }

        // Set the HTML
        newFormItem.innerHTML = templateHtml;

        // Add remove button
        const removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'btn-remove btn btn-sm btn-danger';
        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
        removeBtn.addEventListener('click', removeFormItem);
        newFormItem.appendChild(removeBtn);

        // Add the form item to the column
        newCol.appendChild(newFormItem);

        // Add to container
        document.getElementById('formset-container').appendChild(newCol);

        // Animate in
        setTimeout(() => {
            newCol.style.opacity = '1';
            newCol.style.transform = 'translateY(0)';
            newCol.style.transition = 'all 0.3s ease';
        }, 10);

        // Update management form
        const totalFormsInput = document.getElementById('id_case_items-TOTAL_FORMS');
        if (totalFormsInput) {
            totalFormsInput.value = (formCount + 1).toString();
        }

        // Initialize any Select2 elements in the new form
        $(newFormItem).find('select').select2({
            theme: 'bootstrap-5',
            width: '100%',
            dropdownParent: newFormItem
        });

        showNotification('New item added', 'success');
    });
}
</script>
{% endblock %}