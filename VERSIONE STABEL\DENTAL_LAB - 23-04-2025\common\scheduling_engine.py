"""
Advanced Scheduling Engine with Resource Conflict Resolution
"""

from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging
from django.utils import timezone
from django.db import transaction
from django.db.models import Q, Sum, Count, Avg
from decimal import Decimal

from .exceptions import SchedulingError, ResourceNotFoundError, WorkflowError
from .workflow_automation import workflow_automation, TransitionContext

logger = logging.getLogger(__name__)


@dataclass
class SchedulingConstraint:
    """Represents a scheduling constraint"""
    constraint_type: str  # 'resource', 'time', 'dependency', 'capacity'
    entity_id: int
    start_time: datetime
    end_time: datetime
    priority: int = 1
    metadata: Dict[str, Any] = None


@dataclass
class ResourceAllocation:
    """Represents a resource allocation"""
    resource_id: int
    resource_type: str  # 'user', 'equipment', 'department'
    start_time: datetime
    end_time: datetime
    utilization: float
    efficiency_score: float
    conflicts: List[str] = None


@dataclass
class SchedulingResult:
    """Result of scheduling operation"""
    success: bool
    schedule_id: Optional[int] = None
    allocated_resources: List[ResourceAllocation] = None
    conflicts: List[str] = None
    optimization_score: float = 0.0
    total_duration: timedelta = None
    message: str = ""


class AdvancedSchedulingEngine:
    """
    Advanced scheduling engine with conflict resolution and optimization
    """

    def __init__(self):
        self.constraints: List[SchedulingConstraint] = []
        self.optimization_weights = {
            'resource_utilization': 0.3,
            'timeline_efficiency': 0.25,
            'workload_balance': 0.2,
            'priority_adherence': 0.15,
            'setup_time_minimization': 0.1
        }

    def schedule_case(self, case, optimization_strategy='balanced') -> SchedulingResult:
        """
        Create an optimized schedule for a case

        Args:
            case: The Case instance to schedule
            optimization_strategy: 'speed', 'quality', 'balanced', 'resource_efficient'

        Returns:
            SchedulingResult with allocation details
        """
        try:
            logger.info(f"Starting advanced scheduling for case #{case.case_number}")

            # Analyze case requirements
            requirements = self._analyze_case_requirements(case)

            # Generate scheduling constraints
            constraints = self._generate_constraints(case, requirements)

            # Find optimal resource allocation
            allocation = self._optimize_resource_allocation(
                case, requirements, constraints, optimization_strategy
            )

            # Create the schedule
            schedule = self._create_optimized_schedule(case, allocation)

            # Validate and resolve conflicts
            conflicts = self._validate_and_resolve_conflicts(schedule, allocation)

            # Calculate optimization score
            score = self._calculate_optimization_score(schedule, allocation)

            result = SchedulingResult(
                success=True,
                schedule_id=schedule.id,
                allocated_resources=allocation,
                conflicts=conflicts,
                optimization_score=score,
                total_duration=schedule.end_date - schedule.start_date,
                message=f"Schedule created with {score:.1f}% optimization score"
            )

            logger.info(f"Case #{case.case_number} scheduled successfully with score {score:.1f}%")
            return result

        except Exception as e:
            logger.error(f"Scheduling failed for case #{case.case_number}: {e}", exc_info=True)
            return SchedulingResult(
                success=False,
                message=f"Scheduling failed: {str(e)}"
            )

    def _analyze_case_requirements(self, case) -> Dict[str, Any]:
        """Analyze case requirements for scheduling"""
        requirements = {
            'workflow_stages': [],
            'total_estimated_duration': timedelta(0),
            'required_departments': set(),
            'required_skills': set(),
            'priority_level': getattr(case, 'priority', 2),
            'deadline': getattr(case, 'deadline', None),
            'complexity_score': 1.0,
            'material_requirements': [],
            'material_availability': True,
            'material_shortage_cost': 0.0
        }

        # Check material availability using MRP service
        try:
            from items.services import MaterialRequirementPlanningService
            mrp_service = MaterialRequirementPlanningService()
            production_plan = mrp_service.calculate_case_material_requirements(case)

            requirements['material_requirements'] = production_plan.material_requirements
            requirements['material_availability'] = production_plan.feasible
            requirements['material_shortage_cost'] = production_plan.total_shortage_cost

            # If materials are not available, adjust scheduling
            if not production_plan.feasible:
                logger.warning(f"Case #{case.case_number} has material shortages - adjusting schedule")

        except Exception as e:
            logger.error(f"Error checking material availability for case #{case.case_number}: {str(e)}")
            # Continue with scheduling even if material check fails

        # Analyze workflow stages
        if hasattr(case, 'workflow_template') and case.workflow_template:
            stages = case.workflow_template.stages.all().order_by('order')

            for stage in stages:
                stage_req = {
                    'stage': stage,
                    'department': stage.department,
                    'estimated_duration': stage.estimated_duration,
                    'required_skills': getattr(stage, 'required_skills', []),
                    'setup_time': getattr(stage, 'setup_time', timedelta(minutes=15)),
                    'cleanup_time': getattr(stage, 'cleanup_time', timedelta(minutes=10))
                }

                requirements['workflow_stages'].append(stage_req)
                requirements['total_estimated_duration'] += stage.estimated_duration
                requirements['required_departments'].add(stage.department)

                # Add stage-specific skills
                if hasattr(stage, 'required_skills'):
                    requirements['required_skills'].update(stage.required_skills)

        # Calculate complexity score based on various factors
        complexity_factors = [
            len(requirements['workflow_stages']) / 10,  # Number of stages
            len(requirements['required_departments']) / 5,  # Department diversity
            requirements['total_estimated_duration'].total_seconds() / (8 * 3600),  # Duration in work days
            requirements['priority_level'] / 3  # Priority impact
        ]

        requirements['complexity_score'] = min(sum(complexity_factors) / len(complexity_factors), 3.0)

        return requirements

    def _generate_constraints(self, case, requirements) -> List[SchedulingConstraint]:
        """Generate scheduling constraints"""
        constraints = []

        # Material availability constraints
        if not requirements['material_availability']:
            case_id = getattr(case, 'id', getattr(case, 'pk', 0))

            # Calculate earliest start time based on material delivery
            earliest_start = self._calculate_earliest_material_availability(requirements['material_requirements'])

            constraints.append(SchedulingConstraint(
                constraint_type='material',
                entity_id=case_id,
                start_time=earliest_start,
                end_time=earliest_start + requirements['total_estimated_duration'],
                priority=3,  # High priority - cannot start without materials
                metadata={
                    'type': 'material_availability',
                    'case_id': case_id,
                    'shortage_cost': requirements['material_shortage_cost']
                }
            ))

        # Time constraints
        if requirements['deadline']:
            case_id = getattr(case, 'id', getattr(case, 'pk', 0))
            constraints.append(SchedulingConstraint(
                constraint_type='time',
                entity_id=case_id,
                start_time=timezone.now(),
                end_time=requirements['deadline'],
                priority=3,
                metadata={'type': 'deadline', 'case_id': case_id}
            ))

        # Resource constraints for each department
        for dept in requirements['required_departments']:
            # Check department capacity
            constraints.append(SchedulingConstraint(
                constraint_type='capacity',
                entity_id=dept.id,
                start_time=timezone.now(),
                end_time=timezone.now() + timedelta(days=30),  # Planning horizon
                priority=2,
                metadata={'type': 'department_capacity', 'department_id': dept.id}
            ))

        # Dependency constraints
        for i, stage_req in enumerate(requirements['workflow_stages']):
            if i > 0:  # Not the first stage
                constraints.append(SchedulingConstraint(
                    constraint_type='dependency',
                    entity_id=stage_req['stage'].id,
                    start_time=timezone.now(),
                    end_time=timezone.now() + requirements['total_estimated_duration'],
                    priority=3,
                    metadata={
                        'type': 'stage_dependency',
                        'depends_on': requirements['workflow_stages'][i-1]['stage'].id
                    }
                ))

        return constraints

    def _calculate_earliest_material_availability(self, material_requirements) -> datetime:
        """Calculate the earliest time when all materials will be available"""
        try:
            earliest_availability = timezone.now()

            for requirement in material_requirements:
                if not requirement.is_sufficient and requirement.supplier_options:
                    # Get the fastest delivery option
                    fastest_option = min(
                        requirement.supplier_options,
                        key=lambda x: x.get('delivery_time_days', 30)
                    )

                    delivery_date = timezone.now() + timedelta(
                        days=fastest_option.get('delivery_time_days', 7) + 1  # +1 day buffer
                    )

                    if delivery_date > earliest_availability:
                        earliest_availability = delivery_date

            return earliest_availability

        except Exception as e:
            logger.error(f"Error calculating material availability: {str(e)}")
            # Default to 7 days if calculation fails
            return timezone.now() + timedelta(days=7)

    def _optimize_resource_allocation(self, case, requirements, constraints, strategy) -> List[ResourceAllocation]:
        """Optimize resource allocation using advanced algorithms"""
        allocations = []
        current_time = timezone.now()

        # Adjust optimization weights based on strategy
        weights = self._get_strategy_weights(strategy)

        for stage_req in requirements['workflow_stages']:
            stage = stage_req['stage']
            department = stage_req['department']
            duration = stage_req['estimated_duration']

            # Find optimal time slot
            optimal_slot = self._find_optimal_time_slot(
                department, current_time, duration, weights, constraints
            )

            # Find best resource allocation
            resource_allocation = self._allocate_optimal_resources(
                department, optimal_slot['start'], optimal_slot['end'],
                stage_req, weights
            )

            allocations.append(resource_allocation)
            current_time = optimal_slot['end']

        return allocations

    def _get_strategy_weights(self, strategy) -> Dict[str, float]:
        """Get optimization weights based on strategy"""
        if strategy == 'speed':
            return {
                'resource_utilization': 0.2,
                'timeline_efficiency': 0.4,
                'workload_balance': 0.1,
                'priority_adherence': 0.2,
                'setup_time_minimization': 0.1
            }
        elif strategy == 'quality':
            return {
                'resource_utilization': 0.4,
                'timeline_efficiency': 0.1,
                'workload_balance': 0.3,
                'priority_adherence': 0.1,
                'setup_time_minimization': 0.1
            }
        elif strategy == 'resource_efficient':
            return {
                'resource_utilization': 0.5,
                'timeline_efficiency': 0.1,
                'workload_balance': 0.3,
                'priority_adherence': 0.05,
                'setup_time_minimization': 0.05
            }
        else:  # balanced
            return self.optimization_weights

    def _find_optimal_time_slot(self, department, start_time, duration, weights, constraints) -> Dict[str, datetime]:
        """Find optimal time slot using constraint satisfaction"""
        best_slot = None
        best_score = -1

        # Search window: next 14 days
        search_end = start_time + timedelta(days=14)
        current_search = start_time

        while current_search < search_end:
            slot_end = current_search + duration

            # Check if slot is valid
            if self._is_time_slot_valid(department, current_search, slot_end, constraints):
                # Calculate slot score
                score = self._calculate_slot_score(
                    department, current_search, slot_end, weights
                )

                if score > best_score:
                    best_score = score
                    best_slot = {
                        'start': current_search,
                        'end': slot_end,
                        'score': score
                    }

            # Move to next hour
            current_search += timedelta(hours=1)

        if not best_slot:
            # Fallback: use next available slot
            best_slot = {
                'start': start_time,
                'end': start_time + duration,
                'score': 0.0
            }

        return best_slot

    def _is_time_slot_valid(self, department, start_time, end_time, constraints) -> bool:
        """Check if time slot is valid given constraints"""
        # Check working hours
        if not self._is_within_working_hours(start_time, end_time):
            return False

        # Check department availability
        if not self._is_department_available(department, start_time, end_time):
            return False

        # Check constraint violations
        for constraint in constraints:
            if constraint.constraint_type == 'time':
                if end_time > constraint.end_time:
                    return False
            elif constraint.constraint_type == 'material':
                if start_time < constraint.start_time:
                    return False  # Cannot start before materials are available

        return True

    def _is_within_working_hours(self, start_time, end_time) -> bool:
        """Check if time slot is within working hours"""
        # Standard working hours: 8 AM to 6 PM, Monday to Friday
        if start_time.weekday() >= 5 or end_time.weekday() >= 5:  # Weekend
            return False

        if start_time.hour < 8 or end_time.hour > 18:
            return False

        return True

    def _is_department_available(self, department, start_time, end_time) -> bool:
        """Check department availability"""
        try:
            from scheduling.models import ScheduleItem

            # Check for overlapping schedule items
            overlapping = ScheduleItem.objects.filter(
                department=department,
                start_time__lt=end_time,
                end_time__gt=start_time,
                status__in=['pending', 'in_progress']
            )

            return not overlapping.exists()

        except ImportError:
            # Fallback if scheduling models not available
            return True

    def _calculate_slot_score(self, department, start_time, end_time, weights) -> float:
        """Calculate score for a time slot"""
        score = 0.0

        # Timeline efficiency (prefer earlier slots)
        hours_from_now = (start_time - timezone.now()).total_seconds() / 3600
        timeline_score = max(0, 1 - (hours_from_now / (24 * 7)))  # Decay over a week
        score += weights['timeline_efficiency'] * timeline_score

        # Workload balance (prefer less busy times)
        workload_score = 1 - self._get_department_utilization(department, start_time.date())
        score += weights['workload_balance'] * workload_score

        # Setup time minimization (prefer consecutive slots)
        setup_score = self._calculate_setup_efficiency(department, start_time)
        score += weights['setup_time_minimization'] * setup_score

        return score

    def _get_department_utilization(self, department, date) -> float:
        """Get department utilization for a specific date"""
        try:
            from scheduling.utils import calculate_workload
            return calculate_workload(department, date) / 100.0
        except:
            return 0.5  # Default moderate utilization

    def _calculate_setup_efficiency(self, department, start_time) -> float:
        """Calculate setup efficiency score"""
        try:
            from scheduling.models import ScheduleItem

            # Check for adjacent schedule items
            adjacent_before = ScheduleItem.objects.filter(
                department=department,
                end_time=start_time
            ).exists()

            adjacent_after = ScheduleItem.objects.filter(
                department=department,
                start_time=start_time
            ).exists()

            if adjacent_before or adjacent_after:
                return 1.0  # High efficiency - minimal setup
            else:
                return 0.5  # Moderate efficiency - some setup required

        except ImportError:
            return 0.5

    def _allocate_optimal_resources(self, department, start_time, end_time, stage_req, weights) -> ResourceAllocation:
        """Allocate optimal resources for a time slot"""
        try:
            from scheduling.utils import find_available_worker

            # Find best available worker
            worker = find_available_worker(department, start_time, end_time - start_time)

            if worker:
                # Calculate efficiency score based on worker skills and workload
                efficiency_score = self._calculate_worker_efficiency(
                    worker, stage_req, start_time
                )

                return ResourceAllocation(
                    resource_id=worker.id,
                    resource_type='user',
                    start_time=start_time,
                    end_time=end_time,
                    utilization=0.8,  # Assume 80% utilization
                    efficiency_score=efficiency_score,
                    conflicts=[]
                )
            else:
                # No worker available - create conflict
                return ResourceAllocation(
                    resource_id=0,
                    resource_type='user',
                    start_time=start_time,
                    end_time=end_time,
                    utilization=0.0,
                    efficiency_score=0.0,
                    conflicts=['No available worker']
                )

        except ImportError:
            # Fallback allocation
            return ResourceAllocation(
                resource_id=1,
                resource_type='user',
                start_time=start_time,
                end_time=end_time,
                utilization=0.8,
                efficiency_score=0.7,
                conflicts=[]
            )

    def _calculate_worker_efficiency(self, worker, stage_req, start_time) -> float:
        """Calculate worker efficiency score"""
        score = 0.7  # Base score

        # Check worker skills match
        if hasattr(worker, 'skills'):
            required_skills = stage_req.get('required_skills', [])
            worker_skills = getattr(worker, 'skills', [])

            if required_skills:
                skill_match = len(set(required_skills) & set(worker_skills)) / len(required_skills)
                score += 0.2 * skill_match

        # Check worker workload
        try:
            from scheduling.models import ScheduleItem

            daily_workload = ScheduleItem.objects.filter(
                assigned_to=worker,
                start_time__date=start_time.date()
            ).count()

            # Prefer workers with moderate workload
            if daily_workload <= 3:
                score += 0.1
            elif daily_workload > 6:
                score -= 0.1

        except ImportError:
            pass

        return min(score, 1.0)

    def _create_optimized_schedule(self, case, allocations) -> Any:
        """Create the optimized schedule"""
        try:
            from scheduling.models import Schedule, ScheduleItem

            # Calculate schedule bounds
            start_time = min(alloc.start_time for alloc in allocations)
            end_time = max(alloc.end_time for alloc in allocations)

            # Create schedule
            schedule = Schedule.objects.create(
                case=case,
                start_date=start_time,
                end_date=end_time,
                status='pending',
                workflow_template=case.workflow_template,
                priority=getattr(case, 'priority', 2),
                notes=f"Auto-generated optimized schedule"
            )

            # Create schedule items
            for i, allocation in enumerate(allocations):
                if allocation.resource_id > 0:  # Valid resource
                    from django.contrib.auth import get_user_model
                    User = get_user_model()

                    try:
                        assigned_user = User.objects.get(id=allocation.resource_id)
                    except User.DoesNotExist:
                        assigned_user = None

                    ScheduleItem.objects.create(
                        schedule=schedule,
                        start_time=allocation.start_time,
                        end_time=allocation.end_time,
                        task=f"Stage {i+1}",
                        status='pending',
                        assigned_to=assigned_user
                    )

            return schedule

        except ImportError:
            # Return mock schedule for testing
            class MockSchedule:
                def __init__(self):
                    self.id = 1
                    self.start_date = min(alloc.start_time for alloc in allocations)
                    self.end_date = max(alloc.end_time for alloc in allocations)

            return MockSchedule()

    def _validate_and_resolve_conflicts(self, schedule, allocations) -> List[str]:
        """Validate schedule and resolve conflicts"""
        conflicts = []

        for allocation in allocations:
            if allocation.conflicts:
                conflicts.extend(allocation.conflicts)

        # Check for resource over-allocation
        resource_usage = {}
        for allocation in allocations:
            if allocation.resource_id in resource_usage:
                # Check for time overlap
                existing = resource_usage[allocation.resource_id]
                if (allocation.start_time < existing['end_time'] and
                    allocation.end_time > existing['start_time']):
                    conflicts.append(f"Resource {allocation.resource_id} over-allocated")
            else:
                resource_usage[allocation.resource_id] = {
                    'start_time': allocation.start_time,
                    'end_time': allocation.end_time
                }

        return conflicts

    def _calculate_optimization_score(self, schedule, allocations) -> float:
        """Calculate overall optimization score"""
        if not allocations:
            return 0.0

        # Resource utilization score
        avg_utilization = sum(alloc.utilization for alloc in allocations) / len(allocations)

        # Efficiency score
        avg_efficiency = sum(alloc.efficiency_score for alloc in allocations) / len(allocations)

        # Conflict penalty
        total_conflicts = sum(len(alloc.conflicts or []) for alloc in allocations)
        conflict_penalty = min(total_conflicts * 0.1, 0.5)

        # Timeline efficiency (prefer shorter schedules)
        if hasattr(schedule, 'start_date') and hasattr(schedule, 'end_date'):
            duration_hours = (schedule.end_date - schedule.start_date).total_seconds() / 3600
            timeline_score = max(0, 1 - (duration_hours / (7 * 24)))  # Normalize to week
        else:
            timeline_score = 0.5

        # Combine scores
        final_score = (
            avg_utilization * 0.3 +
            avg_efficiency * 0.3 +
            timeline_score * 0.4 -
            conflict_penalty
        ) * 100

        return max(0, min(100, final_score))


# Global instance
scheduling_engine = AdvancedSchedulingEngine()


class CapacityPlanningService:
    """
    Service for capacity planning and workload balancing
    """

    @staticmethod
    def analyze_department_capacity(department, start_date, end_date) -> Dict[str, Any]:
        """
        Analyze department capacity and utilization

        Returns:
            Dictionary with capacity analysis
        """
        try:
            from scheduling.models import ScheduleItem
            from django.contrib.auth import get_user_model
            User = get_user_model()

            # Get scheduled items for the period
            items = ScheduleItem.objects.filter(
                department=department,
                start_time__date__gte=start_date,
                start_time__date__lte=end_date
            )

            # Calculate total scheduled hours
            total_scheduled_hours = sum(
                (item.end_time - item.start_time).total_seconds() / 3600
                for item in items
            )

            # Calculate available capacity
            working_days = (end_date - start_date).days + 1
            weekdays = sum(1 for i in range(working_days)
                          if (start_date + timedelta(days=i)).weekday() < 5)

            department_capacity = getattr(department, 'capacity', 1)
            hours_per_day = 8  # Standard working hours
            total_available_hours = weekdays * hours_per_day * department_capacity

            # Calculate utilization
            utilization = (total_scheduled_hours / total_available_hours * 100) if total_available_hours > 0 else 0

            # Get department staff
            staff_count = User.objects.filter(departments=department, is_active=True).count()

            # Analyze workload distribution
            daily_distribution = {}
            current_date = start_date

            while current_date <= end_date:
                if current_date.weekday() < 5:  # Weekday
                    day_items = items.filter(start_time__date=current_date)
                    day_hours = sum(
                        (item.end_time - item.start_time).total_seconds() / 3600
                        for item in day_items
                    )

                    daily_capacity = department_capacity * hours_per_day
                    day_utilization = (day_hours / daily_capacity * 100) if daily_capacity > 0 else 0

                    daily_distribution[current_date.isoformat()] = {
                        'scheduled_hours': round(day_hours, 2),
                        'capacity_hours': daily_capacity,
                        'utilization': round(day_utilization, 2),
                        'task_count': day_items.count(),
                        'overloaded': day_utilization > 100
                    }

                current_date += timedelta(days=1)

            # Identify bottlenecks
            bottlenecks = []
            overloaded_days = [
                date for date, data in daily_distribution.items()
                if data['overloaded']
            ]

            if overloaded_days:
                bottlenecks.append({
                    'type': 'overload',
                    'description': f"{len(overloaded_days)} days over capacity",
                    'affected_dates': overloaded_days[:5]  # Show first 5
                })

            if utilization > 90:
                bottlenecks.append({
                    'type': 'high_utilization',
                    'description': f"Overall utilization at {utilization:.1f}%",
                    'recommendation': 'Consider additional resources'
                })

            return {
                'department_id': department.id,
                'department_name': department.name,
                'analysis_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'working_days': weekdays
                },
                'capacity_metrics': {
                    'total_available_hours': total_available_hours,
                    'total_scheduled_hours': round(total_scheduled_hours, 2),
                    'utilization_percentage': round(utilization, 2),
                    'staff_count': staff_count,
                    'capacity_rating': department_capacity
                },
                'daily_distribution': daily_distribution,
                'bottlenecks': bottlenecks,
                'recommendations': CapacityPlanningService._generate_capacity_recommendations(
                    utilization, bottlenecks, staff_count
                )
            }

        except Exception as e:
            logger.error(f"Error analyzing department capacity: {e}", exc_info=True)
            return {
                'error': str(e),
                'department_id': department.id if department else None
            }

    @staticmethod
    def _generate_capacity_recommendations(utilization, bottlenecks, staff_count) -> List[str]:
        """Generate capacity planning recommendations"""
        recommendations = []

        if utilization > 95:
            recommendations.append("Critical: Department severely overloaded. Immediate action required.")
            recommendations.append("Consider hiring additional staff or redistributing workload.")
        elif utilization > 85:
            recommendations.append("Warning: High utilization detected. Monitor closely.")
            recommendations.append("Plan for additional capacity during peak periods.")
        elif utilization < 50:
            recommendations.append("Low utilization detected. Consider optimizing resource allocation.")
            recommendations.append("Evaluate if resources can be reallocated to other departments.")

        if any(b['type'] == 'overload' for b in bottlenecks):
            recommendations.append("Reschedule non-critical tasks from overloaded days.")
            recommendations.append("Consider overtime or temporary staff for peak periods.")

        if staff_count < 2:
            recommendations.append("Single-person department detected. Consider cross-training.")
            recommendations.append("Plan for backup coverage during absences.")

        return recommendations

    @staticmethod
    def balance_workload_across_departments(start_date, end_date) -> Dict[str, Any]:
        """
        Analyze and balance workload across all departments
        """
        try:
            from case.models import Department

            departments = Department.objects.filter(is_active=True)
            department_analyses = []

            total_utilization = 0
            overloaded_departments = []
            underutilized_departments = []

            for department in departments:
                analysis = CapacityPlanningService.analyze_department_capacity(
                    department, start_date, end_date
                )

                if 'capacity_metrics' in analysis:
                    utilization = analysis['capacity_metrics']['utilization_percentage']
                    total_utilization += utilization

                    if utilization > 85:
                        overloaded_departments.append({
                            'department': department.name,
                            'utilization': utilization,
                            'analysis': analysis
                        })
                    elif utilization < 50:
                        underutilized_departments.append({
                            'department': department.name,
                            'utilization': utilization,
                            'analysis': analysis
                        })

                department_analyses.append(analysis)

            avg_utilization = total_utilization / len(departments) if departments else 0

            # Generate balancing recommendations
            balancing_recommendations = []

            if overloaded_departments and underutilized_departments:
                balancing_recommendations.append(
                    "Workload imbalance detected. Consider redistributing tasks."
                )

                for overloaded in overloaded_departments[:2]:  # Top 2 overloaded
                    for underutilized in underutilized_departments[:2]:  # Top 2 underutilized
                        balancing_recommendations.append(
                            f"Move tasks from {overloaded['department']} "
                            f"({overloaded['utilization']:.1f}%) to "
                            f"{underutilized['department']} "
                            f"({underutilized['utilization']:.1f}%)"
                        )

            return {
                'analysis_period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'overall_metrics': {
                    'average_utilization': round(avg_utilization, 2),
                    'total_departments': len(departments),
                    'overloaded_count': len(overloaded_departments),
                    'underutilized_count': len(underutilized_departments)
                },
                'department_analyses': department_analyses,
                'overloaded_departments': overloaded_departments,
                'underutilized_departments': underutilized_departments,
                'balancing_recommendations': balancing_recommendations,
                'system_health': CapacityPlanningService._assess_system_health(
                    avg_utilization, overloaded_departments, underutilized_departments
                )
            }

        except Exception as e:
            logger.error(f"Error balancing workload: {e}", exc_info=True)
            return {'error': str(e)}

    @staticmethod
    def _assess_system_health(avg_utilization, overloaded_depts, underutilized_depts) -> Dict[str, Any]:
        """Assess overall system health"""
        if avg_utilization > 90:
            health_status = 'critical'
            health_message = 'System severely overloaded'
        elif avg_utilization > 80:
            health_status = 'warning'
            health_message = 'System under high load'
        elif avg_utilization < 40:
            health_status = 'underutilized'
            health_message = 'System underutilized'
        else:
            health_status = 'healthy'
            health_message = 'System operating normally'

        # Calculate balance score
        imbalance_penalty = (len(overloaded_depts) + len(underutilized_depts)) * 10
        balance_score = max(0, 100 - imbalance_penalty)

        return {
            'status': health_status,
            'message': health_message,
            'average_utilization': round(avg_utilization, 2),
            'balance_score': balance_score,
            'needs_attention': health_status in ['critical', 'warning'] or balance_score < 70
        }

    @staticmethod
    def predict_future_capacity_needs(department, days_ahead=30) -> Dict[str, Any]:
        """
        Predict future capacity needs based on trends
        """
        try:
            from scheduling.models import ScheduleItem
            from case.models import Case

            # Analyze historical data (last 30 days)
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=30)

            historical_items = ScheduleItem.objects.filter(
                department=department,
                start_time__date__gte=start_date,
                start_time__date__lte=end_date
            )

            # Calculate daily averages
            daily_hours = {}
            current_date = start_date

            while current_date <= end_date:
                if current_date.weekday() < 5:  # Weekday
                    day_items = historical_items.filter(start_time__date=current_date)
                    day_hours = sum(
                        (item.end_time - item.start_time).total_seconds() / 3600
                        for item in day_items
                    )
                    daily_hours[current_date] = day_hours

                current_date += timedelta(days=1)

            # Calculate trend
            if len(daily_hours) > 7:
                recent_avg = sum(list(daily_hours.values())[-7:]) / 7
                older_avg = sum(list(daily_hours.values())[:-7]) / (len(daily_hours) - 7)
                trend = (recent_avg - older_avg) / older_avg * 100 if older_avg > 0 else 0
            else:
                trend = 0
                recent_avg = sum(daily_hours.values()) / len(daily_hours) if daily_hours else 0

            # Predict future needs
            future_start = end_date + timedelta(days=1)
            future_end = future_start + timedelta(days=days_ahead)

            # Check for scheduled cases that might affect capacity
            future_cases = Case.objects.filter(
                deadline__gte=future_start,
                deadline__lte=future_end,
                status__in=['pending_acceptance', 'in_progress']
            )

            predicted_daily_hours = recent_avg * (1 + trend / 100)
            predicted_total_hours = predicted_daily_hours * days_ahead * 5 / 7  # Weekdays only

            # Calculate capacity requirements
            department_capacity = getattr(department, 'capacity', 1)
            available_hours = days_ahead * 5 * 8 * department_capacity  # 5 weekdays, 8 hours, capacity

            predicted_utilization = (predicted_total_hours / available_hours * 100) if available_hours > 0 else 0

            return {
                'department_id': department.id,
                'department_name': department.name,
                'prediction_period': {
                    'start_date': future_start.isoformat(),
                    'end_date': future_end.isoformat(),
                    'days_ahead': days_ahead
                },
                'historical_analysis': {
                    'analysis_period_days': len(daily_hours),
                    'average_daily_hours': round(recent_avg, 2),
                    'trend_percentage': round(trend, 2),
                    'trend_direction': 'increasing' if trend > 5 else 'decreasing' if trend < -5 else 'stable'
                },
                'predictions': {
                    'predicted_daily_hours': round(predicted_daily_hours, 2),
                    'predicted_total_hours': round(predicted_total_hours, 2),
                    'predicted_utilization': round(predicted_utilization, 2),
                    'available_capacity_hours': available_hours
                },
                'upcoming_cases': future_cases.count(),
                'recommendations': CapacityPlanningService._generate_future_recommendations(
                    predicted_utilization, trend, future_cases.count()
                )
            }

        except Exception as e:
            logger.error(f"Error predicting capacity needs: {e}", exc_info=True)
            return {'error': str(e)}

    @staticmethod
    def _generate_future_recommendations(predicted_utilization, trend, upcoming_cases) -> List[str]:
        """Generate recommendations for future capacity planning"""
        recommendations = []

        if predicted_utilization > 90:
            recommendations.append("High utilization predicted. Plan for additional resources.")
            recommendations.append("Consider scheduling overtime or temporary staff.")

        if trend > 10:
            recommendations.append("Strong upward trend detected. Monitor capacity closely.")
            recommendations.append("Consider long-term capacity expansion.")
        elif trend < -10:
            recommendations.append("Declining trend detected. Evaluate resource optimization.")

        if upcoming_cases > 10:
            recommendations.append(f"{upcoming_cases} cases scheduled. Ensure adequate staffing.")

        if predicted_utilization < 40:
            recommendations.append("Low utilization predicted. Consider resource reallocation.")

        return recommendations


# Global instance
capacity_planner = CapacityPlanningService()
