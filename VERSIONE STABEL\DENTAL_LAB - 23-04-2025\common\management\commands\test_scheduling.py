"""
Management command to test the advanced scheduling engine
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import date, timedelta
import json

User = get_user_model()


class Command(BaseCommand):
    help = 'Test the advanced scheduling engine and capacity planning'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-type',
            type=str,
            choices=['scheduling', 'capacity', 'resources', 'conflicts', 'all'],
            default='all',
            help='Type of scheduling test to run'
        )
        parser.add_argument(
            '--case-id',
            type=int,
            help='Specific case ID to test scheduling with'
        )
        parser.add_argument(
            '--department-id',
            type=int,
            help='Specific department ID to test with'
        )
        parser.add_argument(
            '--optimization-strategy',
            type=str,
            choices=['speed', 'quality', 'balanced', 'resource_efficient'],
            default='balanced',
            help='Optimization strategy for scheduling'
        )
        parser.add_argument(
            '--format',
            type=str,
            choices=['text', 'json'],
            default='text',
            help='Output format'
        )

    def handle(self, *args, **options):
        test_type = options['test_type']
        case_id = options.get('case_id')
        department_id = options.get('department_id')
        optimization_strategy = options['optimization_strategy']
        output_format = options['format']

        self.stdout.write(
            self.style.SUCCESS(f'Starting scheduling {test_type} test...')
        )

        try:
            if test_type == 'all':
                results = self.run_all_tests(case_id, department_id, optimization_strategy)
            elif test_type == 'scheduling':
                results = self.test_scheduling_engine(case_id, optimization_strategy)
            elif test_type == 'capacity':
                results = self.test_capacity_planning(department_id)
            elif test_type == 'resources':
                results = self.test_resource_management(department_id)
            elif test_type == 'conflicts':
                results = self.test_conflict_detection()

            # Output results
            if output_format == 'json':
                self.stdout.write(json.dumps(results, indent=2, default=str))
            else:
                self.display_text_results(results)

        except Exception as e:
            raise CommandError(f'Scheduling test failed: {e}')

    def run_all_tests(self, case_id=None, department_id=None, optimization_strategy='balanced'):
        """Run all scheduling tests"""
        results = {
            'timestamp': timezone.now(),
            'test_type': 'comprehensive_scheduling',
            'results': {}
        }

        # Test scheduling engine
        results['results']['scheduling'] = self.test_scheduling_engine(case_id, optimization_strategy)
        
        # Test capacity planning
        results['results']['capacity'] = self.test_capacity_planning(department_id)
        
        # Test resource management
        results['results']['resources'] = self.test_resource_management(department_id)
        
        # Test conflict detection
        results['results']['conflicts'] = self.test_conflict_detection()

        return results

    def test_scheduling_engine(self, case_id=None, optimization_strategy='balanced'):
        """Test the advanced scheduling engine"""
        results = {
            'test_name': 'Advanced Scheduling Engine',
            'tests': [],
            'errors': []
        }

        try:
            from common.scheduling_engine import scheduling_engine
            from case.services import SchedulingService
            
            # Test 1: Engine initialization
            results['tests'].append({
                'test': 'engine_initialization',
                'success': True,
                'message': 'Scheduling engine initialized successfully'
            })

            # Test 2: Case scheduling
            if case_id:
                case = self.get_case(case_id)
            else:
                from case.models import Case
                case = Case.objects.first()

            if case:
                # Test optimized scheduling
                success, message, schedule_id = SchedulingService.create_optimized_schedule(
                    case, optimization_strategy
                )
                
                results['tests'].append({
                    'test': 'create_optimized_schedule',
                    'case_id': case.id,
                    'optimization_strategy': optimization_strategy,
                    'success': success,
                    'message': message,
                    'schedule_id': schedule_id
                })

                # Test direct engine call
                engine_result = scheduling_engine.schedule_case(case, optimization_strategy)
                
                results['tests'].append({
                    'test': 'direct_engine_scheduling',
                    'case_id': case.id,
                    'success': engine_result.success,
                    'optimization_score': engine_result.optimization_score,
                    'conflicts': len(engine_result.conflicts or []),
                    'message': engine_result.message
                })

        except Exception as e:
            results['errors'].append(str(e))

        return results

    def test_capacity_planning(self, department_id=None):
        """Test capacity planning functionality"""
        results = {
            'test_name': 'Capacity Planning',
            'tests': [],
            'errors': []
        }

        try:
            from common.scheduling_engine import capacity_planner
            from case.services import SchedulingService

            # Test 1: Department capacity analysis
            if department_id:
                department = self.get_department(department_id)
            else:
                from case.models import Department
                department = Department.objects.first()

            if department:
                # Test capacity analysis
                analysis = SchedulingService.analyze_department_capacity(department, days_ahead=14)
                
                results['tests'].append({
                    'test': 'department_capacity_analysis',
                    'department_id': department.id,
                    'department_name': department.name,
                    'success': 'error' not in analysis,
                    'utilization': analysis.get('capacity_metrics', {}).get('utilization_percentage', 0),
                    'bottlenecks': len(analysis.get('bottlenecks', [])),
                    'recommendations': len(analysis.get('recommendations', []))
                })

                # Test capacity prediction
                prediction = SchedulingService.predict_capacity_needs(department, days_ahead=30)
                
                results['tests'].append({
                    'test': 'capacity_prediction',
                    'department_id': department.id,
                    'success': 'error' not in prediction,
                    'predicted_utilization': prediction.get('predictions', {}).get('predicted_utilization', 0),
                    'trend': prediction.get('historical_analysis', {}).get('trend_direction', 'unknown'),
                    'recommendations': len(prediction.get('recommendations', []))
                })

            # Test 2: Workload balancing
            balance_analysis = SchedulingService.balance_workload_across_departments(days_ahead=14)
            
            results['tests'].append({
                'test': 'workload_balancing',
                'success': 'error' not in balance_analysis,
                'departments_analyzed': balance_analysis.get('overall_metrics', {}).get('total_departments', 0),
                'overloaded_count': balance_analysis.get('overall_metrics', {}).get('overloaded_count', 0),
                'underutilized_count': balance_analysis.get('overall_metrics', {}).get('underutilized_count', 0),
                'system_health': balance_analysis.get('system_health', {}).get('status', 'unknown')
            })

        except Exception as e:
            results['errors'].append(str(e))

        return results

    def test_resource_management(self, department_id=None):
        """Test resource management functionality"""
        results = {
            'test_name': 'Resource Management',
            'tests': [],
            'errors': []
        }

        try:
            from common.resource_management import resource_manager
            from case.services import SchedulingService

            # Test 1: Resource availability check
            if department_id:
                department = self.get_department(department_id)
            else:
                from case.models import Department
                department = Department.objects.first()

            availability = SchedulingService.check_resource_availability(
                department=department,
                start_time=timezone.now(),
                end_time=timezone.now() + timedelta(hours=8)
            )
            
            results['tests'].append({
                'test': 'resource_availability_check',
                'department_id': department.id if department else None,
                'success': 'error' not in availability,
                'users_available': len(availability.get('users', [])),
                'departments_available': len(availability.get('departments', [])),
                'timestamp': availability.get('timestamp', 'unknown')
            })

            # Test 2: Utilization report
            utilization_report = SchedulingService.generate_utilization_report(days_back=30)
            
            results['tests'].append({
                'test': 'utilization_report',
                'success': 'error' not in utilization_report,
                'users_analyzed': utilization_report.get('overall_metrics', {}).get('total_users_analyzed', 0),
                'departments_analyzed': utilization_report.get('overall_metrics', {}).get('total_departments_analyzed', 0),
                'avg_user_utilization': utilization_report.get('overall_metrics', {}).get('average_user_utilization', 0),
                'avg_dept_utilization': utilization_report.get('overall_metrics', {}).get('average_department_utilization', 0)
            })

        except Exception as e:
            results['errors'].append(str(e))

        return results

    def test_conflict_detection(self):
        """Test conflict detection functionality"""
        results = {
            'test_name': 'Conflict Detection',
            'tests': [],
            'errors': []
        }

        try:
            from case.services import SchedulingService

            # Test conflict detection
            conflicts = SchedulingService.detect_scheduling_conflicts(
                start_date=date.today(),
                end_date=date.today() + timedelta(days=7)
            )
            
            results['tests'].append({
                'test': 'conflict_detection',
                'success': True,
                'conflicts_found': len(conflicts),
                'conflict_types': list(set(c.conflict_type for c in conflicts)) if conflicts else [],
                'critical_conflicts': len([c for c in conflicts if c.severity == 'critical']) if conflicts else 0,
                'high_conflicts': len([c for c in conflicts if c.severity == 'high']) if conflicts else 0
            })

        except Exception as e:
            results['errors'].append(str(e))

        return results

    def get_case(self, case_id):
        """Get case by ID"""
        try:
            from case.models import Case
            return Case.objects.get(id=case_id)
        except Case.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Case with ID {case_id} not found')
            )
            return None

    def get_department(self, department_id):
        """Get department by ID"""
        try:
            from case.models import Department
            return Department.objects.get(id=department_id)
        except Department.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Department with ID {department_id} not found')
            )
            return None

    def display_text_results(self, results):
        """Display results in text format"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(f"SCHEDULING TEST REPORT - {results.get('test_type', 'UNKNOWN').upper()}")
        self.stdout.write('='*60)
        self.stdout.write(f"Timestamp: {results.get('timestamp', 'Unknown')}")

        if 'results' in results:
            # Comprehensive test results
            for test_name, test_results in results['results'].items():
                self.display_test_section(test_name, test_results)
        else:
            # Single test results
            self.display_test_section(results.get('test_name', 'Test'), results)

    def display_test_section(self, test_name, test_results):
        """Display a test section"""
        self.stdout.write(f"\n{test_name.upper()}:")
        self.stdout.write('-'*40)

        if 'tests' in test_results:
            for test in test_results['tests']:
                success_icon = "✅" if test.get('success', False) else "❌"
                test_name_display = test.get('test', 'Unknown test').replace('_', ' ').title()
                self.stdout.write(f"  {success_icon} {test_name_display}")
                
                # Show key metrics
                if test.get('optimization_score'):
                    self.stdout.write(f"    Optimization Score: {test['optimization_score']:.1f}%")
                
                if test.get('utilization'):
                    self.stdout.write(f"    Utilization: {test['utilization']:.1f}%")
                
                if test.get('conflicts_found') is not None:
                    self.stdout.write(f"    Conflicts Found: {test['conflicts_found']}")
                
                if test.get('system_health'):
                    self.stdout.write(f"    System Health: {test['system_health']}")
                
                if not test.get('success', False) and 'message' in test:
                    self.stdout.write(f"    Error: {test['message']}")

        if 'errors' in test_results and test_results['errors']:
            self.stdout.write("  Errors:")
            for error in test_results['errors']:
                self.stdout.write(f"    ❌ {error}")

        if not test_results.get('tests') and not test_results.get('errors'):
            self.stdout.write("  ✅ No specific tests run")
