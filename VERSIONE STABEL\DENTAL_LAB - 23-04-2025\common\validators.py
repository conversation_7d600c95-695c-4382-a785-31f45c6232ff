"""
Enhanced validation system for the Dental Lab System
"""

from decimal import Decimal
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from typing import Dict, List, Any, Optional
import re
import logging

from .exceptions import (
    BusinessRuleViolation, DataIntegrityError, ValidationError as CustomValidationError
)

logger = logging.getLogger(__name__)

def validate_positive_amount(value):
    """Validate that an amount is positive"""
    if value <= 0:
        raise ValidationError(_('Amount must be positive'))

def validate_priority_range(value):
    """Validate that priority is within valid range (1-5)"""
    if not (1 <= value <= 5):
        raise ValidationError(_('Priority must be between 1 and 5'))

def validate_future_date(value):
    """Validate that a date is not in the past"""
    if value and value < timezone.now().date():
        raise ValidationError(_('Date cannot be in the past'))

def validate_deadline_after_received(received_date, deadline):
    """Validate that deadline is after received date"""
    if received_date and deadline and deadline < received_date:
        raise ValidationError(_('Deadline cannot be before received date'))

def validate_exchange_rate(value):
    """Validate exchange rate is positive and reasonable"""
    if value <= 0:
        raise ValidationError(_('Exchange rate must be positive'))
    if value > 10000:  # Reasonable upper limit
        raise ValidationError(_('Exchange rate seems unreasonably high'))

def validate_percentage(value):
    """Validate percentage is between 0 and 100"""
    if not (0 <= value <= 100):
        raise ValidationError(_('Percentage must be between 0 and 100'))

def validate_phone_number(value):
    """Validate phone number format"""
    import re
    if value and not re.match(r'^\+?[\d\s\-\(\)]{8,15}$', value):
        raise ValidationError(_('Invalid phone number format'))

def validate_email_domain(value):
    """Validate email domain is not from disposable email providers"""
    if value:
        disposable_domains = [
            '10minutemail.com', 'tempmail.org', 'guerrillamail.com'
        ]
        domain = value.split('@')[-1].lower()
        if domain in disposable_domains:
            raise ValidationError(_('Disposable email addresses are not allowed'))

class BusinessRuleValidator:
    """Validator for complex business rules"""

    @staticmethod
    def validate_case_creation(case_data):
        """Validate case creation business rules"""
        errors = {}

        # Required fields
        if not case_data.get('dentist'):
            errors['dentist'] = _('Dentist is required')

        if not case_data.get('patient'):
            errors['patient'] = _('Patient is required')

        # Date validations
        received_date = case_data.get('received_date_time')
        deadline = case_data.get('deadline')

        if received_date and deadline:
            try:
                # Convert string dates to datetime objects if needed
                if isinstance(received_date, str):
                    from django.utils.dateparse import parse_datetime, parse_date
                    received_date = parse_datetime(received_date) or parse_date(received_date)
                if isinstance(deadline, str):
                    from django.utils.dateparse import parse_datetime, parse_date
                    deadline = parse_datetime(deadline) or parse_date(deadline)

                if received_date and deadline and deadline < received_date:
                    errors['deadline'] = _('Deadline cannot be before received date')
            except (ValueError, TypeError) as e:
                errors['deadline'] = _('Invalid date format')

        # Priority validation
        priority = case_data.get('priority', 2)
        try:
            validate_priority_range(priority)
        except ValidationError as e:
            errors['priority'] = e.messages

        return errors

    @staticmethod
    def validate_invoice_creation(invoice_data):
        """Validate invoice creation business rules"""
        errors = {}

        # Required fields
        if not invoice_data.get('case'):
            errors['case'] = _('Case is required')

        if not invoice_data.get('dentist'):
            errors['dentist'] = _('Dentist is required')

        # Amount validation
        total_amount = invoice_data.get('total_amount', Decimal('0.00'))
        validate_positive_amount(total_amount)

        # Date validation
        due_date = invoice_data.get('due_date')
        invoice_date = invoice_data.get('date')

        if due_date and invoice_date and due_date < invoice_date:
            errors['due_date'] = _('Due date cannot be before invoice date')

        return errors

    @staticmethod
    def validate_payment_allocation(payment, invoice, amount):
        """Validate payment allocation business rules"""
        errors = []

        # Amount validations
        try:
            validate_positive_amount(amount)
        except ValidationError as e:
            errors.extend(e.messages)

        # Check payment has sufficient remaining amount
        from common.services import CalculationService
        remaining = CalculationService.calculate_payment_allocation_remaining(payment)
        if amount > remaining:
            errors.append(_('Amount exceeds remaining payment amount'))

        # Check invoice needs this payment
        paid_amount = getattr(invoice, 'get_paid_amount', lambda: Decimal('0.00'))()
        invoice_remaining = invoice.total_amount - paid_amount
        if amount > invoice_remaining:
            errors.append(_('Amount exceeds invoice remaining amount'))

        # Currency compatibility
        if payment.currency != invoice.currency:
            from common.services import CurrencyService
            validation = CurrencyService.validate_currency_pair(
                payment.currency.code,
                invoice.currency.code
            )
            if not validation['valid']:
                errors.extend(validation['errors'])
            else:
                # Check if exchange rate is available
                rate = CurrencyService.get_exchange_rate(
                    payment.currency.code,
                    invoice.currency.code
                )
                if not rate:
                    errors.append(_('No exchange rate available for currency conversion'))

        # Business rule: Check if invoice is not cancelled
        if invoice.status == 'cancelled':
            errors.append(_('Cannot allocate payment to cancelled invoice'))

        # Business rule: Check if payment date is not in future
        from django.utils import timezone
        if payment.date > timezone.now().date():
            errors.append(_('Cannot allocate payment with future date'))

        return errors

    @staticmethod
    def validate_payment_creation(payment_data):
        """Enhanced payment creation validation"""
        errors = {}

        # Required fields validation
        required_fields = ['dentist', 'amount', 'currency', 'account', 'date']
        for field in required_fields:
            if not payment_data.get(field):
                errors[field] = [_('This field is required')]

        # Amount validation
        amount = payment_data.get('amount')
        if amount is not None:
            try:
                amount = Decimal(str(amount))
                validate_positive_amount(amount)

                # Business rule: Maximum payment amount
                if amount > Decimal('1000000.00'):  # 1 million limit
                    errors.setdefault('amount', []).append(_('Payment amount exceeds maximum limit'))

            except (ValueError, ValidationError) as e:
                errors.setdefault('amount', []).append(str(e))

        # Date validation
        payment_date = payment_data.get('date')
        if payment_date:
            try:
                from django.utils import timezone
                from django.utils.dateparse import parse_date

                # Convert string to date if needed
                if isinstance(payment_date, str):
                    payment_date = parse_date(payment_date)

                if payment_date:
                    if payment_date > timezone.now().date():
                        errors.setdefault('date', []).append(_('Payment date cannot be in the future'))

                    # Business rule: Payment date not too old (1 year limit)
                    one_year_ago = timezone.now().date().replace(year=timezone.now().year - 1)
                    if payment_date < one_year_ago:
                        errors.setdefault('date', []).append(_('Payment date cannot be more than 1 year old'))
                else:
                    errors.setdefault('date', []).append(_('Invalid date format'))
            except (ValueError, TypeError):
                errors.setdefault('date', []).append(_('Invalid date format'))

        # Account and currency compatibility
        account_id = payment_data.get('account')
        currency_id = payment_data.get('currency')
        if account_id and currency_id:
            try:
                from finance.models import Account
                from items.models import Currency

                account = Account.objects.get(id=account_id)
                currency = Currency.objects.get(id=currency_id)

                # Check if currencies are compatible (same or convertible)
                if account.currency != currency:
                    from common.services import CurrencyService
                    validation = CurrencyService.validate_currency_pair(
                        currency.code, account.currency.code
                    )
                    if not validation['valid']:
                        errors.setdefault('currency', []).append(
                            _('Currency not compatible with selected account')
                        )

            except (Account.DoesNotExist, Currency.DoesNotExist):
                pass  # Will be caught by required field validation

        return errors

    @staticmethod
    def validate_workflow_transition(case, target_stage):
        """Validate workflow stage transition"""
        errors = []

        if not case.workflow_template:
            errors.append(_('Case has no workflow template'))
            return errors

        if target_stage.workflow != case.workflow_template:
            errors.append(_('Target stage does not belong to case workflow'))
            return errors

        # Check stage order
        if case.current_stage and target_stage.order <= case.current_stage.order:
            errors.append(_('Cannot move to previous or same stage'))

        # Check dependencies (if implemented)
        # This would need to be implemented based on your dependency model

        return errors

class DataIntegrityValidator:
    """Validator for data integrity checks"""

    @staticmethod
    def validate_financial_consistency():
        """Validate financial data consistency"""
        from billing.models import Invoice
        from finance.models import Payment, InvoicePayment
        from django.db.models import Sum, F

        errors = []

        # Check invoice totals
        for invoice in Invoice.objects.all():
            calculated_total = invoice.invoice_items.aggregate(
                total=Sum(F('quantity') * F('selling_price'))
            )['total'] or Decimal('0.00')

            if abs(invoice.total_amount - calculated_total) > Decimal('0.01'):
                errors.append(f'Invoice {invoice.id} total mismatch')

        # Check payment allocations
        for payment in Payment.objects.all():
            allocated = InvoicePayment.objects.filter(
                payment=payment
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0.00')

            if allocated > payment.amount:
                errors.append(f'Payment {payment.id} over-allocated')

        return errors

    @staticmethod
    def validate_status_consistency():
        """Validate status consistency across related objects"""
        from case.models import Case
        from billing.models import Invoice

        errors = []

        # Check case-schedule status consistency
        for case in Case.objects.filter(schedule__isnull=False):
            schedule = case.schedule
            if case.status == 'completed' and schedule.status != 'completed':
                errors.append(f'Case {case.case_number} status inconsistent with schedule')

        # Check invoice-case status consistency
        for invoice in Invoice.objects.all():
            if invoice.case.status == 'cancelled' and invoice.status != 'cancelled':
                errors.append(f'Invoice {invoice.id} status inconsistent with cancelled case')

        return errors

    @staticmethod
    def validate_currency_consistency():
        """Validate currency and exchange rate consistency"""
        from items.models import Currency, ExchangeRate

        errors = []

        base_currency = Currency.objects.filter(code='ALL').first()
        if not base_currency:
            errors.append('Base currency ALL not found')
            return errors

        # Check for missing exchange rates
        for currency in Currency.objects.exclude(code='ALL'):
            rate_exists = ExchangeRate.objects.filter(
                from_currency=currency,
                to_currency=base_currency
            ).exists()

            if not rate_exists:
                errors.append(f'Missing exchange rate from {currency.code} to ALL')

        return errors

def run_full_validation():
    """Run all validation checks"""
    all_errors = []

    # Financial consistency
    financial_errors = DataIntegrityValidator.validate_financial_consistency()
    all_errors.extend([f"Financial: {error}" for error in financial_errors])

    # Status consistency
    status_errors = DataIntegrityValidator.validate_status_consistency()
    all_errors.extend([f"Status: {error}" for error in status_errors])

    # Currency consistency
    currency_errors = DataIntegrityValidator.validate_currency_consistency()
    all_errors.extend([f"Currency: {error}" for error in currency_errors])

    return all_errors


class CentralizedValidationService:
    """
    Centralized validation service that coordinates all validation activities
    """

    @staticmethod
    def validate_model_data(model_class, data: Dict[str, Any], instance=None) -> Dict[str, List[str]]:
        """
        Validate data for a specific model using all applicable validators
        """
        errors = {}
        model_name = model_class.__name__.lower()

        try:
            # Apply model-specific business rules
            if model_name == 'case':
                business_errors = BusinessRuleValidator.validate_case_creation(data)
                errors.update(business_errors)

            elif model_name == 'invoice':
                business_errors = BusinessRuleValidator.validate_invoice_creation(data)
                errors.update(business_errors)

            elif model_name == 'payment':
                business_errors = BusinessRuleValidator.validate_payment_creation(data)
                errors.update(business_errors)

            # Apply field-level validations
            field_errors = CentralizedValidationService._validate_fields(model_class, data)
            for field, field_error_list in field_errors.items():
                if isinstance(field_error_list, list):
                    errors.setdefault(field, []).extend(field_error_list)
                else:
                    errors.setdefault(field, []).append(str(field_error_list))

            # Apply cross-field validations
            cross_field_errors = CentralizedValidationService._validate_cross_fields(model_class, data)
            for field, field_error_list in cross_field_errors.items():
                if isinstance(field_error_list, list):
                    errors.setdefault(field, []).extend(field_error_list)
                else:
                    errors.setdefault(field, []).append(str(field_error_list))

        except Exception as e:
            logger.error(f"Validation error for {model_name}: {e}", exc_info=True)
            errors['__all__'] = [_('Validation system error. Please try again.')]

        return errors

    @staticmethod
    def _validate_fields(model_class, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Validate individual fields"""
        errors = {}

        # Get model fields
        for field in model_class._meta.fields:
            field_name = field.name
            value = data.get(field_name)

            # Skip auto fields and primary keys
            if field.auto_created or field.primary_key:
                continue

            # Check for required fields
            if value is None:
                if not field.null and not field.blank and field_name in data:
                    errors.setdefault(field_name, []).append(_('This field is required'))
                continue

            # Skip validation for empty values on optional fields
            if not value and field.blank:
                continue

            # Apply field-specific validators
            try:
                # Only validate if we have a value
                if value is not None:
                    field.validate(value, model_instance=None)

                    # Apply custom validators based on field type
                    if hasattr(field, 'validators'):
                        for validator in field.validators:
                            try:
                                validator(value)
                            except ValidationError as ve:
                                if hasattr(ve, 'messages'):
                                    errors.setdefault(field_name, []).extend(ve.messages)
                                else:
                                    errors.setdefault(field_name, []).append(str(ve))

            except ValidationError as e:
                if hasattr(e, 'messages'):
                    errors.setdefault(field_name, []).extend(e.messages)
                else:
                    errors.setdefault(field_name, []).append(str(e))
            except Exception as e:
                # Catch any other validation errors
                errors.setdefault(field_name, []).append(f'Validation error: {str(e)}')

        return errors

    @staticmethod
    def _validate_cross_fields(model_class, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """Validate relationships between fields"""
        errors = {}
        model_name = model_class.__name__.lower()

        # Date field relationships
        if 'received_date_time' in data and 'deadline' in data:
            received = data.get('received_date_time')
            deadline = data.get('deadline')
            if received and deadline and deadline < received:
                errors.setdefault('deadline', []).append(
                    _('Deadline cannot be before received date')
                )

        # Amount and currency relationships
        if 'amount' in data and 'currency' in data:
            amount = data.get('amount')
            currency = data.get('currency')
            if amount and currency:
                try:
                    validate_positive_amount(amount)
                except ValidationError as e:
                    errors.setdefault('amount', []).extend(e.messages)

        return errors

    @staticmethod
    def validate_business_operation(operation_type: str, **kwargs) -> List[str]:
        """
        Validate complex business operations
        """
        errors = []

        try:
            if operation_type == 'payment_allocation':
                payment = kwargs.get('payment')
                invoice = kwargs.get('invoice')
                amount = kwargs.get('amount')

                if payment and invoice and amount:
                    allocation_errors = BusinessRuleValidator.validate_payment_allocation(
                        payment, invoice, amount
                    )
                    errors.extend(allocation_errors)

            elif operation_type == 'workflow_transition':
                case = kwargs.get('case')
                target_stage = kwargs.get('target_stage')

                if case and target_stage:
                    transition_errors = BusinessRuleValidator.validate_workflow_transition(
                        case, target_stage
                    )
                    errors.extend(transition_errors)

            elif operation_type == 'case_completion':
                case = kwargs.get('case')
                if case:
                    # Validate all tasks are completed
                    incomplete_tasks = case.tasks.filter(status__in=['pending', 'in_progress'])
                    if incomplete_tasks.exists():
                        errors.append(_('Cannot complete case with incomplete tasks'))

                    # Validate all invoices are paid or cancelled
                    unpaid_invoices = case.invoices.filter(
                        status__in=['draft', 'sent'],
                        total_amount__gt=0
                    )
                    if unpaid_invoices.exists():
                        errors.append(_('Cannot complete case with unpaid invoices'))

        except Exception as e:
            logger.error(f"Business operation validation error: {e}", exc_info=True)
            errors.append(_('Business validation error. Please try again.'))

        return errors

    @staticmethod
    def validate_data_integrity() -> Dict[str, List[str]]:
        """
        Run comprehensive data integrity checks
        """
        all_errors = {}

        try:
            # Financial consistency
            financial_errors = DataIntegrityValidator.validate_financial_consistency()
            if financial_errors:
                all_errors['financial'] = financial_errors

            # Status consistency
            status_errors = DataIntegrityValidator.validate_status_consistency()
            if status_errors:
                all_errors['status'] = status_errors

            # Currency consistency
            currency_errors = DataIntegrityValidator.validate_currency_consistency()
            if currency_errors:
                all_errors['currency'] = currency_errors

        except Exception as e:
            logger.error(f"Data integrity validation error: {e}", exc_info=True)
            all_errors['system'] = [_('Data integrity check failed')]

        return all_errors

    @staticmethod
    def get_validation_summary() -> Dict[str, Any]:
        """
        Get a summary of system validation status
        """
        try:
            integrity_errors = CentralizedValidationService.validate_data_integrity()

            total_errors = sum(len(errors) for errors in integrity_errors.values())

            return {
                'status': 'healthy' if total_errors == 0 else 'issues_found',
                'total_errors': total_errors,
                'error_categories': list(integrity_errors.keys()),
                'details': integrity_errors,
                'last_checked': timezone.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Validation summary error: {e}", exc_info=True)
            return {
                'status': 'error',
                'message': 'Unable to generate validation summary',
                'last_checked': timezone.now().isoformat()
            }
