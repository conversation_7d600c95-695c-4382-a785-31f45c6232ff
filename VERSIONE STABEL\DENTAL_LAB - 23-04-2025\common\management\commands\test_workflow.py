"""
Management command to test the workflow automation system
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.contrib.auth import get_user_model
from common.workflow_automation import workflow_automation, TransitionContext
from common.status_synchronization import status_sync
from common.dependency_management import dependency_manager
from common.exceptions import WorkflowError
import json

User = get_user_model()


class Command(BaseCommand):
    help = 'Test the workflow automation system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-type',
            type=str,
            choices=['transitions', 'auto-progress', 'dependencies', 'sync', 'all'],
            default='all',
            help='Type of workflow test to run'
        )
        parser.add_argument(
            '--case-id',
            type=int,
            help='Specific case ID to test with'
        )
        parser.add_argument(
            '--task-id',
            type=int,
            help='Specific task ID to test with'
        )
        parser.add_argument(
            '--format',
            type=str,
            choices=['text', 'json'],
            default='text',
            help='Output format'
        )

    def handle(self, *args, **options):
        test_type = options['test_type']
        case_id = options.get('case_id')
        task_id = options.get('task_id')
        output_format = options['format']

        self.stdout.write(
            self.style.SUCCESS(f'Starting workflow {test_type} test...')
        )

        try:
            if test_type == 'all':
                results = self.run_all_tests(case_id, task_id)
            elif test_type == 'transitions':
                results = self.test_state_transitions(case_id, task_id)
            elif test_type == 'auto-progress':
                results = self.test_auto_progression(case_id)
            elif test_type == 'dependencies':
                results = self.test_dependencies(task_id)
            elif test_type == 'sync':
                results = self.test_status_synchronization(case_id)

            # Output results
            if output_format == 'json':
                self.stdout.write(json.dumps(results, indent=2, default=str))
            else:
                self.display_text_results(results)

        except Exception as e:
            raise CommandError(f'Workflow test failed: {e}')

    def run_all_tests(self, case_id=None, task_id=None):
        """Run all workflow tests"""
        results = {
            'timestamp': timezone.now(),
            'test_type': 'comprehensive',
            'results': {}
        }

        # Test state transitions
        results['results']['transitions'] = self.test_state_transitions(case_id, task_id)
        
        # Test auto-progression
        results['results']['auto_progress'] = self.test_auto_progression(case_id)
        
        # Test dependencies
        results['results']['dependencies'] = self.test_dependencies(task_id)
        
        # Test status synchronization
        results['results']['sync'] = self.test_status_synchronization(case_id)

        return results

    def test_state_transitions(self, case_id=None, task_id=None):
        """Test state machine transitions"""
        results = {
            'test_name': 'State Transitions',
            'case_tests': [],
            'task_tests': [],
            'errors': []
        }

        try:
            # Test case transitions
            if case_id:
                case = self.get_case(case_id)
                if case:
                    results['case_tests'] = self.test_case_transitions(case)
            else:
                # Test with first available case
                from case.models import Case
                case = Case.objects.first()
                if case:
                    results['case_tests'] = self.test_case_transitions(case)

            # Test task transitions
            if task_id:
                task = self.get_task(task_id)
                if task:
                    results['task_tests'] = self.test_task_transitions(task)
            else:
                # Test with first available task
                from case.models import Task
                task = Task.objects.first()
                if task:
                    results['task_tests'] = self.test_task_transitions(task)

        except Exception as e:
            results['errors'].append(str(e))

        return results

    def test_case_transitions(self, case):
        """Test case state transitions"""
        tests = []
        
        try:
            # Get valid transitions
            valid_transitions = workflow_automation.get_valid_transitions('case', case)
            
            tests.append({
                'test': 'get_valid_transitions',
                'case_id': case.id,
                'current_status': case.status,
                'valid_transitions': [t.to_state for t in valid_transitions],
                'success': True
            })
            
            # Test a transition if available
            if valid_transitions:
                transition = valid_transitions[0]
                context = TransitionContext(
                    reason="Test transition",
                    force=False
                )
                
                result, message = workflow_automation.transition_entity(
                    'case', case, transition.to_state, context
                )
                
                tests.append({
                    'test': 'transition_entity',
                    'case_id': case.id,
                    'from_state': case.status,
                    'to_state': transition.to_state,
                    'result': result.value,
                    'message': message,
                    'success': result.value == 'success'
                })

        except Exception as e:
            tests.append({
                'test': 'case_transitions',
                'error': str(e),
                'success': False
            })

        return tests

    def test_task_transitions(self, task):
        """Test task state transitions"""
        tests = []
        
        try:
            # Get valid transitions
            valid_transitions = workflow_automation.get_valid_transitions('task', task)
            
            tests.append({
                'test': 'get_valid_transitions',
                'task_id': task.id,
                'current_status': task.status,
                'valid_transitions': [t.to_state for t in valid_transitions],
                'success': True
            })

        except Exception as e:
            tests.append({
                'test': 'task_transitions',
                'error': str(e),
                'success': False
            })

        return tests

    def test_auto_progression(self, case_id=None):
        """Test automatic workflow progression"""
        results = {
            'test_name': 'Auto Progression',
            'tests': [],
            'errors': []
        }

        try:
            if case_id:
                case = self.get_case(case_id)
            else:
                from case.models import Case
                case = Case.objects.first()

            if case:
                # Test auto-progression
                progression_results = workflow_automation.auto_progress_entity('case', case)
                
                results['tests'].append({
                    'test': 'auto_progress_case',
                    'case_id': case.id,
                    'progressions': len(progression_results),
                    'results': [(r.value, msg) for r, msg in progression_results],
                    'success': True
                })

        except Exception as e:
            results['errors'].append(str(e))

        return results

    def test_dependencies(self, task_id=None):
        """Test dependency management"""
        results = {
            'test_name': 'Dependencies',
            'tests': [],
            'errors': []
        }

        try:
            if task_id:
                task = self.get_task(task_id)
            else:
                from case.models import Task
                task = Task.objects.first()

            if task:
                # Test dependency chain
                dependency_chain = dependency_manager.get_task_dependency_chain(task)
                
                results['tests'].append({
                    'test': 'get_dependency_chain',
                    'task_id': task.id,
                    'chain': dependency_chain,
                    'success': 'error' not in dependency_chain
                })

                # Test critical path if task has a case
                if hasattr(task, 'case') and task.case:
                    critical_path = dependency_manager.get_critical_path(task.case)
                    
                    results['tests'].append({
                        'test': 'get_critical_path',
                        'case_id': task.case.id,
                        'critical_path': critical_path,
                        'success': 'error' not in critical_path
                    })

        except Exception as e:
            results['errors'].append(str(e))

        return results

    def test_status_synchronization(self, case_id=None):
        """Test status synchronization"""
        results = {
            'test_name': 'Status Synchronization',
            'tests': [],
            'errors': []
        }

        try:
            if case_id:
                case = self.get_case(case_id)
            else:
                from case.models import Case
                case = Case.objects.first()

            if case:
                # Test case-task sync
                changed, new_status, message = status_sync.sync_case_status_from_tasks(case)
                
                results['tests'].append({
                    'test': 'sync_case_status_from_tasks',
                    'case_id': case.id,
                    'changed': changed,
                    'new_status': new_status,
                    'message': message,
                    'success': True
                })

                # Test status consistency validation
                consistency_errors = status_sync.validate_status_consistency()
                
                results['tests'].append({
                    'test': 'validate_status_consistency',
                    'errors_found': len(consistency_errors),
                    'errors': consistency_errors,
                    'success': len(consistency_errors) == 0
                })

        except Exception as e:
            results['errors'].append(str(e))

        return results

    def get_case(self, case_id):
        """Get case by ID"""
        try:
            from case.models import Case
            return Case.objects.get(id=case_id)
        except Case.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Case with ID {case_id} not found')
            )
            return None

    def get_task(self, task_id):
        """Get task by ID"""
        try:
            from case.models import Task
            return Task.objects.get(id=task_id)
        except Task.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Task with ID {task_id} not found')
            )
            return None

    def display_text_results(self, results):
        """Display results in text format"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(f"WORKFLOW TEST REPORT - {results.get('test_type', 'UNKNOWN').upper()}")
        self.stdout.write('='*60)
        self.stdout.write(f"Timestamp: {results.get('timestamp', 'Unknown')}")

        if 'results' in results:
            # Comprehensive test results
            for test_name, test_results in results['results'].items():
                self.display_test_section(test_name, test_results)
        else:
            # Single test results
            self.display_test_section(results.get('test_name', 'Test'), results)

    def display_test_section(self, test_name, test_results):
        """Display a test section"""
        self.stdout.write(f"\n{test_name.upper()}:")
        self.stdout.write('-'*40)

        if 'tests' in test_results:
            for test in test_results['tests']:
                success_icon = "✅" if test.get('success', False) else "❌"
                self.stdout.write(f"  {success_icon} {test.get('test', 'Unknown test')}")
                
                if not test.get('success', False) and 'error' in test:
                    self.stdout.write(f"    Error: {test['error']}")

        if 'case_tests' in test_results:
            for test in test_results['case_tests']:
                success_icon = "✅" if test.get('success', False) else "❌"
                self.stdout.write(f"  {success_icon} Case Test: {test.get('test', 'Unknown')}")

        if 'task_tests' in test_results:
            for test in test_results['task_tests']:
                success_icon = "✅" if test.get('success', False) else "❌"
                self.stdout.write(f"  {success_icon} Task Test: {test.get('test', 'Unknown')}")

        if 'errors' in test_results and test_results['errors']:
            self.stdout.write("  Errors:")
            for error in test_results['errors']:
                self.stdout.write(f"    ❌ {error}")

        if not test_results.get('tests') and not test_results.get('case_tests') and not test_results.get('task_tests'):
            self.stdout.write("  ✅ No specific tests run")
