#!/usr/bin/env python
"""
Data Fix Script
Fixes the data integrity issues identified by the audit
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

from django.db import transaction
from common.services import DataConsistencyService, CalculationService
from billing.models import Invoice
from items.models import Currency, ExchangeRate
from case.models import Case

class DataFixer:
    def __init__(self):
        self.fixes_applied = []
        self.errors = []

    def log_fix(self, description):
        """Log a successful fix"""
        self.fixes_applied.append(description)
        print(f"✅ {description}")

    def log_error(self, description):
        """Log an error during fixing"""
        self.errors.append(description)
        print(f"❌ {description}")

    @transaction.atomic
    def fix_invoice_totals(self):
        """Fix invoice total calculation mismatches"""
        print("\n=== Fixing Invoice Totals ===")

        try:
            fixed_count = DataConsistencyService.fix_all_invoice_totals()
            if fixed_count > 0:
                self.log_fix(f"Fixed {fixed_count} invoice total calculations")
            else:
                self.log_fix("All invoice totals are already correct")
        except Exception as e:
            self.log_error(f"Failed to fix invoice totals: {e}")

    @transaction.atomic
    def fix_missing_exchange_rates(self):
        """Create missing exchange rates"""
        print("\n=== Creating Missing Exchange Rates ===")

        try:
            created_count = DataConsistencyService.create_missing_exchange_rates()
            if created_count > 0:
                self.log_fix(f"Created {created_count} missing exchange rates")
            else:
                self.log_fix("All required exchange rates already exist")
        except Exception as e:
            self.log_error(f"Failed to create exchange rates: {e}")

    @transaction.atomic
    def fix_status_inconsistencies(self):
        """Fix status inconsistencies between related objects"""
        print("\n=== Fixing Status Inconsistencies ===")

        try:
            fixed_count = DataConsistencyService.fix_all_status_inconsistencies()
            if fixed_count > 0:
                self.log_fix(f"Fixed {fixed_count} status inconsistencies")
            else:
                self.log_fix("All statuses are already consistent")
        except Exception as e:
            self.log_error(f"Failed to fix status inconsistencies: {e}")

    def add_database_constraints(self):
        """Add missing database constraints (SQLite compatible)"""
        print("\n=== Adding Database Constraints ===")

        # SQLite doesn't support ADD CONSTRAINT for CHECK constraints
        # We'll validate data instead and add model-level constraints

        try:
            # Validate that all amounts are positive
            negative_invoices = Invoice.objects.filter(total_amount__lt=0)
            if negative_invoices.exists():
                self.log_error(f"Found {negative_invoices.count()} invoices with negative amounts")
                # Fix negative amounts
                for invoice in negative_invoices:
                    invoice.total_amount = abs(invoice.total_amount)
                    invoice.save(update_fields=['total_amount'])
                self.log_fix(f"Fixed {negative_invoices.count()} negative invoice amounts")
            else:
                self.log_fix("All invoice amounts are positive")

            # Validate priority values
            invalid_priorities = Case.objects.exclude(priority__range=(1, 5))
            if invalid_priorities.exists():
                self.log_error(f"Found {invalid_priorities.count()} cases with invalid priority")
                # Fix invalid priorities
                for case in invalid_priorities:
                    case.priority = 2  # Set to normal priority
                    case.save(update_fields=['priority'])
                self.log_fix(f"Fixed {invalid_priorities.count()} invalid case priorities")
            else:
                self.log_fix("All case priorities are valid")

        except Exception as e:
            self.log_error(f"Failed to validate constraints: {e}")

    def validate_critical_data(self):
        """Validate critical data after fixes"""
        print("\n=== Validating Critical Data ===")

        # Check for negative amounts
        negative_invoices = Invoice.objects.filter(total_amount__lt=0)
        if negative_invoices.exists():
            self.log_error(f"Found {negative_invoices.count()} invoices with negative amounts")
        else:
            self.log_fix("All invoice amounts are positive")

        # Check for missing base currency
        base_currency = Currency.objects.filter(code='ALL').first()
        if not base_currency:
            self.log_error("Base currency 'ALL' is missing")
        else:
            self.log_fix("Base currency 'ALL' exists")

        # Check for orphaned records
        orphaned_invoices = Invoice.objects.filter(case__isnull=True)
        if orphaned_invoices.exists():
            self.log_error(f"Found {orphaned_invoices.count()} orphaned invoices")
        else:
            self.log_fix("No orphaned invoices found")

    def backup_critical_data(self):
        """Create backup of critical data before fixes"""
        print("\n=== Creating Data Backup ===")

        from django.core.management import call_command
        import datetime

        backup_filename = f"backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            # Create a fixture backup of critical models
            call_command('dumpdata',
                        'billing.Invoice',
                        'billing.InvoiceItem',
                        'finance.Payment',
                        'finance.InvoicePayment',
                        'items.ExchangeRate',
                        output=backup_filename,
                        indent=2)
            self.log_fix(f"Created backup: {backup_filename}")
        except Exception as e:
            self.log_error(f"Failed to create backup: {e}")

    def run_all_fixes(self):
        """Run all data fixes"""
        print("Starting Data Fix Process...")
        print("=" * 50)

        # Create backup first
        self.backup_critical_data()

        # Apply fixes
        self.fix_missing_exchange_rates()
        self.fix_invoice_totals()
        self.fix_status_inconsistencies()
        self.add_database_constraints()

        # Validate results
        self.validate_critical_data()

        # Summary
        print("\n" + "=" * 50)
        print("DATA FIX SUMMARY")
        print("=" * 50)
        print(f"Fixes Applied: {len(self.fixes_applied)}")
        print(f"Errors Encountered: {len(self.errors)}")

        if self.fixes_applied:
            print("\nSUCCESSFUL FIXES:")
            for fix in self.fixes_applied:
                print(f"✅ {fix}")

        if self.errors:
            print("\nERRORS:")
            for error in self.errors:
                print(f"❌ {error}")

        success = len(self.errors) == 0
        if success:
            print("\n🎉 All data fixes completed successfully!")
        else:
            print("\n⚠️  Some fixes failed. Please review errors above.")

        return success

if __name__ == '__main__':
    fixer = DataFixer()
    success = fixer.run_all_fixes()

    if success:
        print("\n✅ Data integrity restored! You can now proceed with development.")
        sys.exit(0)
    else:
        print("\n⚠️  Some issues remain. Please review and fix manually.")
        sys.exit(1)
