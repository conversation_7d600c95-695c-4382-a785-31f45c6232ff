{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Tasks Management" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
<style>
    :root {
        --priority-low: #6c757d;
        --priority-medium: #0d6efd;
        --priority-high: #ffc107;
        --priority-urgent: #dc3545;

        --status-pending: #6c757d;
        --status-in-progress: #0d6efd;
        --status-paused: #6610f2;
        --status-completed: #198754;
        --status-delayed: #dc3545;
        --status-blocked: #dc3545;
        --status-cancelled: #6c757d;
        --status-review: #0dcaf0;

        --border-radius-sm: 0.25rem;
        --border-radius: 0.375rem;
        --border-radius-lg: 0.5rem;
        --border-radius-xl: 1rem;

        --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    }

    /* Page Layout */
    .task-container {
        max-width: 95%;
        margin: 0 auto;
        padding: 1.5rem;
    }

    .page-header {
        background-color: #fff;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--shadow-sm);
    }

    /* Stats Cards */
    .stats-card {
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow);
    }

    .stats-card .card-body {
        padding: 1.5rem;
    }

    .stats-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stats-label {
        font-size: 0.875rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Task Table */
    .task-table {
        border-collapse: separate;
        border-spacing: 0;
    }

    .task-table thead th {
        background-color: #f8f9fa;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
        padding: 1rem;
        border-bottom: 2px solid #e9ecef;
    }

    .task-table tbody tr {
        transition: all 0.2s ease;
        border-left: 3px solid transparent;
    }

    .task-table tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }

    .task-table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #e9ecef;
    }

    /* Priority Indicators */
    .priority-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }

    .priority-1 .priority-indicator {
        background-color: var(--priority-low);
    }

    .priority-2 .priority-indicator {
        background-color: var(--priority-medium);
    }

    .priority-3 .priority-indicator {
        background-color: var(--priority-high);
    }

    .priority-4 .priority-indicator {
        background-color: var(--priority-urgent);
    }

    .priority-1 {
        border-left-color: var(--priority-low) !important;
    }

    .priority-2 {
        border-left-color: var(--priority-medium) !important;
    }

    .priority-3 {
        border-left-color: var(--priority-high) !important;
    }

    .priority-4 {
        border-left-color: var(--priority-urgent) !important;
    }

    /* Status Badges */
    .status-badge {
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.35em 0.65em;
        border-radius: 50rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Task Elements */
    .task-title {
        font-weight: 600;
        color: #212529;
        font-size: 0.95rem;
    }

    .task-description {
        font-size: 0.8rem;
        max-width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .task-progress {
        height: 8px;
        border-radius: 4px;
        overflow: hidden;
    }

    /* Avatar */
    .avatar {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-weight: 600;
        font-size: 0.875rem;
    }

    .avatar-sm {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    /* Timeline Info */
    .timeline-info {
        font-size: 0.8rem;
    }

    .timeline-info i {
        width: 16px;
        text-align: center;
        margin-right: 4px;
    }

    /* Empty State */
    .empty-state {
        padding: 4rem 2rem;
        text-align: center;
        background-color: #f8f9fa;
        border-radius: var(--border-radius);
        margin: 2rem 0;
    }

    .empty-state i {
        font-size: 4rem;
        color: #adb5bd;
        margin-bottom: 1.5rem;
    }

    .empty-state h4 {
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .empty-state p {
        max-width: 500px;
        margin: 0 auto 1.5rem;
    }

    /* Filter Card */
    .filter-card {
        background-color: #fff;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        margin-bottom: 1.5rem;
        border: none;
    }

    .filter-card .card-body {
        padding: 1.25rem;
    }

    .filter-form .form-control,
    .filter-form .form-select {
        border-radius: var(--border-radius-sm);
        font-size: 0.875rem;
    }

    .filter-form .btn {
        border-radius: var(--border-radius-sm);
        font-size: 0.875rem;
    }

    /* Dropdown positioning */
    .task-table .dropdown-menu {
        min-width: 200px;
        max-width: 250px;
        margin-top: 0;
        left: auto !important;
        right: auto !important;
        transform: none !important;
    }

    /* Ensure dropdown stays within table */
    .task-table td:last-child .dropdown-menu {
        right: 0 !important;
        left: auto !important;
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
        .task-container {
            max-width: 100%;
            padding: 1rem;
        }

        .stats-row .col-md-3 {
            margin-bottom: 1rem;
        }

        .task-table thead th,
        .task-table tbody td {
            padding: 0.75rem;
        }
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
        }

        .task-table {
            min-width: 900px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="task-container py-4">
    <!-- Header Section -->
    <div class="page-header d-flex justify-content-between align-items-center mb-4 animate__animated animate__fadeIn">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="bi bi-list-task me-2"></i>{% trans "Tasks Management" %}
            </h1>
            <p class="text-muted">{% trans "Manage and track all tasks across your cases" %}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{% url 'scheduling:schedule_item_list' %}" class="btn btn-outline-primary">
                <i class="bi bi-calendar-week me-1"></i>{% trans "Schedule View" %}
            </a>
            <a href="{% url 'case:task_create_general' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-1"></i>{% trans "Create New Task" %}
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row stats-row mb-4">
        <div class="col-md-3 col-sm-6 mb-3 animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="stats-icon bg-primary bg-opacity-10 text-primary">
                        <i class="bi bi-hourglass-top"></i>
                    </div>
                    <div class="stats-number">{{ stats.pending_count }}</div>
                    <div class="stats-label">{% trans "Pending Tasks" %}</div>
                </div>
                <div class="card-footer bg-primary bg-opacity-10 py-2 text-center">
                    <a href="?status=pending" class="text-decoration-none text-primary small">
                        <i class="bi bi-eye me-1"></i>{% trans "View All" %}
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="stats-icon bg-warning bg-opacity-10 text-warning">
                        <i class="bi bi-arrow-repeat"></i>
                    </div>
                    <div class="stats-number">{{ stats.in_progress_count }}</div>
                    <div class="stats-label">{% trans "In Progress" %}</div>
                </div>
                <div class="card-footer bg-warning bg-opacity-10 py-2 text-center">
                    <a href="?status=in_progress" class="text-decoration-none text-warning small">
                        <i class="bi bi-eye me-1"></i>{% trans "View All" %}
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3 animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="stats-icon bg-success bg-opacity-10 text-success">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div class="stats-number">{{ stats.completed_count }}</div>
                    <div class="stats-label">{% trans "Completed" %}</div>
                </div>
                <div class="card-footer bg-success bg-opacity-10 py-2 text-center">
                    <a href="?status=completed" class="text-decoration-none text-success small">
                        <i class="bi bi-eye me-1"></i>{% trans "View All" %}
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3 animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
            <div class="card stats-card h-100">
                <div class="card-body">
                    <div class="stats-icon bg-danger bg-opacity-10 text-danger">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="stats-number">{{ stats.delayed_count }}</div>
                    <div class="stats-label">{% trans "Delayed" %}</div>
                </div>
                <div class="card-footer bg-danger bg-opacity-10 py-2 text-center">
                    <a href="?status=delayed" class="text-decoration-none text-danger small">
                        <i class="bi bi-eye me-1"></i>{% trans "View All" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filter-card animate__animated animate__fadeIn" style="animation-delay: 0.5s">
        <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-funnel me-2"></i>{% trans "Filter Tasks" %}
            </h5>
            <button class="btn btn-sm btn-link text-muted" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="true" aria-controls="filterCollapse">
                <i class="bi bi-chevron-up"></i>
            </button>
        </div>
        <div class="collapse show" id="filterCollapse">
            <div class="card-body">
                <form method="get" class="row g-3 filter-form">
                    <div class="col-md-3">
                        <label for="search" class="form-label">{% trans "Search" %}</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" id="search" name="search"
                                placeholder="{% trans 'Search by title or case number' %}"
                                value="{{ search_query }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">{% trans "Status" %}</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">{% trans "All Statuses" %}</option>
                            {% for status_code, status_name in status_choices %}
                                <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>
                                    {{ status_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="priority" class="form-label">{% trans "Priority" %}</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="">{% trans "All Priorities" %}</option>
                            {% for priority_code, priority_name in priority_choices %}
                                <option value="{{ priority_code }}" {% if priority_filter == priority_code|stringformat:"i" %}selected{% endif %}>
                                    {{ priority_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="due_date" class="form-label">{% trans "Due Date" %}</label>
                        <select class="form-select" id="due_date" name="due_date">
                            <option value="">{% trans "All Dates" %}</option>
                            <option value="today" {% if due_date_filter == 'today' %}selected{% endif %}>{% trans "Today" %}</option>
                            <option value="tomorrow" {% if due_date_filter == 'tomorrow' %}selected{% endif %}>{% trans "Tomorrow" %}</option>
                            <option value="this_week" {% if due_date_filter == 'this_week' %}selected{% endif %}>{% trans "This Week" %}</option>
                            <option value="next_week" {% if due_date_filter == 'next_week' %}selected{% endif %}>{% trans "Next Week" %}</option>
                            <option value="overdue" {% if due_date_filter == 'overdue' %}selected{% endif %}>{% trans "Overdue" %}</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-filter me-1"></i>{% trans "Apply Filters" %}
                            </button>
                            <a href="{% url 'case:task_list' %}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-1"></i>{% trans "Clear" %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Tasks Table -->
    <div class="card border-0 shadow-sm animate__animated animate__fadeIn" style="animation-delay: 0.6s">
        <div class="card-body">
            {% if tasks %}
                <div class="table-responsive">
                    <table class="table table-hover align-middle task-table">
                        <thead>
                            <tr>
                                <th style="width: 8%;">{% trans "Case #" %}</th>
                                <th style="width: 18%;">{% trans "Title" %}</th>
                                <th style="width: 8%;">{% trans "Stage" %}</th>
                                <th style="width: 12%;">{% trans "Assigned To" %}</th>
                                <th style="width: 16%;">{% trans "Timeline" %}</th>
                                <th style="width: 8%;">{% trans "Priority" %}</th>
                                <th style="width: 8%;">{% trans "Status" %}</th>
                                <th style="width: 10%;">{% trans "Progress" %}</th>
                                <th class="text-center" style="width: 10%;">{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks %}
                            <tr class="priority-{{ task.priority }} animate__animated animate__fadeIn" style="animation-delay: {{ forloop.counter|add:6|divisibleby:10 }}0ms">
                                <td>
                                    <a href="{% url 'case:case_detail' task.case.case_number %}"
                                       class="text-decoration-none fw-semibold">
                                        #{{ task.case.case_number }}
                                    </a>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="d-flex align-items-center">
                                            <span class="priority-indicator"></span>
                                            <span class="task-title">{{ task.title }}</span>
                                        </div>
                                        {% if task.description %}
                                        <small class="text-muted task-description" data-bs-toggle="tooltip" title="{{ task.description }}">
                                            {{ task.description|truncatechars:50 }}
                                        </small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if task.workflow_stage %}
                                    <span class="badge bg-info bg-opacity-10 text-info">
                                        {{ task.workflow_stage.name }}
                                    </span>
                                    {% else %}
                                    <span class="text-muted">—</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.assigned_to %}
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm bg-primary bg-opacity-10 text-primary rounded-circle me-2">
                                            {{ task.assigned_to.get_full_name|default:task.assigned_to.email|slice:":1"|upper }}
                                        </div>
                                        <span>{{ task.assigned_to.get_full_name|default:task.assigned_to.email }}</span>
                                    </div>
                                    {% else %}
                                    <span class="badge bg-secondary bg-opacity-10 text-secondary">
                                        {% trans "Unassigned" %}
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.schedule_items.all %}
                                        {% with schedule=task.schedule_items.first %}
                                        <div class="timeline-info">
                                            <div class="mb-1">
                                                <i class="bi bi-calendar-event text-primary"></i>
                                                <span>{{ schedule.start_time|date:"M d, Y H:i"|default:"Not scheduled" }}</span>
                                            </div>
                                            <div>
                                                <i class="bi bi-calendar-check text-success"></i>
                                                <span>{{ schedule.end_time|date:"M d, Y H:i"|default:"Not scheduled" }}</span>
                                            </div>
                                        </div>
                                        {% endwith %}
                                    {% else %}
                                        <div class="timeline-info">
                                            <div class="mb-1">
                                                <i class="bi bi-calendar-event text-primary"></i>
                                                <span>{{ task.actual_start_time|date:"M d, Y H:i"|default:"Not started" }}</span>
                                            </div>
                                            <div>
                                                <i class="bi bi-calendar-check text-success"></i>
                                                <span>{{ task.actual_end_time|date:"M d, Y H:i"|default:"Not completed" }}</span>
                                            </div>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge rounded-pill status-badge
                                        {% if task.priority == 4 %}bg-danger
                                        {% elif task.priority == 3 %}bg-warning
                                        {% elif task.priority == 2 %}bg-info
                                        {% else %}bg-secondary{% endif %}">
                                        {{ task.get_priority_display }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge rounded-pill status-badge
                                        {% if task.status == 'completed' %}bg-success
                                        {% elif task.status == 'in_progress' %}bg-warning
                                        {% elif task.status == 'delayed' %}bg-danger
                                        {% elif task.status == 'paused' %}bg-info
                                        {% elif task.status == 'blocked' %}bg-danger
                                        {% elif task.status == 'cancelled' %}bg-secondary
                                        {% elif task.status == 'review' %}bg-primary
                                        {% else %}bg-primary{% endif %}">
                                        {{ task.get_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="progress task-progress flex-grow-1 me-2">
                                            <div class="progress-bar
                                                {% if task.status == 'delayed' %}bg-danger
                                                {% elif task.status == 'completed' %}bg-success
                                                {% elif task.status == 'in_progress' %}bg-warning
                                                {% else %}bg-primary{% endif %}"
                                                 role="progressbar"
                                                 style="width: {{ task.progress }}%"
                                                 aria-valuenow="{{ task.progress }}"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100">
                                            </div>
                                        </div>
                                        <span class="small">{{ task.progress }}%</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex justify-content-center">
                                        <!-- Single dropdown for all actions -->
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                    type="button"
                                                    data-bs-toggle="dropdown"
                                                    aria-expanded="false">
                                                <i class="bi bi-gear me-1"></i>
                                                {% trans "Actions" %}
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-start">
                                                <!-- View action -->
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'case:task_detail' task.id %}">
                                                        <i class="bi bi-eye me-2 text-primary"></i>
                                                        {% trans "View Details" %}
                                                    </a>
                                                </li>

                                                <!-- Edit action -->
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'case:task_update' task.id %}">
                                                        <i class="bi bi-pencil me-2 text-secondary"></i>
                                                        {% trans "Edit Task" %}
                                                    </a>
                                                </li>

                                                <!-- Status-specific actions -->
                                                {% if task.status == 'pending' %}
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'case:task_start' task.id %}">
                                                        <i class="bi bi-play-fill me-2 text-success"></i>
                                                        {% trans "Start Task" %}
                                                    </a>
                                                </li>
                                                {% endif %}

                                                {% if task.status == 'in_progress' %}
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'case:task_complete' task.id %}">
                                                        <i class="bi bi-check-lg me-2 text-success"></i>
                                                        {% trans "Complete Task" %}
                                                    </a>
                                                </li>
                                                {% endif %}

                                                <!-- Related case link -->
                                                {% if task.case %}
                                                <li>
                                                    <a class="dropdown-item" href="{% url 'case:case_detail' task.case.case_number %}">
                                                        <i class="bi bi-folder me-2 text-info"></i>
                                                        {% trans "View Case" %}
                                                    </a>
                                                </li>
                                                {% endif %}

                                                <li><hr class="dropdown-divider"></li>

                                                <!-- Delete action -->
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#"
                                                       onclick="confirmDelete('{% url 'case:task_delete' task.id %}')">
                                                        <i class="bi bi-trash me-2"></i>
                                                        {% trans "Delete Task" %}
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="empty-state animate__animated animate__fadeIn">
                    <i class="bi bi-clipboard-x"></i>
                    <h4 class="mt-3">{% trans "No Tasks Found" %}</h4>
                    <p class="text-muted">{% trans "There are no tasks matching your criteria." %}</p>
                    <a href="{% url 'case:task_create_general' %}" class="btn btn-primary btn-lg mt-3">
                        <i class="bi bi-plus-circle me-1"></i>{% trans "Create New Task" %}
                    </a>
                </div>
            {% endif %}

            <!-- Pagination -->
            {% if tasks and tasks.has_other_pages %}
            <div class="d-flex justify-content-center mt-4 animate__animated animate__fadeIn" style="animation-delay: 0.8s">
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-md">
                        {% if tasks.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if priority_filter %}&priority={{ priority_filter }}{% endif %}{% if due_date_filter %}&due_date={{ due_date_filter }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ tasks.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if priority_filter %}&priority={{ priority_filter }}{% endif %}{% if due_date_filter %}&due_date={{ due_date_filter }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% endif %}

                        {% for num in tasks.paginator.page_range %}
                            {% if tasks.number == num %}
                                <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                            {% elif num > tasks.number|add:'-3' and num < tasks.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if priority_filter %}&priority={{ priority_filter }}{% endif %}{% if due_date_filter %}&due_date={{ due_date_filter }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if tasks.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ tasks.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if priority_filter %}&priority={{ priority_filter }}{% endif %}{% if due_date_filter %}&due_date={{ due_date_filter }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ tasks.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if priority_filter %}&priority={{ priority_filter }}{% endif %}{% if due_date_filter %}&due_date={{ due_date_filter }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            <div class="text-center text-muted small mt-2">
                {% trans "Showing" %} {{ tasks.start_index }} - {{ tasks.end_index }} {% trans "of" %} {{ tasks.paginator.count }} {% trans "tasks" %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content animate__animated animate__fadeInUp animate__faster">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteConfirmModalLabel">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>{% trans "Confirm Deletion" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0 me-3">
                        <i class="bi bi-trash text-danger" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <p class="mb-0">{% trans "Are you sure you want to delete this task? This action cannot be undone." %}</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>{% trans "Cancel" %}
                </button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">
                    <i class="bi bi-trash me-1"></i>{% trans "Delete" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                boundary: document.body
            });
        });

        // Delete confirmation modal
        function confirmDelete(url) {
            document.getElementById('confirmDeleteBtn').href = url;
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
            deleteModal.show();
        }

        // Make confirmDelete function available globally
        window.confirmDelete = confirmDelete;

        // Add hover effect to table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(13, 110, 253, 0.05)';
            });
            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });

        // Fix dropdown positioning
        document.querySelectorAll('.dropdown').forEach(dropdown => {
            dropdown.addEventListener('show.bs.dropdown', function () {
                const dropdownMenu = this.querySelector('.dropdown-menu');
                const tableContainer = document.querySelector('.table-responsive');

                // Reset any previous styles
                dropdownMenu.style.maxHeight = '';

                // After the dropdown is shown, check if it's visible
                setTimeout(() => {
                    const rect = dropdownMenu.getBoundingClientRect();
                    const tableRect = tableContainer.getBoundingClientRect();

                    // Check if dropdown extends beyond table bottom
                    if (rect.bottom > tableRect.bottom) {
                        const newMaxHeight = tableRect.bottom - rect.top - 10;
                        dropdownMenu.style.maxHeight = newMaxHeight + 'px';
                        dropdownMenu.style.overflowY = 'auto';
                    }

                    // Check if dropdown extends beyond right edge
                    if (rect.right > tableRect.right) {
                        dropdownMenu.classList.remove('dropdown-menu-start');
                        dropdownMenu.classList.add('dropdown-menu-end');
                    }

                    // Check if dropdown extends beyond left edge
                    if (rect.left < tableRect.left) {
                        dropdownMenu.classList.remove('dropdown-menu-end');
                        dropdownMenu.classList.add('dropdown-menu-start');
                    }
                }, 0);
            });
        });

        // Add animation to filter collapse
        const filterCollapse = document.getElementById('filterCollapse');
        if (filterCollapse) {
            filterCollapse.addEventListener('show.bs.collapse', function () {
                const chevron = document.querySelector('[data-bs-target="#filterCollapse"] i');
                if (chevron) {
                    chevron.classList.remove('bi-chevron-down');
                    chevron.classList.add('bi-chevron-up');
                }
            });

            filterCollapse.addEventListener('hide.bs.collapse', function () {
                const chevron = document.querySelector('[data-bs-target="#filterCollapse"] i');
                if (chevron) {
                    chevron.classList.remove('bi-chevron-up');
                    chevron.classList.add('bi-chevron-down');
                }
            });
        }
    });
</script>
{% endblock %}