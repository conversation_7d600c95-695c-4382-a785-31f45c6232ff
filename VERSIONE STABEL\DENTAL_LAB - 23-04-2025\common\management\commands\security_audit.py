"""
Management command for security auditing and monitoring
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.contrib.auth.models import Group
from datetime import datetime, timedelta
from common.audit import FinancialAuditService
from common.security import SecurityService
from accounts.models import CustomUser
import json

class Command(BaseCommand):
    help = 'Perform security audit and generate reports'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--failed-logins',
            action='store_true',
            help='Show recent failed login attempts',
        )
        parser.add_argument(
            '--security-events',
            action='store_true',
            help='Show recent security events',
        )
        parser.add_argument(
            '--user-activity',
            type=str,
            help='Show activity for specific user (email)',
        )
        parser.add_argument(
            '--compliance-report',
            action='store_true',
            help='Generate compliance report',
        )
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Number of days to look back (default: 7)',
        )
        parser.add_argument(
            '--export',
            type=str,
            help='Export report to file (JSON format)',
        )
        parser.add_argument(
            '--check-permissions',
            action='store_true',
            help='Check for users with excessive permissions',
        )
    
    def handle(self, *args, **options):
        if options['failed_logins']:
            self._show_failed_logins(options['days'])
        
        elif options['security_events']:
            self._show_security_events(options['days'])
        
        elif options['user_activity']:
            self._show_user_activity(options['user_activity'], options['days'])
        
        elif options['compliance_report']:
            self._generate_compliance_report(options['days'], options.get('export'))
        
        elif options['check_permissions']:
            self._check_permissions()
        
        else:
            self._show_security_summary(options['days'])
    
    def _show_failed_logins(self, days):
        """Show recent failed login attempts"""
        self.stdout.write(self.style.WARNING(f'🚨 Failed Login Attempts (Last {days} days)'))
        self.stdout.write('=' * 60)
        
        start_date = timezone.now() - timedelta(days=days)
        
        failed_logins = FinancialAuditService.get_audit_trail(
            operation_type='login_failed',
            start_date=start_date,
            limit=50
        )
        
        if not failed_logins:
            self.stdout.write(self.style.SUCCESS('✅ No failed login attempts found'))
            return
        
        for log in failed_logins:
            details = log.details
            email = details.get('email', 'Unknown')
            attempts = details.get('email_attempts', 0)
            ip_attempts = details.get('ip_attempts', 0)
            
            self.stdout.write(
                f'🔴 {log.timestamp.strftime("%Y-%m-%d %H:%M:%S")} - '
                f'{email} (IP: {log.ip_address})'
            )
            self.stdout.write(f'   Email attempts: {attempts}, IP attempts: {ip_attempts}')
    
    def _show_security_events(self, days):
        """Show recent security events"""
        self.stdout.write(self.style.WARNING(f'🔒 Security Events (Last {days} days)'))
        self.stdout.write('=' * 60)
        
        start_date = timezone.now() - timedelta(days=days)
        
        security_events = FinancialAuditService.get_audit_trail(
            operation_type='security_event',
            start_date=start_date,
            limit=50
        )
        
        if not security_events:
            self.stdout.write(self.style.SUCCESS('✅ No security events found'))
            return
        
        for log in security_events:
            details = log.details
            event_type = details.get('event_type', 'Unknown')
            user_name = log.user.get_full_name() if log.user else 'Anonymous'
            
            self.stdout.write(
                f'🔒 {log.timestamp.strftime("%Y-%m-%d %H:%M:%S")} - '
                f'{event_type} by {user_name} (IP: {log.ip_address})'
            )
    
    def _show_user_activity(self, email, days):
        """Show activity for specific user"""
        try:
            user = CustomUser.objects.get(email=email)
        except CustomUser.DoesNotExist:
            raise CommandError(f'User with email {email} not found')
        
        self.stdout.write(
            self.style.SUCCESS(f'👤 Activity for {user.get_full_name()} (Last {days} days)')
        )
        self.stdout.write('=' * 60)
        
        activity = FinancialAuditService.get_user_activity_summary(user, days)
        
        self.stdout.write(f'Total Actions: {activity["total_actions"]}')
        self.stdout.write('\n📊 Activity by Type:')
        
        for op_type, actions in activity['activity_by_type'].items():
            self.stdout.write(f'  • {op_type.replace("_", " ").title()}: {len(actions)} actions')
        
        self.stdout.write('\n📝 Recent Activity:')
        for action in activity['recent_activity']:
            self.stdout.write(
                f'  • {action["timestamp"].strftime("%Y-%m-%d %H:%M")} - '
                f'{action["operation"].replace("_", " ").title()} on {action["object"]}'
            )
    
    def _generate_compliance_report(self, days, export_file=None):
        """Generate compliance report"""
        self.stdout.write(self.style.SUCCESS(f'📋 Compliance Report (Last {days} days)'))
        self.stdout.write('=' * 60)
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        report = FinancialAuditService.generate_compliance_report(start_date, end_date)
        
        # Display summary
        self.stdout.write(f'Period: {start_date.date()} to {end_date.date()}')
        self.stdout.write(f'Total Operations: {report["total_operations"]}')
        
        self.stdout.write('\n📊 Operations by Type:')
        for op_type, count in report['operation_counts'].items():
            self.stdout.write(f'  • {op_type.replace("_", " ").title()}: {count}')
        
        self.stdout.write('\n🔒 Security Summary:')
        security = report['security_summary']
        self.stdout.write(f'  • Security Events: {security["security_events"]}')
        self.stdout.write(f'  • Failed Logins: {security["failed_logins"]}')
        self.stdout.write(f'  • Permission Denials: {security["permission_denials"]}')
        
        self.stdout.write('\n👥 Top Active Users:')
        for user_activity in report['user_activity'][:10]:
            username = user_activity['user__username'] or 'Anonymous'
            count = user_activity['count']
            self.stdout.write(f'  • {username}: {count} actions')
        
        # Export if requested
        if export_file:
            with open(export_file, 'w') as f:
                # Convert datetime objects to strings for JSON serialization
                report_copy = report.copy()
                report_copy['period']['start_date'] = start_date.isoformat()
                report_copy['period']['end_date'] = end_date.isoformat()
                report_copy['generated_at'] = report_copy['generated_at'].isoformat()
                
                json.dump(report_copy, f, indent=2, default=str)
            
            self.stdout.write(
                self.style.SUCCESS(f'\n💾 Report exported to {export_file}')
            )
    
    def _check_permissions(self):
        """Check for users with excessive permissions"""
        self.stdout.write(self.style.WARNING('🔍 Checking User Permissions'))
        self.stdout.write('=' * 60)
        
        # Check for users with superuser status
        superusers = CustomUser.objects.filter(is_superuser=True, is_active=True)
        self.stdout.write(f'🔴 Superusers: {superusers.count()}')
        for user in superusers:
            self.stdout.write(f'  • {user.get_full_name()} ({user.email})')
        
        # Check for users in multiple high-privilege groups
        high_privilege_groups = ['super_admin', 'lab_manager']
        users_with_multiple_roles = []
        
        for user in CustomUser.objects.filter(is_active=True):
            user_groups = [g.name for g in user.groups.all()]
            high_privilege_count = sum(1 for group in user_groups if group in high_privilege_groups)
            
            if high_privilege_count > 1:
                users_with_multiple_roles.append((user, user_groups))
        
        if users_with_multiple_roles:
            self.stdout.write(f'\n⚠️  Users with Multiple High-Privilege Roles: {len(users_with_multiple_roles)}')
            for user, groups in users_with_multiple_roles:
                self.stdout.write(f'  • {user.get_full_name()}: {", ".join(groups)}')
        else:
            self.stdout.write('\n✅ No users with excessive role assignments found')
        
        # Check for inactive users with permissions
        inactive_users_with_groups = CustomUser.objects.filter(
            is_active=False, 
            groups__isnull=False
        ).distinct()
        
        if inactive_users_with_groups.exists():
            self.stdout.write(f'\n⚠️  Inactive Users with Permissions: {inactive_users_with_groups.count()}')
            for user in inactive_users_with_groups:
                groups = [g.name for g in user.groups.all()]
                self.stdout.write(f'  • {user.get_full_name()}: {", ".join(groups)}')
        else:
            self.stdout.write('\n✅ No inactive users with permissions found')
    
    def _show_security_summary(self, days):
        """Show overall security summary"""
        self.stdout.write(self.style.SUCCESS(f'🔒 Security Summary (Last {days} days)'))
        self.stdout.write('=' * 60)
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # Get basic statistics
        total_logins = FinancialAuditService.get_audit_trail(
            operation_type='user_login',
            start_date=start_date
        ).count()
        
        failed_logins = FinancialAuditService.get_audit_trail(
            operation_type='login_failed',
            start_date=start_date
        ).count()
        
        security_events = FinancialAuditService.get_audit_trail(
            operation_type='security_event',
            start_date=start_date
        ).count()
        
        permission_denials = FinancialAuditService.get_audit_trail(
            operation_type='permission_denied',
            start_date=start_date
        ).count()
        
        # Display summary
        self.stdout.write(f'📊 Login Statistics:')
        self.stdout.write(f'  • Successful Logins: {total_logins}')
        self.stdout.write(f'  • Failed Logins: {failed_logins}')
        
        if total_logins > 0:
            failure_rate = (failed_logins / (total_logins + failed_logins)) * 100
            self.stdout.write(f'  • Failure Rate: {failure_rate:.1f}%')
        
        self.stdout.write(f'\n🔒 Security Events: {security_events}')
        self.stdout.write(f'🚫 Permission Denials: {permission_denials}')
        
        # Active users
        active_users = CustomUser.objects.filter(is_active=True).count()
        self.stdout.write(f'\n👥 Active Users: {active_users}')
        
        # Recent activity
        if failed_logins > 0:
            self.stdout.write(
                self.style.WARNING(f'\n⚠️  {failed_logins} failed login attempts detected')
            )
        
        if permission_denials > 0:
            self.stdout.write(
                self.style.WARNING(f'⚠️  {permission_denials} permission denials detected')
            )
        
        if failed_logins == 0 and permission_denials == 0:
            self.stdout.write(self.style.SUCCESS('\n✅ No security issues detected'))
